
'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTit<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  User,
  Bell,
  Phone,
  Shield,
  Database,
  Mic,
  Volume2,
  Save,
  Eye,
  EyeOff
} from 'lucide-react';
import {
  SETTINGS_PAGE_TITLE,
  SETTINGS_PAGE_SUBTITLE,
  SETTINGS_SAVE_CHANGES,
  SETTINGS_PROFILE_TITLE,
  SETTINGS_PROFILE_DEFAULT_NAME,
  SETTINGS_PROFILE_DEFAULT_EMAIL,
  SETTINGS_PROFILE_DEFAULT_PHONE,
  SETTINGS_PROFILE_ROLE,
  SETTINGS_PROFILE_CHANGE_PASSWORD,
  SETTINGS_PROFILE_CURRENT_PASSWORD,
  SETTINGS_PROFILE_NEW_PASSWORD,
  SETTINGS_NOTIFICATION_TITLE,
  SETTINGS_NOTIFICATION_APPOINTMENT,
  SETTINGS_NOTIFICATION_APPOINTMENT_DESC,
  SETTINGS_NOTIFICATION_AI_CALLS,
  SETTINGS_NOTIFICATION_AI_CALLS_DESC,
  SETTINGS_NOTIFICATION_REACTIVATION,
  SETTINGS_NOTIFICATION_REACTIVATION_DESC,
  SETTINGS_NOTIFICATION_SYSTEM,
  SETTINGS_NOTIFICATION_SYSTEM_DESC,
  SETTINGS_NOTIFICATION_DEFAULTS,
  SETTINGS_AI_AGENT_TITLE,
  SETTINGS_AI_AGENT_VOICE,
  SETTINGS_AI_AGENT_VOICE_DESC,
  SETTINGS_AI_AGENT_AUTO_REACTIVATION,
  SETTINGS_AI_AGENT_AUTO_REACTIVATION_DESC,
  SETTINGS_AI_AGENT_CALL_RECORDING,
  SETTINGS_AI_AGENT_CALL_RECORDING_DESC,
  SETTINGS_AI_AGENT_SMART_SCHEDULING,
  SETTINGS_AI_AGENT_SMART_SCHEDULING_DESC,
  SETTINGS_AI_AGENT_DEFAULTS,
  SETTINGS_AI_AGENT_VOICE_SETTINGS,
  SETTINGS_AI_AGENT_VOICE_SPEED,
  SETTINGS_AI_AGENT_VOICE_VOLUME,
  SETTINGS_SYSTEM_INFO_VERSION,
  SETTINGS_SYSTEM_INFO_VERSION_VALUE,
  SETTINGS_SYSTEM_INFO_LAST_UPDATE,
  SETTINGS_SYSTEM_INFO_LAST_UPDATE_VALUE,
  SETTINGS_SYSTEM_INFO_DB_SIZE,
  SETTINGS_SYSTEM_INFO_DB_SIZE_VALUE,
  SETTINGS_SYSTEM_INFO_ACTIVE_USERS,
  SETTINGS_SYSTEM_INFO_ACTIVE_USERS_VALUE,
  SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS,
  SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS_VALUE,
  SETTINGS_SYSTEM_INFO_BACKUP_STATUS,
  SETTINGS_SYSTEM_INFO_BACKUP_STATUS_VALUE,
  SETTINGS_SYSTEM_INFO_SECURITY_SETTINGS,
  SETTINGS_SYSTEM_INFO_BACKUP_NOW
} from '@/Constants/Settings';

import PageSection from '@/components/CommonComponents/PageSection';
const Settings = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [notifications, setNotifications] = useState({ ...SETTINGS_NOTIFICATION_DEFAULTS });
  const [aiSettings, setAiSettings] = useState({ ...SETTINGS_AI_AGENT_DEFAULTS });

  // Track checked state for each day


  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">{SETTINGS_PAGE_TITLE}</h2>
          <p className="text-gray-600">{SETTINGS_PAGE_SUBTITLE}</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Save className="h-4 w-4 mr-2" />
          {SETTINGS_SAVE_CHANGES}
        </Button>
      </div>

      {/* Profile Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2 text-blue-600" />
            {SETTINGS_PROFILE_TITLE}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Full Name</label>
              <Input defaultValue={SETTINGS_PROFILE_DEFAULT_NAME} className="border-gray-200" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Email</label>
              <Input defaultValue={SETTINGS_PROFILE_DEFAULT_EMAIL} className="border-gray-200" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Phone</label>
              <Input defaultValue={SETTINGS_PROFILE_DEFAULT_PHONE} className="border-gray-200" />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Role</label>
              <div className="mt-2">
                <Badge variant="outline" className="border-gray-200">{SETTINGS_PROFILE_ROLE}</Badge>
              </div>
            </div>
          </div>
          <div className="pt-4 border-t border-gray-200">
            <h4 className="font-medium text-gray-900 mb-4">{SETTINGS_PROFILE_CHANGE_PASSWORD}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">{SETTINGS_PROFILE_CURRENT_PASSWORD}</label>
                <div className="relative">
                  <Input 
                    type={showPassword ? "text" : "password"} 
                    placeholder="Enter current password"
                    className="border-gray-200"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">{SETTINGS_PROFILE_NEW_PASSWORD}</label>
                <Input type="password" placeholder="Enter new password" className="border-gray-200" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Bell className="h-5 w-5 mr-2 text-green-600" />
            {SETTINGS_NOTIFICATION_TITLE}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">{SETTINGS_NOTIFICATION_APPOINTMENT}</p>
              <p className="text-sm text-gray-600">{SETTINGS_NOTIFICATION_APPOINTMENT_DESC}</p>
            </div>
            <Switch 
              checked={notifications.appointments}
              onCheckedChange={(checked) => setNotifications({...notifications, appointments: checked})}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">{SETTINGS_NOTIFICATION_AI_CALLS}</p>
              <p className="text-sm text-gray-600">{SETTINGS_NOTIFICATION_AI_CALLS_DESC}</p>
            </div>
            <Switch 
              checked={notifications.aiCalls}
              onCheckedChange={(checked) => setNotifications({...notifications, aiCalls: checked})}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">{SETTINGS_NOTIFICATION_REACTIVATION}</p>
              <p className="text-sm text-gray-600">{SETTINGS_NOTIFICATION_REACTIVATION_DESC}</p>
            </div>
            <Switch 
              checked={notifications.reactivation}
              onCheckedChange={(checked) => setNotifications({...notifications, reactivation: checked})}
            />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">{SETTINGS_NOTIFICATION_SYSTEM}</p>
              <p className="text-sm text-gray-600">{SETTINGS_NOTIFICATION_SYSTEM_DESC}</p>
            </div>
            <Switch 
              checked={notifications.system}
              onCheckedChange={(checked) => setNotifications({...notifications, system: checked})}
            />
          </div>
        </CardContent>
      </Card>

      {/* AI Agent Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Phone className="h-5 w-5 mr-2 text-purple-600" />
            {SETTINGS_AI_AGENT_TITLE}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{SETTINGS_AI_AGENT_VOICE}</p>
                  <p className="text-sm text-gray-600">{SETTINGS_AI_AGENT_VOICE_DESC}</p>
                </div>
                <Switch 
                  checked={aiSettings.voiceEnabled}
                  onCheckedChange={(checked) => setAiSettings({...aiSettings, voiceEnabled: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{SETTINGS_AI_AGENT_AUTO_REACTIVATION}</p>
                  <p className="text-sm text-gray-600">{SETTINGS_AI_AGENT_AUTO_REACTIVATION_DESC}</p>
                </div>
                <Switch 
                  checked={aiSettings.autoReactivation}
                  onCheckedChange={(checked) => setAiSettings({...aiSettings, autoReactivation: checked})}
                />
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{SETTINGS_AI_AGENT_CALL_RECORDING}</p>
                  <p className="text-sm text-gray-600">{SETTINGS_AI_AGENT_CALL_RECORDING_DESC}</p>
                </div>
                <Switch 
                  checked={aiSettings.callRecording}
                  onCheckedChange={(checked) => setAiSettings({...aiSettings, callRecording: checked})}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{SETTINGS_AI_AGENT_SMART_SCHEDULING}</p>
                  <p className="text-sm text-gray-600">{SETTINGS_AI_AGENT_SMART_SCHEDULING_DESC}</p>
                </div>
                <Switch 
                  checked={aiSettings.smartScheduling}
                  onCheckedChange={(checked) => setAiSettings({...aiSettings, smartScheduling: checked})}
                />
              </div>
            </div>
          </div>
          
          <div className="pt-4 border-t border-gray-200">
            <h4 className="font-medium text-gray-900 mb-4">{SETTINGS_AI_AGENT_VOICE_SETTINGS}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">{SETTINGS_AI_AGENT_VOICE_SPEED}</label>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">Slow</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full w-2/3" />
                  </div>
                  <span className="text-sm text-gray-600">Fast</span>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">{SETTINGS_AI_AGENT_VOICE_VOLUME}</label>
                <div className="flex items-center space-x-4">
                  <Volume2 className="h-4 w-4 text-gray-400" />
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full w-3/4" />
                  </div>
                  <Mic className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Info Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-gray-700" />
            System Info
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_VERSION}</span>
                <Badge variant="outline" className="bg-gray-200 text-gray-900 border-gray-200">{SETTINGS_SYSTEM_INFO_VERSION_VALUE}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_LAST_UPDATE}</span>
                <span className="font-medium">{SETTINGS_SYSTEM_INFO_LAST_UPDATE_VALUE}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_DB_SIZE}</span>
                <span className="font-medium">{SETTINGS_SYSTEM_INFO_DB_SIZE_VALUE}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_ACTIVE_USERS}</span>
                <span className="font-medium">{SETTINGS_SYSTEM_INFO_ACTIVE_USERS_VALUE}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS}</span>
                <Badge variant="default" className="bg-black text-white border-black">{SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS_VALUE}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{SETTINGS_SYSTEM_INFO_BACKUP_STATUS}</span>
                <Badge variant="secondary" className="bg-gray-200 text-gray-900 border-gray-200">{SETTINGS_SYSTEM_INFO_BACKUP_STATUS_VALUE}</Badge>
              </div>
            </div>
          </div>
          <div className="flex gap-4 pt-4 border-t border-gray-200 mt-2">
            <Button variant="outline" size="sm" className="border-gray-200">
              <Shield className="h-4 w-4 mr-2" />
              {SETTINGS_SYSTEM_INFO_SECURITY_SETTINGS}
            </Button>
            <Button variant="outline" size="sm" className="border-gray-200">
              <Database className="h-4 w-4 mr-2" />
              {SETTINGS_SYSTEM_INFO_BACKUP_NOW}
            </Button>
          </div>
        </CardContent>
      </Card>
    </PageSection>
  );
};

export default Settings;
