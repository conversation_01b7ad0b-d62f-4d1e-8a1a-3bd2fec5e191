export const CHANGE_PASSWORD_CONSTANTS = {
  // Page Titles and Headers
  PAGE_TITLE: "Change Password",
  PAGE_SUBTITLE: "Update your account password to keep it secure.",
  SECURITY_SETTINGS_TITLE: "Security Settings",
  SUB<PERSON>TL<PERSON>: "Enter your current password and new password below.",
  
  // Form Labels
  CURRENT_PASSWORD_LABEL: "Current Password",
  NEW_PASSWORD_LABEL: "New Password",
  CONFIRM_PASSWORD_LABEL: "Confirm New Password",
  
  // Form Placeholders
  CURRENT_PASSWORD_PLACEHOLDER: "••••••••",
  NEW_PASSWORD_PLACEHOLDER: "••••••••",
  CONFIRM_PASSWORD_PLACEHOLDER: "••••••••",
  
  // Button Text
  CHANGE_PASSWORD: "Change Password",
  CANCEL: "Cancel",
  LOADING: "Changing...",
  
  // Status Messages
  SUCCESS: "Password changed successfully! Redirecting...",
  
  // Password Requirements
  PASSWORD_REQUIREMENTS_TITLE: "Password Requirements:",
  REQUIREMENT_LENGTH: "At least 8 characters",
  REQUIREMENT_UPPERCASE: "One uppercase letter (A-Z)",
  REQUIREMENT_LOWERCASE: "One lowercase letter (a-z)",
  REQUIREMENT_NUMBER: "One number (0-9)",
  REQUIREMENT_SPECIAL: "One special character (!@#$%^&*)",
  
  // Icons
  CHECK_ICON: "✓",
  CROSS_ICON: "✗",
} as const;

export const VALIDATION_MESSAGES = {
  CURRENT_PASSWORD_REQUIRED: "Current password is required",
  NEW_PASSWORD_REQUIRED: "New password is required",
  CONFIRM_PASSWORD_REQUIRED: "Please confirm your password",
  PASSWORDS_MATCH: "Passwords must match",
  PASSWORD_MIN_LENGTH: "Password must be at least 8 characters",
  PASSWORD_UPPERCASE: "Password must contain at least one uppercase letter (A-Z)",
  PASSWORD_LOWERCASE: "Password must contain at least one lowercase letter (a-z)",
  PASSWORD_NUMBER: "Password must contain at least one number (0-9)",
  PASSWORD_SPECIAL: "Password must contain at least one special character (!@#$%^&*)",
  PASSWORD_PATTERN: "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
} as const;

export const TOAST_MESSAGES = {
  CHANGE_SUCCESS: "Password changed successfully! You can now use your new password.",
  CHANGE_FAILED: "Password change failed",
  INTERNAL_SERVER_ERROR: "Internal server error",
} as const;

export const FORM_FIELDS = {
  CURRENT_PASSWORD: "currentPassword",
  NEW_PASSWORD: "newPassword",
  CONFIRM_PASSWORD: "confirmPassword",
} as const;

export const FORM_IDS = {
  CURRENT_PASSWORD: "currentPassword",
  NEW_PASSWORD: "newPassword",
  CONFIRM_PASSWORD: "confirmPassword",
} as const;

export const ACCESSIBILITY = {
  HIDE_PASSWORD: "Hide password",
  SHOW_PASSWORD: "Show password",
  DENSY_AI_LOGO: "Densy AI Logo",
  CHANGE_PASSWORD: "Change password",
  CURRENT_PASSWORD_INPUT: "Current password input",
  NEW_PASSWORD_INPUT: "New password input",
  CONFIRM_PASSWORD_INPUT: "Confirm password input",
} as const;

export const ASSETS = {
  LOGO_SRC: "/Densy%20AI.svg",
  LOGO_WIDTH: 150,
  LOGO_HEIGHT: 40,
} as const;

export const ANIMATION = {
  INITIAL_OPACITY: 0,
  INITIAL_SCALE: 0.96,
  FINAL_OPACITY: 1,
  FINAL_SCALE: 1,
  DURATION: 0.5,
} as const;

export const INPUT_TYPES = {
  PASSWORD: "password",
  TEXT: "text",
} as const;

export const BUTTON_TYPES = {
  BUTTON: "button",
  SUBMIT: "submit",
} as const;

export const FORM_ATTRIBUTES = {
  CURRENT_PASSWORD_AUTOCOMPLETE: "current-password",
  NEW_PASSWORD_AUTOCOMPLETE: "new-password",
  CONFIRM_PASSWORD_AUTOCOMPLETE: "new-password",
} as const;

export const ROUTES = {
  DASHBOARD: "/dashboard",
} as const;

export const ICONS = {
  SHIELD: "h-8 w-8 text-blue-600",
  LOCK: "18",
  EYE: "18",
  EYE_OFF: "18",
  LOADER: "18",
} as const;

export const CSS_CLASSES = {
  // Page Layout
  PAGE_CONTAINER: "px-4 py-8",
  PAGE_HEADER: "mb-8",
  PAGE_TITLE: "text-3xl font-bold text-gray-900 mb-2",
  PAGE_SUBTITLE: "text-gray-600",
  
  // Card
  CHANGE_CARD: "changeCard",
  CHANGE_HEADER: "changeHeader",
  CHANGE_TITLE: "changeTitle",
  CHANGE_SUBTITLE: "changeSubtitle",
  
  // Form
  CHANGE_FORM: "changeForm",
  FORM_FIELD: "formField",
  FORM_LABEL: "formLabel",
  CHANGE_INPUT: "changeInput",
  CHANGE_ERROR: "changeError",
  
  // Icons
  CHANGE_ICON: "changeIcon",
  CHANGE_ICON_LEFT: "changeIconLeft",
  CHANGE_PASSWORD_TOGGLE: "changePasswordToggle",
  
  // Buttons
  CHANGE_BUTTON: "changeButton",
  
  // Password Strength
  PASSWORD_STRENGTH: "passwordStrength",
  PASSWORD_STRENGTH_TITLE: "passwordStrengthTitle",
  PASSWORD_CHECKS: "passwordChecks",
  PASSWORD_CHECK: "passwordCheck",
  CHECK_ICON: "checkIcon",
  VALID: "valid",
  INVALID: "invalid",
  
  // Layout
  FLEX_CONTAINER: "flex gap-4 mt-6",
  FLEX_ITEM: "flex-1",
  ICON_CONTAINER: "p-3 bg-blue-100 rounded-full mr-3",
  ICON_WRAPPER: "flex items-center mb-4",
  BUTTON_WRAPPER: "flex items-center justify-center gap-2",
} as const;

export const ERROR_MESSAGES = {
  UPDATE_FAILED: "Failed to update password",
  INVALID_TOKEN: "Invalid reset token. Please request a new password reset.",
} as const;

export const SUCCESS_MESSAGES = {
  PASSWORD_UPDATED: "Password updated successfully",
} as const; 