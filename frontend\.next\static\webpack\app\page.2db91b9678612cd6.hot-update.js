"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/reactivation-program/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/reactivation-program/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsGrid */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsGrid.tsx\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsTable */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsTable.tsx\");\n/* harmony import */ var _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Constants/ReactivationProgram */ \"(app-pages-browser)/./src/Constants/ReactivationProgram.ts\");\n/* harmony import */ var _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useReactivationManagement */ \"(app-pages-browser)/./src/hooks/useReactivationManagement.ts\");\n/* harmony import */ var _components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CommonComponents/PageSection */ \"(app-pages-browser)/./src/components/CommonComponents/PageSection.tsx\");\n/* harmony import */ var _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useAppointment */ \"(app-pages-browser)/./src/hooks/useAppointment.ts\");\n/* harmony import */ var _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAICallLog */ \"(app-pages-browser)/./src/hooks/useAICallLog.ts\");\n/* harmony import */ var _components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ReactivationProgram/AddReactivationForm */ \"(app-pages-browser)/./src/components/ReactivationProgram/AddReactivationForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReactivationProgram = ()=>{\n    _s();\n    const [isAddReactivationOpen, setIsAddReactivationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGNS_DATA);\n    // View state\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"campaigns\");\n    const { aiCallLogs, getAllAICallLogs } = (0,_hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__.useAICallLog)();\n    const { appointments, getAllAppointments } = (0,_hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__.useAppointment)();\n    const { summary, reactivations, error, getReactivationSummary, getReactivationsByClinic } = (0,_hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__.useReactivationManagement)();\n    const handleAddCampaign = (data)=>{\n        setCampaigns([\n            ...campaigns,\n            data\n        ]);\n    };\n    // Handle clinic selection\n    const handleClinicSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleClinicSelect]\": async (clinicId)=>{\n            setSelectedClinicId(clinicId);\n            setSelectedPatientIds([]);\n            setBatchCallResults(null);\n            setShowResults(false);\n            // Fetch both patients and reactivation summary\n            const [patientsResult, summaryResult] = await Promise.all([\n                getPatientsByClinic(clinicId),\n                getReactivationSummary(clinicId)\n            ]);\n            if (!patientsResult.success) {\n                console.error(\"Failed to fetch patients:\", patientsResult.error);\n            }\n            if (!summaryResult.success) {\n                console.error(\"Failed to fetch reactivation summary:\", summaryResult.error);\n            }\n        }\n    }[\"ReactivationProgram.useCallback[handleClinicSelect]\"], [\n        getPatientsByClinic,\n        getReactivationSummary\n    ]);\n    // Handle patient selection change\n    const handlePatientSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handlePatientSelectionChange]\": (patientIds)=>{\n            setSelectedPatientIds(patientIds);\n        }\n    }[\"ReactivationProgram.useCallback[handlePatientSelectionChange]\"], []);\n    // Handle batch call submission\n    const handleBatchCallSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleBatchCallSubmit]\": async (time)=>{\n            if (!selectedClinicId || selectedPatientIds.length === 0) {\n                return;\n            }\n            const result = await submitBatchCall(selectedClinicId, selectedPatientIds, time);\n            if (result.success && result.data) {\n                setBatchCallResults(result.data);\n                setShowResults(true);\n            }\n        }\n    }[\"ReactivationProgram.useCallback[handleBatchCallSubmit]\"], [\n        selectedClinicId,\n        selectedPatientIds,\n        submitBatchCall\n    ]);\n    // Clear results and reset\n    const handleCloseResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleCloseResults]\": ()=>{\n            setShowResults(false);\n            setBatchCallResults(null);\n            setSelectedPatientIds([]);\n            clearPatients();\n        }\n    }[\"ReactivationProgram.useCallback[handleCloseResults]\"], [\n        clearPatients\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAppointments();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAppointments\n    ]);\n    // Fetch reactivations when stats tab is active and clinic is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Fetching data for stats tab, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error fetching stats data:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        activeView,\n        selectedClinicId,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAICallLogs();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAICallLogs\n    ]);\n    const today = new Date().toISOString().split(\"T\")[0]; // \"2025-09-15\" format\n    const appointmentCount = Array.isArray(appointments) ? appointments.filter((appointment)=>appointment.appointment_date === today).length : 0;\n    // Fetch data when component mounts if clinic is already selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Initial data fetch for stats, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error in initial stats fetch:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        selectedClinicId,\n        activeView,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_TITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_SUBTITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"main\",\n                        onClick: ()=>setIsAddReactivationOpen(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_ADD_NEW\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                appointments: appointmentCount,\n                calls: (aiCallLogs === null || aiCallLogs === void 0 ? void 0 : aiCallLogs.length) || 0,\n                successRate: \"\".concat((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, \"%\"),\n                newBookings: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"campaigns\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"campaigns\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Campaigns\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"patients\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"patients\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Patient Management\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: async ()=>{\n                                setActiveView(\"stats\");\n                                // Fetch data immediately when stats tab is clicked\n                                if (selectedClinicId) {\n                                    await Promise.all([\n                                        getReactivationSummary(selectedClinicId),\n                                        getReactivationsByClinic(selectedClinicId)\n                                    ]);\n                                }\n                            },\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"stats\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Call Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"campaigns\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_CAMPAIGNS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_TITLE\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_DESCRIPTION\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"patients\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PATIENT_MANAGEMENT_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: \"Patient Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: 'Patient management functionality has been moved to the Add Reactivation Program form. Click the \"Add New Program\" button to access patient management features.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 278,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"stats\" && // <></>\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedClinicId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600\",\n                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.LOADING_STATISTICS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 17\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.ERROR_LOADING_STATISTICS,\n                                                \" \",\n                                                error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 17\n                            }, undefined),\n                            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_TOTAL_CAMPAIGNS\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 344,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_campaigns) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 347,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: [\n                                                                            (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGN_STATUS_BREAKDOWN_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_ACTIVE_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 402,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.active_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_COMPLETED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_FAILED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-red-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.failed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PERFORMANCE_METRICS_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-purple-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 445,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: [\n                                                                                (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_CAMPAIGN_EFFICIENCY\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: [\n                                                                                summary && summary.total_campaigns > 0 ? Math.round((summary.total_patients_contacted || 0) / summary.total_campaigns) : 0,\n                                                                                \" \",\n                                                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_PER_CAMPAIGN\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        reactivations: reactivations || [],\n                                        loading: loading\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_SELECT_CLINIC\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAddReactivationOpen,\n                onClose: ()=>setIsAddReactivationOpen(false),\n                onSubmit: handleAddCampaign\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: '<elevenlabs-convai agent-id=\"agent_01jybn5qtwfnd8twmvjffcb0h3\"></elevenlabs-convai>'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReactivationProgram, \"XhXAUGBYvkDLCNSSmLss5FLD+t0=\", false, function() {\n    return [\n        _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__.useAICallLog,\n        _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__.useAppointment,\n        _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__.useReactivationManagement\n    ];\n});\n_c = ReactivationProgram;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactivationProgram);\nvar _c;\n$RefreshReg$(_c, \"ReactivationProgram\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reactivation-program/page.tsx\n"));

/***/ })

});