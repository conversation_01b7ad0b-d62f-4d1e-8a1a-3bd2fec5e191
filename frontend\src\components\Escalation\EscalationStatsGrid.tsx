import React from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { Alert<PERSON>riangle, CheckCircle, Clock, Users } from "lucide-react";
import {
  ESCALATION_STAT_TOTAL,
  ESCALATION_STAT_OPEN,
  ESCALATION_STAT_RESOLVED,
  ESCALATION_STAT_PENDING,
} from "@/Constants/Escalation";

interface EscalationStatsGridProps {
  stats: {
    total: number;
    open: number;
    resolved: number;
    pending: number;
  };
}

const EscalationStatsGrid: React.FC<EscalationStatsGridProps> = ({ stats }) => {
  const statCards = [
    {
      title: ESCALATION_STAT_TOTAL,
      value: stats.total,
      icon: <Users className="h-6 w-6 text-blue-600" />,
    },
    {
      title: ESCALATION_STAT_OPEN,
      value: stats.open,
      icon: <AlertTriangle className="h-6 w-6 text-red-600" />,
    },
    {
      title: ESCALATION_STAT_PENDING,
      value: stats.pending,
      icon: <Clock className="h-6 w-6 text-yellow-600" />,
    },
    {
      title: ESCALATION_STAT_RESOLVED,
      value: stats.resolved,
      icon: <CheckCircle className="h-6 w-6 text-green-600" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {statCards.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          icon={stat.icon}
        />
      ))}
    </div>
  );
};

export default EscalationStatsGrid; 