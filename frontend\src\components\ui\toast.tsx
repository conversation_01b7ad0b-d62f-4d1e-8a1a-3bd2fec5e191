import * as React from "react";
import * as ToastPrimitives from "@radix-ui/react-toast";
import { cva, type VariantProps } from "class-variance-authority";
import {
  X,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  Loader2,
  Bell,
  Shield,
  Star,
  Heart,
  Zap,
  Clock,
} from "lucide-react";
import { cn } from "@/lib/utils";

const TOAST_ICONS = {
  default: Bell,
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
  loading: Loader2,
  destructive: AlertCircle,
  security: Shield,
  premium: Star,
  love: Heart,
  power: Zap,
  time: Clock,
  compact: CheckCircle,
  "compact-error": AlertCircle,
} as const;

const ToastProvider = ToastPrimitives.Provider;

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",
      className
    )}
    {...props}
  />
));
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center space-x-4 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        default: "border bg-background text-foreground",
        success: "border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",
        error: "border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",
        warning: "border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100",
        info: "border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100",
        loading: "border-gray-200 bg-gray-50 text-gray-900 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-100",
        destructive: "destructive group border-destructive bg-destructive text-destructive-foreground",
        security: "border-purple-200 bg-purple-50 text-purple-900 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-100",
        premium: "border-amber-200 bg-amber-50 text-amber-900 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-100",
        love: "border-pink-200 bg-pink-50 text-pink-900 dark:border-pink-800 dark:bg-pink-950 dark:text-pink-100",
        power: "border-orange-200 bg-orange-50 text-orange-900 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-100",
        time: "border-indigo-200 bg-indigo-50 text-indigo-900 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-100",
        compact: "border-teal-200 bg-teal-50 text-teal-800 dark:border-teal-800 dark:bg-teal-950 dark:text-teal-100 p-2 pr-3",
        "compact-error": "border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-100 p-2 pr-3",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

interface ToastProps
  extends React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root>,
    VariantProps<typeof toastVariants> {
  icon?: React.ReactNode;
  showIcon?: boolean;
}

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  ToastProps
>(({ className, variant, icon, showIcon = true, children, ...props }, ref) => {
  const IconComponent = variant && TOAST_ICONS[variant];
  const shouldShowIcon = showIcon && (icon || IconComponent);
  const isAnimated = variant === "loading";

  return (
    <ToastPrimitives.Root
      ref={ref}
      className={cn(toastVariants({ variant }), className)}
      {...props}
    >
      {shouldShowIcon && (
        <div className="flex-shrink-0">
          {icon ||
            (IconComponent && (
              <IconComponent
                className={cn(
                  variant === "compact" ? "h-4 w-4" : "h-5 w-5",
                  isAnimated && "animate-spin",
                  variant === "success" && "text-green-600 dark:text-green-400",
                  variant === "error" && "text-red-600 dark:text-red-400",
                  variant === "warning" && "text-yellow-600 dark:text-yellow-400",
                  variant === "info" && "text-blue-600 dark:text-blue-400",
                  variant === "loading" && "text-gray-600 dark:text-gray-400",
                  variant === "security" && "text-purple-600 dark:text-purple-400",
                  variant === "premium" && "text-amber-600 dark:text-amber-400",
                  variant === "love" && "text-pink-600 dark:text-pink-400",
                  variant === "power" && "text-orange-600 dark:text-orange-400",
                  variant === "time" && "text-indigo-600 dark:text-indigo-400",
                  variant === "compact" && "text-teal-600 dark:text-teal-400",
                  variant === "compact-error" && "text-red-600 dark:text-red-400"
                )}
              />
            ))}
        </div>
      )}
      <div className={cn("flex-1", variant === "compact" ? "space-y-0.5" : "space-y-1")}>{children}</div>
    </ToastPrimitives.Root>
  );
});
Toast.displayName = ToastPrimitives.Root.displayName;

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      className
    )}
    {...props}
  />
));
ToastAction.displayName = ToastPrimitives.Action.displayName;

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      className
    )}
    toast-close=""
    {...props}
  >
    <X className="h-4 w-4" />
  </ToastPrimitives.Close>
));
ToastClose.displayName = ToastPrimitives.Close.displayName;

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title
    ref={ref}
    className={cn("text-sm font-semibold [&+div]:mt-0.5", className)}
    {...props}
  />
));
ToastTitle.displayName = ToastPrimitives.Title.displayName;

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Description
    ref={ref}
    className={cn("text-sm opacity-90 [&+div]:mt-0.5", className)}
    {...props}
  />
));
ToastDescription.displayName = ToastPrimitives.Description.displayName;

interface ToastProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number;
  max?: number;
  variant?: "default" | "success" | "error" | "warning" | "info";
}

const ToastProgress = React.forwardRef<HTMLDivElement, ToastProgressProps>(
  ({ className, value = 0, max = 100, variant = "default", ...props }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    return (
      <div
        ref={ref}
        className={cn(
          "h-1 w-full bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700",
          className
        )}
        {...props}
      >
        <div
          className={cn(
            "h-full transition-all duration-300 ease-out",
            variant === "success" && "bg-green-500",
            variant === "error" && "bg-red-500",
            variant === "warning" && "bg-yellow-500",
            variant === "info" && "bg-blue-500",
            variant === "default" && "bg-gray-500"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    );
  }
);
ToastProgress.displayName = "ToastProgress";

interface ToastBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: "default" | "success" | "error" | "warning" | "info" | "secondary";
}

const ToastBadge = React.forwardRef<HTMLSpanElement, ToastBadgeProps>(
  ({ className, variant = "default", children, ...props }, ref) => {
    return (
      <span
        ref={ref}
        className={cn(
          "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
          variant === "default" && "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
          variant === "success" && "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200",
          variant === "error" && "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200",
          variant === "warning" && "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200",
          variant === "info" && "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200",
          variant === "secondary" && "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400",
          className
        )}
        {...props}
      >
        {children}
      </span>
    );
  }
);
ToastBadge.displayName = "ToastBadge";

type ToastPropsType = React.ComponentPropsWithoutRef<typeof Toast>;
type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
  type ToastPropsType as ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
  ToastProgress,
  ToastBadge,
  TOAST_ICONS,
};
