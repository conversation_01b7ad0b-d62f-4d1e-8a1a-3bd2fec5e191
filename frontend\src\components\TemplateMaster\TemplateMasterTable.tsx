import React, { useState } from "react";
import Table from "@/components/CommonComponents/Table";
import Pagination from "@/components/CommonComponents/Pagination";
import { Edit, Trash2, Eye } from "lucide-react";
import { templateTableColumns } from '@/utils/column';
import ConfirmDeleteDialog from '@/components/CommonComponents/ConfirmDeleteDialog';

interface Template {
  id: number;
  name: string;
  type: string;
  content: string;
  created_at?: string;
}

interface TemplateMasterTableProps {
  templates?: Template[];
  loading?: boolean;
  page?: number;
  pageSize?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  onViewTemplate?: (template: Template) => void;
  onEditTemplate?: (template: Template) => void;
  onDeleteTemplate?: (template: Template) => void;
  variant?: 'striped' | 'hover' | 'bordered';
}

const TemplateMasterTable: React.FC<TemplateMasterTableProps> = ({
  templates = [],
  loading = false,
  page = 1,
  pageSize = 10,
  total = 0,
  onPageChange,
  onViewTemplate,
  onEditTemplate,
  onDeleteTemplate,
  variant = 'striped',
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<Template | null>(null);

  const getTypeBadge = (type: string) => {
    const typeColors = {
      sms: "bg-blue-100 text-blue-800",
      email: "bg-purple-100 text-purple-800",
      call_prompt: "bg-orange-100 text-orange-800",
    };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeColors[type as keyof typeof typeColors] || "bg-gray-100 text-gray-800"}`}>
        {type.toUpperCase()}
      </span>
    );
  };

  // Create custom columns with type badge rendering
  const customColumns = [
    ...templateTableColumns.slice(0, 1), // Template Name
    {
      ...templateTableColumns[1], // Type
      render: (row: Record<string, unknown>) => getTypeBadge(String(row.type))
    },
    ...templateTableColumns.slice(2, 3), // Content
    ...templateTableColumns.slice(3) // Created Date
  ];

    const handleActions = (row: Record<string, unknown>) => {
    const template = row as unknown as Template;
    return (
      <div className="flex gap-3 items-center">
        <button
          className="text-black"
          onClick={() => onViewTemplate && onViewTemplate(template)}
          title="View"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Eye className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => onEditTemplate && onEditTemplate(template)}
          title="Edit"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Edit className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => { setTemplateToDelete(template); setDeleteDialogOpen(true); }}
          title="Delete"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div>
      <Table
        columns={customColumns}
        data={templates as unknown as Record<string, unknown>[]}  
        variant={variant}
        onActions={handleActions}
      />
      {onPageChange && (
        <div className="mt-4 flex justify-end">
          <Pagination
            currentPage={page}
            totalPages={Math.ceil(total / pageSize)}
            onPageChange={onPageChange}
          />
        </div>
      )}
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Template"
        description={<span>Are you sure you want to delete <b>{templateToDelete ? templateToDelete.name : ''}</b>? This action cannot be undone.</span>}
        onConfirm={() => { if (onDeleteTemplate && templateToDelete) onDeleteTemplate(templateToDelete); setTemplateToDelete(null); }}
      />
    </div>
  );
};

export default TemplateMasterTable; 