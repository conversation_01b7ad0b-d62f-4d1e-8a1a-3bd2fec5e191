import React from "react";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter, X } from "lucide-react";
import SearchBar from "@/components/CommonComponents/SearchBar";
import {
  ESCALATION_FILTER_ALL,
  ESCALATION_STATUS_OPTIONS,
  ESCALATION_TAG_OPTIONS,
} from "@/Constants/Escalation";

interface EscalationFiltersProps {
  searchTerm: string;
  statusFilter: string;
  tagFilter: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onClearSearch: () => void;
  onStatusFilterChange: (value: string) => void;
  onTagFilterChange: (value: string) => void;
}

const EscalationFilters: React.FC<EscalationFiltersProps> = ({
  searchTerm,
  statusFilter,
  tagFilter,
  onSearchChange,
  onClearSearch,
  onStatusFilterChange,
  onTagFilterChange,
}) => {
  const hasActiveFilters = searchTerm || (statusFilter && statusFilter !== "all") || (tagFilter && tagFilter !== "all");

  return (
    <div className="space-y-4 mb-6">
             {/* Search and Filter Controls */}
       <div className="flex flex-col sm:flex-row gap-4">
         <div className="flex-1">
           <SearchBar
             placeholder="Search escalations..."
             value={searchTerm}
             onChange={onSearchChange}
           />
         </div>
        
        <div className="flex gap-2">
          <Select value={statusFilter || "all"} onValueChange={onStatusFilterChange}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{ESCALATION_FILTER_ALL}</SelectItem>
              {ESCALATION_STATUS_OPTIONS.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={tagFilter || "all"} onValueChange={onTagFilterChange}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by tag" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tags</SelectItem>
              {ESCALATION_TAG_OPTIONS.map((tag) => (
                <SelectItem key={tag.value} value={tag.value}>
                  {tag.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
                     {searchTerm && (
             <Badge variant="secondary" className="flex items-center gap-1">
               Search: {searchTerm}
               <button
                 onClick={onClearSearch}
                 className="ml-1 hover:text-red-600"
               >
                 <X className="h-3 w-3" />
               </button>
             </Badge>
           )}
          {statusFilter && statusFilter !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {ESCALATION_STATUS_OPTIONS.find(s => s.value === statusFilter)?.label}
              <button
                onClick={() => onStatusFilterChange("all")}
                className="ml-1 hover:text-red-600"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {tagFilter && tagFilter !== "all" && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Tag: {ESCALATION_TAG_OPTIONS.find(t => t.value === tagFilter)?.label}
              <button
                onClick={() => onTagFilterChange("all")}
                className="ml-1 hover:text-red-600"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};

export default EscalationFilters; 