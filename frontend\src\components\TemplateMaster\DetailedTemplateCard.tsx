import React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { extractTemplateVariables, formatVariableName, getVariableFormat } from "@/utils/templateUtils";
import { TEMPLATE_DETAILED_CARD } from "@/Constants/TemplateMaster";

interface DetailedTemplateCardProps {
  name: string;
  type: string;
  content: string;
  createdDate: string;
}

const DetailedTemplateCard: React.FC<DetailedTemplateCardProps> = ({
  name,
  type,
  content,
  createdDate,
}) => {
  const getTypeBadge = (type: string) => {
    const typeColors = {
      SMS: "bg-blue-100 text-blue-800",
      EMAIL: "bg-purple-100 text-purple-800",
      CALL_PROMPT: "bg-orange-100 text-orange-800",
    };
    
    return (
      <Badge className={`${typeColors[type.toUpperCase() as keyof typeof typeColors] || "bg-gray-100 text-gray-800"}`}>
        {type.toUpperCase()}
      </Badge>
    );
  };

  // Extract dynamic variables from content
  const templateVariables = extractTemplateVariables(content);
  const variableFormat = getVariableFormat(content);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="text-xl font-semibold">{name}</span>
          {getTypeBadge(type)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500">{TEMPLATE_DETAILED_CARD.TEMPLATE_TYPE}</label>
            <p className="text-sm text-gray-900 mt-1">{type.toUpperCase()}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500">{TEMPLATE_DETAILED_CARD.CREATED_DATE}</label>
            <p className="text-sm text-gray-900 mt-1">
              {createdDate ? new Date(createdDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }) : TEMPLATE_DETAILED_CARD.NOT_AVAILABLE}
            </p>
          </div>
        </div>
        
        <div>
          <label className="text-sm font-medium text-gray-500">{TEMPLATE_DETAILED_CARD.TEMPLATE_CONTENT}</label>
          <div className="mt-2 p-4 bg-gray-50 rounded-lg border max-h-64 overflow-y-auto">
            <pre className="text-sm text-gray-900 whitespace-pre-wrap font-sans">
              {content}
            </pre>
          </div>
        </div>

        {/* Dynamic Variables Section */}
        {templateVariables.length > 0 ? (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="text-sm font-medium text-blue-800 mb-3">{TEMPLATE_DETAILED_CARD.TEMPLATE_VARIABLES}</h4>
            <div className="space-y-2">
              <p className="text-xs text-blue-700 mb-3">
                {TEMPLATE_DETAILED_CARD.VARIABLES_DESCRIPTION.replace('{format}', variableFormat)}
              </p>
              <div className="flex flex-wrap gap-2">
                {templateVariables.map((variable, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="bg-white text-blue-700 border-blue-300 text-xs"
                  >
                    {formatVariableName(variable)}
                  </Badge>
                ))}
              </div>
              <p className="text-xs text-blue-600 mt-3 italic">
                {TEMPLATE_DETAILED_CARD.VARIABLES_FOOTNOTE}
              </p>
            </div>
          </div>
        ) : (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">{TEMPLATE_DETAILED_CARD.NO_VARIABLES_TITLE}</h4>
            <p className="text-xs text-gray-600">
              {TEMPLATE_DETAILED_CARD.NO_VARIABLES_MESSAGE}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DetailedTemplateCard; 