import axios from 'axios'
import dotenv from 'dotenv';
import * as constants from '../utils/constants.utils.js';
import logger from '../config/logger.config.js';
import * as reactivationService from './reactivation.service.js';

dotenv.config();

const apiKey = process.env.ELEVENLABS_API_KEY
const agentId = process.env.ELEVENLABS_AGENT_ID
const agentPhoneNumberId = process.env.ELEVENLABS_AGENT_PHONE_ID

/**
 * Submits a batch calling job to ElevenLabs
 * @param {Array} patients - Array of patient objects
 * @param {string} time - Optional scheduled time (datetime string)
 * @param {Object} options - Additional options including clinic_id and reactivation_days
 */
export const submitElevenLabsBatchCall = async (patients, time, options = {}) => {
  const url = constants.BATCH_CALL_URL
  const { clinic_id, reactivation_days, user_id } = options;

  const recipientChunks = getPhoneNumberChunks(patients, 10); // [{phone_number: "..."}, ...]

  const today = new Date();
  const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  // Convert time to Unix timestamp if provided
  let scheduledTimeUnix = null;
  let scheduledTime = null;
  if (time) {
    try {
      // Parse the datetime string and convert to Unix timestamp
      const scheduledDate = new Date(time);
      if (isNaN(scheduledDate.getTime())) {
        throw new Error('Invalid datetime format');
      }
      scheduledTimeUnix = Math.floor(scheduledDate.getTime() / 1000); // Convert to Unix timestamp (seconds)
      scheduledTime = scheduledDate;
    } catch (error) {
      logger.error(`Error parsing scheduled time: ${time}`, error);
      throw new Error(`Invalid scheduled time format: ${time}. Expected format: YYYY-MM-DDTHH:mm`);
    }
  }

  // Create reactivation record for tracking
  let reactivationRecord = null;
  if (clinic_id) {
    try {
      reactivationRecord = await reactivationService.createReactivation({
        clinic_id,
        batch_name: `reactivation-${dateStr}-batch-1`,
        status: 'pending',
        patient_count: patients.length,
        scheduled_time_unix: scheduledTimeUnix,
        scheduled_time: scheduledTime,
        reactivation_days,
        created_by: user_id,
        updated_by: user_id,
      });
      logger.info(`Created reactivation record ${reactivationRecord.id} for clinic ${clinic_id}`);
    } catch (error) {
      logger.error('Error creating reactivation record:', error);
      // Continue with the call even if tracking fails
    }
  }

  for (let i = 0; i < recipientChunks.length; i++) {
    const recipients = recipientChunks[i];
    const batchName = `reactivation-${dateStr}-batch-${i + 1}`;

    const payload = {
      call_name: batchName,
      agent_id: agentId,
      agent_phone_number_id: agentPhoneNumberId,
      scheduled_time_unix: scheduledTimeUnix,
      recipients, // already in format [{ phone_number }]
    };
    
    try {
      const response = await axios.post(url, payload, {
        headers: {
          'xi-api-key': apiKey,
          'Content-Type': 'application/json',
        }
      });
      
      // Update reactivation record with ElevenLabs batch ID if available
      if (reactivationRecord && response.data && response.data.id) {
        await reactivationService.updateReactivation(reactivationRecord.id, {
          elevenlabs_batch_id: response.data.id,
          status: 'in_progress',
        });
      }
      
      logger.info(`Successfully submitted batch call ${i + 1} to ElevenLabs`);
    } catch (error) {
      logger.error(`Error submitting batch call ${i + 1} to ElevenLabs:`, error);
      
      // Update reactivation record with error if tracking exists
      if (reactivationRecord) {
        await reactivationService.updateReactivation(reactivationRecord.id, {
          status: 'failed',
          error_message: error.message || 'Failed to submit to ElevenLabs',
        });
      }
      
      throw error;
    }
  }

  return reactivationRecord;
};

export function getPhoneNumberChunks(patients, chunkSize = 10) {
  const phoneObjects = patients
    .filter(p => p.phone_number)
    .map(p => {
      let phone = p.phone_number.trim();      

      let data = {
        phone_number:phone,
        conversation_initiation_client_data:{
          dynamic_variables:{
            phone_number: phone,
            timeZone: 'US/Central',
            firstName: p.first_name,
            last_visit_summary: p.last_visit_summary,
            last_bookable_date: p.last_visit
          }
        }
      }

      return data;
    });

  const chunks = [];
  for (let i = 0; i < phoneObjects.length; i += chunkSize) {
    chunks.push(phoneObjects.slice(i, i + chunkSize));
  }

  return chunks;
}