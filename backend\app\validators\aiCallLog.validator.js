/**
 * AI Call Log Validators: express-validator schemas for AI call log create endpoint.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

export const createAICallLogSchema = [
  body('direction')
    .isIn(constants.AI_CALL_LOG_DIRECTIONS)
    .withMessage(constants.DIRECTION_INVALID)
    .notEmpty()
    .withMessage(constants.DIRECTION_REQUIRED),
  
  body('call_time_date')
    .isISO8601()
    .withMessage(constants.CALL_TIME_DATE_INVALID)
    .notEmpty()
    .withMessage(constants.CALL_TIME_DATE_REQUIRED),
  
  body('call_duration')
    .isInt({ min: 0 })
    .withMessage(constants.CALL_DURATION_INVALID)
    .notEmpty()
    .withMessage(constants.CALL_DURATION_REQUIRED),
  
  body('call_status')
    .isIn(constants.AI_CALL_LOG_STATUSES)
    .withMessage(constants.CALL_STATUS_INVALID)
    .notEmpty()
    .withMessage(constants.CALL_STATUS_REQUIRED),
  
  body('conversation_summary')
    .optional()
    .isString()
    .withMessage(constants.CONVERSATION_SUMMARY_INVALID),

  body('conversation_id')
    .optional()
    .isString()
    .withMessage(constants.CONV_ID_INVALID),
  
  body('call_summary_title')
    .optional()
    .isString()
    .withMessage(constants.CALL_SUMMARY_TITLE_INVALID),
  
  body('clinic_id')
    .optional()
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID_AI_CALL_LOG),
  
  body('patient_id')
    .optional()
    .isInt()
    .withMessage(constants.PATIENT_ID_INVALID_AI_CALL_LOG),
  
  body('phone_number')
    .optional()
    .isString()
    .withMessage(constants.INVALID_PHONE_NUMBER),
]; 