// ReactivationStatsGrid.tsx
// Renders a grid of StatCard components to display reactivation program statistics.
// Shows active campaigns, calls today, success rate, and new bookings.

import React from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { RefreshCw, Phone, CheckCircle, TrendingUp } from "lucide-react";
import {
  REACTIVATION_PROGRAM_STAT_ACTIVE_APPOINTMENTS,
  REACTIVATION_PROGRAM_STAT_CALLS,
  REACTIVATION_PROGRAM_STAT_SUCCESS_RATE,
  REACTIVATION_PROGRAM_STAT_PATIENTS,
} from "@/Constants/ReactivationProgram";

/**
 * Props for the ReactivationStatsGrid component
 * @property campaigns - Array of campaign objects
 * @property callsToday - Number of calls made today
 * @property successRate - Success rate percentage
 * @property newBookings - Number of new bookings
 */

interface ReactivationStatsGridProps {
  appointments: number;
  calls: number;
  successRate: string;
  newBookings: number;
}

/**
 * Renders a grid of StatCard components for reactivation program statistics.
 * Used in the Reactivation Program page.
 */
const ReactivationStatsGrid: React.FC<ReactivationStatsGridProps> = ({
  appointments,
  calls,
  successRate,
  newBookings,
}) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
    <StatCard
      title={REACTIVATION_PROGRAM_STAT_ACTIVE_APPOINTMENTS}
      value={appointments}
      icon={<RefreshCw className="h-8 w-8 text-blue-600" />}
    />
    <StatCard
      title={REACTIVATION_PROGRAM_STAT_CALLS}
      value={calls}
      icon={<Phone className="h-8 w-8 text-green-600" />}
    />
    <StatCard
      title={REACTIVATION_PROGRAM_STAT_SUCCESS_RATE}
      value={successRate}
      icon={<CheckCircle className="h-8 w-8 text-purple-600" />}
    />
    <StatCard
      title={REACTIVATION_PROGRAM_STAT_PATIENTS}
      value={newBookings}
      icon={<TrendingUp className="h-8 w-8 text-orange-600" />}
    />
  </div>
);

export default ReactivationStatsGrid;
