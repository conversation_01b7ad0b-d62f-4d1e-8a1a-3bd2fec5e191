import React, { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>T<PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Clock, User, Phone, MessageSquare, AlertTriangle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import EscalationStatsGrid from "./EscalationStatsGrid";
import EscalationFilters from "./EscalationFilters";
import { EscalationData } from "@/hooks/useEscalation";

interface EscalationListProps {
  escalations?: EscalationData[];
  loading?: boolean;
}

interface TranscriptMessage {
  role: string;
  message: string;
  time_in_call_secs: number;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "open":
      return "bg-red-100 text-red-800 border-red-200";
    case "in_progress":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "resolved":
      return "bg-green-100 text-green-800 border-green-200";
    case "closed":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getTagColor = (tag: string) => {
  switch (tag) {
    case "busy":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "technical":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "complaint":
      return "bg-red-100 text-red-800 border-red-200";
    case "urgent":
      return "bg-purple-100 text-purple-800 border-purple-200";
    case "follow_up":
      return "bg-green-100 text-green-800 border-green-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const EscalationList: React.FC<EscalationListProps> = ({ 
  escalations = [], 
  loading = false 
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [tagFilter, setTagFilter] = useState("all");
  const [selectedEscalation, setSelectedEscalation] = useState<number | null>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  // Calculate stats
  const stats = useMemo(() => {
    const total = escalations.length;
    const open = escalations.filter(e => e.status === "open").length;
    const resolved = escalations.filter(e => e.status === "resolved").length;
    const pending = escalations.filter(e => e.status === "in_progress").length;

    return { total, open, resolved, pending };
  }, [escalations]);

  // Filter escalations
  const filteredEscalations = useMemo(() => {
    return escalations.filter((escalation) => {
      const matchesSearch = !searchTerm || 
        escalation.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (escalation.patient_name && escalation.patient_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (escalation.assignee_name && escalation.assignee_name.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus = statusFilter === "all" || escalation.status === statusFilter;
      const matchesTag = tagFilter === "all" || escalation.tags === tagFilter;

      return matchesSearch && matchesStatus && matchesTag;
    });
  }, [escalations, searchTerm, statusFilter, tagFilter]);

  // Parse transcript for a given escalation
  const parseTranscript = (transcript: unknown): TranscriptMessage[] => {
    if (!transcript) return [];

    // Handle string array
    if (Array.isArray(transcript)) {
      return transcript
        .map((item: unknown): TranscriptMessage => {
          if (typeof item === "string") {
            try {
              const parsed = JSON.parse(item) as Record<string, unknown>;
              return {
                role: (parsed.role as string) || "unknown",
                message: (parsed.message as string) || "",
                time_in_call_secs: (parsed.time_in_call_secs as number) || 0,
              };
            } catch {
              return {
                role: "unknown",
                message: item as string,
                time_in_call_secs: 0,
              };
            }
          }
          return item as TranscriptMessage;
        })
        .filter((message: TranscriptMessage) => 
          message.message && message.message.trim().length > 0
        );
    }

    // Handle object array directly
    if (Array.isArray(transcript) && transcript.length > 0 && typeof transcript[0] === "object") {
      return transcript
        .map((item: Record<string, unknown>): TranscriptMessage => ({
          role: (item.role as string) || "unknown",
          message: (item.message as string) || "",
          time_in_call_secs: (item.time_in_call_secs as number) || 0,
        }))
        .filter((message: TranscriptMessage) => 
          message.message && message.message.trim().length > 0
        );
    }

    // Handle single object
    if (typeof transcript === "object" && transcript !== null) {
      const item = transcript as Record<string, unknown>;
      const message = {
        role: (item.role as string) || "unknown",
        message: (item.message as string) || "",
        time_in_call_secs: (item.time_in_call_secs as number) || 0,
      };
      
      // Only return if message is not empty
      return message.message && message.message.trim().length > 0 ? [message] : [];
    }

    return [];
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <EscalationStatsGrid stats={stats} />
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
            </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <EscalationStatsGrid stats={stats} />

      {/* Filters */}
      <EscalationFilters
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        tagFilter={tagFilter}
        onSearchChange={handleSearchChange}
        onClearSearch={handleClearSearch}
        onStatusFilterChange={setStatusFilter}
        onTagFilterChange={setTagFilter}
      />

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredEscalations.length} of {escalations.length} escalations
        </p>
      </div>

      {/* Escalations List */}
      {filteredEscalations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              Recent Escalations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredEscalations.map((escalation) => {
                const parsedTranscript = parseTranscript(escalation.transcript);
                
                return (
                  <div
                    key={escalation.id}
                    className="p-4 border border-gray-200 rounded-lg hover:shadow-lg focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
                    onClick={() =>
                      setSelectedEscalation(selectedEscalation === escalation.id ? null : escalation.id)
                    }
                  >
                    {/* Top row: escalation icon, summary, status/tag badges */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {/* Escalation icon */}
                        <div className="p-2 rounded-full bg-red-100">
                          <AlertTriangle className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {escalation.summary}
                          </h3>
                          <div className="flex items-center space-x-3 text-sm text-gray-600">
                            <span>{escalation.patient_name}</span>
                            <span className="flex items-center">
                              <Phone className="h-4 w-4 mr-1" />
                              {escalation.patient_phone}
                            </span>
                            <span className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {formatDistanceToNow(new Date(escalation.created_at), {
                                addSuffix: true,
                              })}
                            </span>
                          </div>
                        </div>
                      </div>
                      {/* Status and tag badges */}
                      <div className="flex items-center space-x-3">
                        <Badge className={`${getStatusColor(escalation.status)} border`}>
                          {escalation.status.replace("_", " ").toUpperCase()}
                        </Badge>
                        <Badge className={`${getTagColor(escalation.tags)} border`}>
                          {escalation.tags.toUpperCase()}
                        </Badge>
                      </div>
                    </div>

                    {/* Expanded Details (shown when selected) */}
                    {selectedEscalation === escalation.id && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {/* Assignee Information */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                              <User className="h-4 w-4 mr-2 text-blue-600" />
                              Assigned To
                            </h4>
                            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                              <User className="h-5 w-5 text-blue-600" />
                              <div className="flex-1">
                                <p className="font-medium text-gray-900">
                                  {escalation.assignee_name}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Assigned to handle this escalation
                                </p>
                              </div>
                            </div>
                          </div>

                                                     {/* Transcript Section */}
                           <div>
                             <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                               <MessageSquare className="h-4 w-4 mr-2" />
                               Conversation Transcript
                             </h4>
                             
                                                                                                                     {parsedTranscript.length > 0 ? (
                               <Dialog>
                                 <DialogTrigger asChild>
                                   <Button
                                     variant="outline"
                                     size="sm"
                                     className="flex items-center gap-1"
                                     onClick={(e) => {
                                       e.stopPropagation();
                                     }}
                                   >
                                     <MessageSquare className="h-4 w-4" />
                                     View Transcript
                                   </Button>
                                 </DialogTrigger>
                                <DialogContent className="max-w-4xl max-h-[80vh]">
                                  <DialogHeader>
                                    <DialogTitle className="flex items-center gap-2">
                                      <MessageSquare className="h-5 w-5" />
                                      Conversation Transcript
                                    </DialogTitle>
                                  </DialogHeader>
                                  <ScrollArea className="h-[60vh] pr-4">
                                    <div className="space-y-4">
                                      {parsedTranscript.map((message, index) => (
                                        <div key={index} className="space-y-2">
                                          <div className="flex items-center justify-between">
                                            <Badge
                                              variant={
                                                message.role === "agent" ? "default" : "secondary"
                                              }
                                              className={
                                                message.role === "agent"
                                                  ? "bg-blue-100 text-blue-800"
                                                  : "bg-gray-100 text-gray-800"
                                              }
                                            >
                                              {message.role === "agent" ? "AI Agent" : "Patient"}
                                            </Badge>
                                            <span className="text-xs text-gray-500">
                                              {formatTime(message.time_in_call_secs)}
                                            </span>
                                          </div>
                                          <div
                                            className={`p-3 rounded-lg ${
                                              message.role === "agent"
                                                ? "bg-blue-50 border border-blue-200"
                                                : "bg-gray-50 border border-gray-200"
                                            }`}
                                          >
                                            <p className="text-sm text-gray-900 whitespace-pre-wrap">
                                              {message.message}
                                            </p>
                                          </div>
                                          {index < parsedTranscript.length - 1 && <Separator />}
                                        </div>
                                      ))}
                                    </div>
                                  </ScrollArea>
                                </DialogContent>
                              </Dialog>
                            ) : (
                              <div className="p-3 bg-gray-50 rounded-lg text-center">
                                <p className="text-sm text-gray-600">No transcript available</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {filteredEscalations.length === 0 && !loading && (
        <div className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No escalations found</h3>
          <p className="text-gray-600">
            {escalations.length === 0 
              ? "No escalations have been created yet." 
              : "No escalations match your current filters."}
          </p>
        </div>
      )}
    </div>
  );
};

export default EscalationList; 