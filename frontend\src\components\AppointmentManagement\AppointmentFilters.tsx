import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Filter } from "lucide-react";
import SearchBar from "@/components/CommonComponents/SearchBar";
import {
  PLACEHOLDER_SEARCH_PATIENT,
  PLACEHOLDER_ALL_DOCTORS,
  LABEL_FILTERS,
} from "@/Constants/Appointment";

// Props for the AppointmentFilters component
interface AppointmentFiltersProps {
  filterDoctor: string; // Currently selected doctor filter
  setFilterDoctor: (val: string) => void; // Handler for changing doctor filter
  doctorList: string[]; // List of available doctors
  searchValue: string; // Current value of the search input
  setSearchValue: (val: string) => void; // Handler for changing search input
}

/**
 * Renders filter controls for appointments: doctor and search.
 * Calls handlers when filters are changed.
 */
const AppointmentFilters: React.FC<AppointmentFiltersProps> = ({ filterDoctor, setFilterDoctor, doctorList, searchValue, setSearchValue }) => (
  <div className="flex items-center space-x-4">
    {/* Filter label and icon */}
    <div className="flex items-center space-x-2">
      <Filter className="h-4 w-4 text-gray-500" />
      <span className="text-sm font-medium text-gray-700">{LABEL_FILTERS}</span>
    </div>
    {/* Doctor filter dropdown */}
    <Select value={filterDoctor} onValueChange={setFilterDoctor}>
      <SelectTrigger className="w-48">
        <SelectValue placeholder={PLACEHOLDER_ALL_DOCTORS} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">{PLACEHOLDER_ALL_DOCTORS}</SelectItem>
        {doctorList.slice(1).map((doctorName) => (
          <SelectItem key={doctorName} value={doctorName}>{doctorName}</SelectItem>
        ))}
      </SelectContent>
    </Select>
    {/* Search bar for patient name */}
    <SearchBar
      placeholder={PLACEHOLDER_SEARCH_PATIENT}
      value={searchValue}
      onChange={e => setSearchValue(e.target.value)}
      className="w-64"
    />
  </div>
);

export default AppointmentFilters;
