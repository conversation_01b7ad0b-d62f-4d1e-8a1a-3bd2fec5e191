import React from "react";
import { User, LogOut, ChevronDown, Lock } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useUserProfile } from "@/hooks/useUserProfile";
import { useAuth } from "@/components/contexts/AuthContext";
import {
  PROFILE_DROPDOWN,
  ROUTES,
  ERROR_MESSAGES,
  ACCESSIBILITY,
  CSS_CLASSES,
  ICONS,
  AVATAR,
  TEXT_STYLES,
} from "@/Constants/Header";

interface UserProfileDropdownProps {
  className?: string;
}

const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({
  className = "",
}) => {
  const { profile, loading } = useUserProfile();
  const { logout } = useAuth();
  const router = useRouter();

  const handleViewProfile = () => {
    router.push(ROUTES.PROFILE);
  };

  const handleChangePassword = () => {
    router.push(ROUTES.CHANGE_PASSWORD);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push(ROUTES.LOGIN);
    } catch (error) {
      console.error(ERROR_MESSAGES.LOGOUT_FAILED, error);
      router.push(ROUTES.LOGIN);
    }
  };

  // Generate initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n: string) => n[0])
      .join("")
      .toUpperCase();
  };

  const userName = profile?.name || PROFILE_DROPDOWN.LOADING_TEXT;

  return (
    <div className={className}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            title={ACCESSIBILITY.PROFILE_DROPDOWN_TRIGGER}
            className={CSS_CLASSES.PROFILE_BUTTON}
            disabled={loading}
          >
            <Avatar className={AVATAR.SIZE}>
              <AvatarFallback className={ICONS.AVATAR_FALLBACK}>
                {getInitials(userName)}
              </AvatarFallback>
            </Avatar>
            <span className={TEXT_STYLES.USER_NAME}>{userName}</span>
            <ChevronDown className={ICONS.CHEVRON_DOWN} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className={CSS_CLASSES.DROPDOWN_CONTENT}
          sideOffset={8}
        >
          <div className={CSS_CLASSES.DROPDOWN_HEADER}>
            <p className={TEXT_STYLES.USER_NAME}>{userName}</p>
            <p className={TEXT_STYLES.USER_EMAIL}>{profile?.email}</p>
          </div>
          <div className="py-2">
            <DropdownMenuItem
              onClick={handleViewProfile}
              className={CSS_CLASSES.DROPDOWN_ITEM}
            >
              <User className={ICONS.USER} />
              <span className={TEXT_STYLES.DROPDOWN_ITEM_TEXT}>
                {PROFILE_DROPDOWN.MY_PROFILE}
              </span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleChangePassword}
              className={CSS_CLASSES.DROPDOWN_ITEM}
            >
              <Lock className={ICONS.LOCK} />
              <span className={TEXT_STYLES.DROPDOWN_ITEM_TEXT}>
                {PROFILE_DROPDOWN.CHANGE_PASSWORD}
              </span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className={CSS_CLASSES.DROPDOWN_SEPARATOR} />
            <DropdownMenuItem
              onClick={handleLogout}
              className={CSS_CLASSES.DROPDOWN_ITEM_LOGOUT}
            >
              <LogOut className={ICONS.LOGOUT} />
              <span className={TEXT_STYLES.DROPDOWN_ITEM_TEXT}>
                {PROFILE_DROPDOWN.LOGOUT}
              </span>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default UserProfileDropdown;
