.forgotContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0fdf4 100%);
  padding: 0 1rem;
}

.forgotMotion {
  width: 100%;
  max-width: 480px;
}

.forgotCard {
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 1.5px 8px rgba(34,197,94,0.07);
  background: rgba(255,255,255,0.95);
  border: 1px solid #e5e7eb;
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
}

.forgotHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: center;
  margin-top: 0.5rem;
}

.forgotLogo {
  margin: 0 auto 0.5rem auto;
}

.forgotTitle {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(90deg, #1e40af, #22c55e, #14b8a6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: -0.02em;
}

.forgotSubtitle {
  color: var(--color-muted-foreground, #6b7280);
  margin-top: 0.25rem;
  font-size: 1rem;
}

.forgotForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.forgotInput {
  padding-left: 2.5rem;
  min-height: 44px;
  font-size: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  transition: border-color 0.2s;
}
.forgotInput:focus {
  border-color: #2563eb;
  background: #fff;
}

.forgotIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-muted-foreground, #6b7280);
}

.forgotError {
  font-size: 0.875rem;
  color: #ef4444;
  margin-top: 0.25rem;
  display: block;
}

.forgotButton {
  width: 100%;
  color: #fff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 0;
  margin-top: 0.5rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}

.forgotButton:focus,
.forgotButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.forgotButton:disabled {
  background: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
}

.forgotLogin {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-muted-foreground, #6b7280);
}

.forgotLoginLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: underline;
  transition: color 0.15s;
}

.forgotSuccess {
  color: #22c55e;
  font-weight: 500;
  margin-bottom: 12px;
  display: block;
  text-align: center;
}

.forgotButtonSuccess {
  background: #22c55e;
  color: #fff;
  cursor: default;
  opacity: 0.8;
  pointer-events: none;
}
