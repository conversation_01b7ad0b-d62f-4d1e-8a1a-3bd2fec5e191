export const PROFILE_CONSTANTS = {
  // Page Titles and Headers
  PAGE_TITLE: "Profile Settings",
  PROFILE_PREVIEW_TITLE: "Profile Preview",
  PERSONAL_INFO_TITLE: "Personal Information",
  ACCOUNT_SETTINGS_TITLE: "Account Settings",
  
  // Subtitles and Descriptions
  EDIT_SUBTITLE: "Edit your personal information and account settings",
  VIEW_SUBTITLE: "View your profile information",
  PREVIEW_SUBTITLE: "Your current profile information",
  EDIT_HINT: "Click \"Edit Profile\" to make changes",
  
  // Form Labels
  FULL_NAME_LABEL: "Full Name",
  EMAIL_LABEL: "Email Address",
  PHONE_LABEL: "Phone Number",
  ROLE_LABEL: "Role",
  TIMEZONE_LABEL: "Timezone",
  
  // Form Placeholders
  FULL_NAME_PLACEHOLDER: "Enter your full name",
  EMAIL_PLACEHOLDER: "Enter your email",
  PHONE_PLACEHOLDER: "Enter your phone number",
  ROLE_PLACEHOLDER: "Select your role",
  TIMEZONE_PLACEHOLDER: "Select your timezone",
  
  // Button Text
  EDIT_PROFILE: "Edit Profile",
  SAVE_CHANGES: "Save Changes",
  CANCEL: "Cancel",
  BACK_TO_DASHBOARD: "Back to Dashboard",
  RETRY: "Retry",
  REFRESH: "Refresh",
  SAVING: "Saving...",
  
  // Status and Messages
  ACTIVE_STATUS: "Active",
  NOT_PROVIDED: "Not provided",
  LOADING_MESSAGE: "Loading profile...",
  NO_DATA_MESSAGE: "No profile data available",
  
  // Default Values
  DEFAULT_TIMEZONE: "America/New_York",
  DEFAULT_ROLE_ID: 1,
};

export const TIMEZONES = [
  { value: "America/New_York", label: "America/New_York" },
  { value: "America/Chicago", label: "America/Chicago" },
  { value: "America/Denver", label: "America/Denver" },
  { value: "America/Los_Angeles", label: "America/Los_Angeles" },
  { value: "Asia/Kolkata", label: "Asia/Kolkata" },
];

export const ROLES = [
  { value: 1, label: "Super Admin" },
  { value: 2, label: "Clinic Admin" },
  { value: 3, label: "Front Desk" },
  { value: 4, label: "Agent" },
];

export const ROLE_MAPPING = {
  SUPER_ADMIN: "super_admin",
  CLINIC_ADMIN: "clinic_admin",
  FRONT_DESK: "front_desk",
  AGENT: "agent",
};

export const ROLE_BADGE_VARIANTS = {
  SUPER_ADMIN: "destructive" as const,
  CLINIC_ADMIN: "default" as const,
  FRONT_DESK: "secondary" as const,
  AGENT: "outline" as const,
} as const;

export const FORM_FIELDS = {
  NAME: "name",
  EMAIL: "email",
  ROLE_ID: "role_id",
  PHONE: "user_phonenumber",
  TIMEZONE: "timezone",
};

export const PREVIEW_LABELS = {
  PHONE: "Phone",
  TIMEZONE: "Timezone",
  STATUS: "Status",
};

export const ROUTES = {
  DASHBOARD: "/dashboard",
};

export const ACCESSIBILITY = {
  EDIT_PROFILE_BUTTON: "Edit profile button",
  SAVE_CHANGES_BUTTON: "Save changes button",
  CANCEL_BUTTON: "Cancel button",
  BACK_BUTTON: "Back to dashboard button",
  RETRY_BUTTON: "Retry button",
  REFRESH_BUTTON: "Refresh button",
  FULL_NAME_INPUT: "Full name input",
  EMAIL_INPUT: "Email input",
  PHONE_INPUT: "Phone number input",
  ROLE_SELECT: "Role select",
  TIMEZONE_SELECT: "Timezone select",
};

export const ERROR_MESSAGES = {
  UPDATE_FAILED: "Failed to update profile",
  LOAD_FAILED: "Failed to load profile",
  NO_PROFILE_DATA: "No profile data available",
};

export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: "Profile updated successfully",
};

export const ICONS = {
  SETTINGS: "h-5 w-5 text-blue-600",
  USER: "h-4 w-4",
  EDIT: "h-4 w-4",
  X: "h-4 w-4",
  CHECK_CIRCLE: "h-4 w-4",
  LOADER: "h-4 w-4 animate-spin",
  ARROW_LEFT: "h-4 w-4",
  USER_CHECK: "h-5 w-5 text-blue-600",
  ALERT_CIRCLE: "h-4 w-4",
  LOADER_LARGE: "h-8 w-8",
};

export const CSS_CLASSES = {
  // Container and Layout
  PROFILE_CONTAINER: "profileContainer",
  PROFILE_WRAPPER: "profileWrapper",
  PROFILE_GRID: "profileGrid",
  FORM_COLUMN: "formColumn",
  PREVIEW_COLUMN: "previewColumn",
  
  // Cards
  PROFILE_CARD: "profileCard",
  PREVIEW_CARD: "previewCard",
  CARD_HEADER: "cardHeader",
  CARD_CONTENT: "cardContent",
  CARD_HEADER_CONTENT: "cardHeaderContent",
  CARD_HEADER_LEFT: "cardHeaderLeft",
  CARD_HEADER_ICON: "cardHeaderIcon",
  CARD_HEADER_TEXT: "cardHeaderText",
  CARD_TITLE: "cardTitle",
  CARD_SUBTITLE: "cardSubtitle",
  
  // Buttons
  EDIT_BUTTON: "editButton",
  SAVE_BUTTON: "saveButton",
  CANCEL_BUTTON: "cancelButton",
  BACK_BUTTON: "backButton",
  ERROR_BUTTON: "errorButton",
  REFRESH_BUTTON: "refreshButton",
  
  // Form
  FORM: "form",
  FORM_SECTION: "formSection",
  FORM_GRID: "formGrid",
  FORM_FIELD: "formField",
  FORM_LABEL: "formLabel",
  FORM_INPUT: "formInput",
  SELECT_TRIGGER: "selectTrigger",
  SECTION_HEADER: "sectionHeader",
  SECTION_ICON: "sectionIcon",
  SECTION_TITLE: "sectionTitle",
  FORM_ACTIONS: "formActions",
  ACTIONS_RIGHT: "actionsRight",
  EDIT_HINT: "editHint",
  
  // Separator
  SEPARATOR: "separator",
  
  // Preview
  PREVIEW_CONTENT: "previewContent",
  AVATAR: "avatar",
  PREVIEW_INFO: "previewInfo",
  PREVIEW_NAME: "previewName",
  PREVIEW_BADGE: "previewBadge",
  PREVIEW_EMAIL: "previewEmail",
  PREVIEW_DETAILS: "previewDetails",
  DETAIL_ROW: "detailRow",
  DETAIL_LABEL: "detailLabel",
  DETAIL_VALUE: "detailValue",
  STATUS_BADGE: "statusBadge",
  
  // Loading and Error States
  LOADING_CONTAINER: "loadingContainer",
  LOADING_CONTENT: "loadingContent",
  LOADING_SPINNER: "loadingSpinner",
  LOADING_TEXT: "loadingText",
  ERROR_CONTAINER: "errorContainer",
  ERROR_CONTENT: "errorContent",
  ERROR_ICON: "errorIcon",
  ERROR_TEXT: "errorText",
  NO_DATA_CONTAINER: "noDataContainer",
  NO_DATA_CONTENT: "noDataContent",
  NO_DATA_ICON: "noDataIcon",
  NO_DATA_TEXT: "noDataText",
}; 