import { useCallback } from "react";
import { apiRequest } from "../utils/axios.utils";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import {
  CLINIC_CREATE_SUCCESS,
  CLINIC_CREATE_FAILED,
  CLINIC_UPDATE_SUCCESS,
  CLINIC_UPDATE_FAILED,
  CLINIC_DELETE_SUCCESS,
  CLINIC_DELETE_FAILED,
  CLINIC_FETCH_FAILED,
  CLIN<PERSON>S_FETCH_FAILED,
  REACTIVATION_SCHEDULE_API_SUCCESS,
  REACTIVATION_SCHEDULE_API_FAILED,
  BATCH_CALL_API_SUCCESS,
  BATCH_CALL_API_FAILED,
} from "@/Constants/ClinicMaster";

const CLINIC_CREATE_ENDPOINT = "/clinic/create";
const CLINIC_LIST_ENDPOINT = "/clinic/list";
const CLIN<PERSON>_DETAIL_ENDPOINT = (id: number) => `/clinic/${id}`;
const CLINIC_REACTIVATION_ENDPOINT = (id: number) => `/clinic/reactivation-days/${id}`;
const PATIENT_BATCH_CALL_ENDPOINT = "/patient/outbound/all";

export interface AddClinicPayload {
  clinic_name: string;
  location: string;
  timezone: string;
  clinic_email: string;
  clinic_phonenumber: string;
  working_hours: string; // JSON string
  working_days: string; // JSON string
  sms_service?: boolean;
  email_service?: boolean;
}

export interface ReactivationDaysPayload {
  reactivation_days: number;
  batch_call_time: string; // Format: "HH:mm" (e.g., "10:00")
}

export interface BatchCallPayload {
  clinic_id: number;
  time: number; // Unix timestamp
}

export const useClinic = () => {
  const createClinic = useCallback(async (clinicData: AddClinicPayload) => {
    try {
      const response = (await apiRequest.post(
        CLINIC_CREATE_ENDPOINT,
        clinicData
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || CLINIC_CREATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || CLINIC_CREATE_FAILED);
        return {
          success: false,
          error: response?.message || CLINIC_CREATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, CLINIC_CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const getClinics = useCallback(async () => {
    try {
      const response = (await apiRequest.get(CLINIC_LIST_ENDPOINT)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        const filtered = Array.isArray(response.data)
          ? response.data.filter((clinic: Record<string, unknown>) => !(clinic.is_deleted as boolean))
          : response.data;
        return { success: true, data: filtered };
      } else {
        compactErrorMessage(response?.message || CLINICS_FETCH_FAILED);
        return {
          success: false,
          error: response?.message || CLINICS_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, CLINICS_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const getClinicById = useCallback(async (id: number) => {
    try {
      const response = (await apiRequest.get(
        CLINIC_DETAIL_ENDPOINT(id)
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || CLINIC_FETCH_FAILED);
        return {
          success: false,
          error: response?.message || CLINIC_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, CLINIC_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const updateClinic = useCallback(async (id: number, clinicData: Record<string, unknown>) => {
    try {
      const response = (await apiRequest.put(
        CLINIC_DETAIL_ENDPOINT(id),
        clinicData
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || CLINIC_UPDATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || CLINIC_UPDATE_FAILED);
        return {
          success: false,
          error: response?.message || CLINIC_UPDATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, CLINIC_UPDATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const deleteClinic = useCallback(async (id: number) => {
    try {
      const response = (await apiRequest.delete(
        CLINIC_DETAIL_ENDPOINT(id)
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || CLINIC_DELETE_SUCCESS);
        return { success: true };
      } else {
        compactErrorMessage(response?.message || CLINIC_DELETE_FAILED);
        return {
          success: false,
          error: response?.message || CLINIC_DELETE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, CLINIC_DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const updateReactivationDays = useCallback(async (id: number, reactivationData: ReactivationDaysPayload) => {
    try {
      const response = (await apiRequest.put(
        CLINIC_REACTIVATION_ENDPOINT(id),
        reactivationData
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || REACTIVATION_SCHEDULE_API_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || REACTIVATION_SCHEDULE_API_FAILED);
        return {
          success: false,
          error: response?.message || REACTIVATION_SCHEDULE_API_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, REACTIVATION_SCHEDULE_API_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  const triggerBatchCall = useCallback(async (clinicId: number, unixTimestamp: number) => {
    try {
      const payload: BatchCallPayload = {
        clinic_id: clinicId,
        time: unixTimestamp,
      };

      const response = (await apiRequest.post(
        PATIENT_BATCH_CALL_ENDPOINT,
        payload
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || BATCH_CALL_API_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || BATCH_CALL_API_FAILED);
        return {
          success: false,
          error: response?.message || BATCH_CALL_API_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, BATCH_CALL_API_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  }, []);

  return {
    createClinic,
    getClinics,
    getClinicById,
    updateClinic,
    deleteClinic,
    updateReactivationDays,
    triggerBatchCall, // Add this new function
  };
};
