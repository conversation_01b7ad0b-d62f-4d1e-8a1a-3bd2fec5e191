import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createEscalationSchema, updateEscalationSchema, createEscalationWithTranscriptSchema } from '../validators/escalation.validator.js';
import * as escalationController from '../controllers/escalation.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new escalation
// POST /v1/escalation/create
router.post(
  '',
  verifyToken,
  validate(createEscalationSchema),
  escalationController.createEscalation
);

// Create a new escalation with transcript from ElevenLabs
// POST /v1/escalation/create-with-transcript
router.post(
  '/create-with-transcript',
  // verifyToken,
  validate(createEscalationWithTranscriptSchema),
  escalationController.createEscalationWithTranscript
);

// Get all escalations
// GET /v1/escalation/list
router.get(
  '',
  verifyToken,
  escalationController.getAllEscalations
);

// Get escalation by ID
// GET /v1/escalation/:id
router.get(
  '/:id',
  verifyToken,
  escalationController.getEscalationById
);

// Update escalation by ID
// PUT /v1/escalation/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateEscalationSchema),
  escalationController.updateEscalation
);

// Delete escalation by ID
// DELETE /v1/escalation/:id
router.delete(
  '/:id',
  verifyToken,
  escalationController.deleteEscalation
);

export default router; 