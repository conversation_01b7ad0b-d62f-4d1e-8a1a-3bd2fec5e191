export const ERROR_MESSAGES = {
  REQUIRED_FIELDS: "All fields are required.",
  PASSWORDS_MISMATCH: "Passwords do not match.",
  ACCEPT_TERMS: "You must accept the Terms & Conditions.",
  NETWORK_ERROR: "Network error. Please try again.",
  API_REQUEST_FAILED: "API request failed.",
  UNEXPECTED_ERROR: "An unexpected error occurred.",
  LOGIN_FAILED: "Login failed.",
  REGISTRATION_FAILED: "Registration failed.",
  // Simplified error messages for authentication
  AUTHENTICATION_FAILED: "Authentication failed",
  INTERNAL_SERVER_ERROR: "Internal server error",
};

export const SESSION_EXPIRED = "Session expired. Please login again.";

export const STATUS_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
  BAD_REQUEST: 400,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
};

export const DOCTOR_MESSAGES = {
  CREATE_SUCCESS: "Doctor created successfully.",
  CREATE_FAILED: "Failed to create doctor.",
  UPDATE_SUCCESS: "Doctor updated successfully.",
  UPDATE_FAILED: "Failed to update doctor.",
  DELETE_SUCCESS: "Doctor deleted successfully.",
  DELETE_FAILED: "Failed to delete doctor.",
};
