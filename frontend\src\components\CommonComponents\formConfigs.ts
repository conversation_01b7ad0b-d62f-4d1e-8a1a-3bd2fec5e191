// formConfigs.ts
// Contains form field configuration objects for patient, doctor, clinic, and campaign forms.
// Used by CommonForm to dynamically render forms based on type.

import { FIELD_TYPES, FORM_VALIDATION } from "@/Constants/CommonComponents";
import {
  PATIENT_FORM_FIELDS,
  PATIENT_FORM_PLACEHOLDERS,
  PATIENT_FORM_OPTIONS,
} from "@/Constants/PatientRegistry";
import {
  DOCTOR_FORM_FIELDS,
  DOCTOR_FORM_PLACEHOLDERS,
  DOCTOR_FORM_OPTIONS,
} from "@/Constants/DoctorDirectory";
import {
  CLINIC_FORM_FIELDS,
  CLINIC_FORM_PLACEHOLDERS,
  CLINIC_FORM_DEFAULTS,
  CLINIC_STATUS_OPTIONS,
} from "@/Constants/ClinicMaster";
import {
  CAMPAIGN_FORM_FIELDS,
  CAMPAIGN_FORM_PLACEHOLDERS,
  CAMPAIGN_FORM_DEFAULTS,
} from "@/Constants/ReactivationProgram";
import {
  TEMPLATE_FORM_FIELDS,
  TEMPLATE_FORM_PLACEHOLDERS,
  TEMPLATE_FORM_DEFAULTS,
  TEMPLATE_TYPE_OPTIONS,
} from "@/Constants/TemplateMaster";
import {
  APPOINTMENT_FORM_FIELDS,
  APPOINTMENT_FORM_PLACEHOLDERS,
  APPOINTMENT_STATUS_OPTIONS,
  APPOINTMENT_SOURCE_OPTIONS,
  APPOINTMENT_FORM_DEFAULTS,
} from "@/Constants/Appointment";
import { 
  templateValidationSchemas, 
  patientValidationSchemas,
  doctorValidationSchemas,
} from "@/utils/validation";
import { validatePhoneNumber } from "@/utils/phoneUtils";

/**
 * Patient form field configuration
 * Each object defines a field for the patient form
 */
export const patientFormConfig = [
  {
    name: "first_name",
    label: PATIENT_FORM_FIELDS.FIRST_NAME || "First Name",
    type: FIELD_TYPES.TEXT,
    required: true,
  },
  {
    name: "last_name",
    label: PATIENT_FORM_FIELDS.LAST_NAME || "Last Name",
    type: FIELD_TYPES.TEXT,
    required: true,
    validation: (value: string) => {
      const result = patientValidationSchemas.patientName.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "email",
    label: PATIENT_FORM_FIELDS.EMAIL,
    type: FIELD_TYPES.EMAIL,
    required: true,
    validation: (value: string) => {
      const result = patientValidationSchemas.patientEmail.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "phone_number",
    label: PATIENT_FORM_FIELDS.PHONE || "Phone Number",
    type: FIELD_TYPES.PHONE,
    required: true,
    placeholder: PATIENT_FORM_PLACEHOLDERS.PHONE,
    country: "us",
    enableAreaCodes: true,
    validation: (value: string) => {
      const validation = validatePhoneNumber(value);
      if (!validation.isValid) {
        return validation.error;
      }
      const result = patientValidationSchemas.patientPhone.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "address",
    label: PATIENT_FORM_FIELDS.ADDRESS || "Address",
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: PATIENT_FORM_PLACEHOLDERS.ADDRESS,
  },
  {
    name: "dob",
    label: PATIENT_FORM_FIELDS.DOB || "Date of Birth",
    type: FIELD_TYPES.DATE,
    required: true,
    gridCols: 2,
    validation: (value: string) => {
      const result = patientValidationSchemas.patientAge.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "gender",
    label: PATIENT_FORM_FIELDS.GENDER,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: PATIENT_FORM_OPTIONS.GENDER,
    gridCols: 2,
    validation: (value: string) => {
      const result = patientValidationSchemas.patientGender.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
];

/**
 * Doctor form field configuration
 * Each object defines a field for the doctor form
 */
export const doctorFormConfig = [
  {
    name: "name",
    label: DOCTOR_FORM_FIELDS.FULL_NAME,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: DOCTOR_FORM_PLACEHOLDERS.FULL_NAME,
    validation: (value: string) => {
      const result = doctorValidationSchemas.doctorName.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "specialization",
    label: DOCTOR_FORM_FIELDS.SPECIALIZATION,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: DOCTOR_FORM_OPTIONS.SPECIALIZATIONS,
    defaultValue: "Cardiology",
    gridCols: 2,
    validation: (value: string) => {
      const result = doctorValidationSchemas.doctorSpecialization.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "experience",
    label: DOCTOR_FORM_FIELDS.EXPERIENCE,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: DOCTOR_FORM_PLACEHOLDERS.EXPERIENCE,
    gridCols: 2,
    validation: (value: string) => {
      const result = doctorValidationSchemas.doctorExperience.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "qualification",
    label: DOCTOR_FORM_FIELDS.QUALIFICATION,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: DOCTOR_FORM_PLACEHOLDERS.QUALIFICATION,
    validation: (value: string) => {
      const result = doctorValidationSchemas.doctorQualification.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "phone",
    label: DOCTOR_FORM_FIELDS.PHONE,
    type: FIELD_TYPES.PHONE,
    required: true,
    placeholder: DOCTOR_FORM_PLACEHOLDERS.PHONE,
    country: "us",
    enableAreaCodes: true,
    validation: (value: string) => {
      const validation = validatePhoneNumber(value);
      if (!validation.isValid) {
        return validation.error || 'Invalid phone number';
      }
      const result = doctorValidationSchemas.doctorPhone.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "email",
    label: DOCTOR_FORM_FIELDS.EMAIL,
    type: FIELD_TYPES.EMAIL,
    required: true,
    gridCols: 2,
    validation: (value: string) => {
      const result = doctorValidationSchemas.doctorEmail.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "clinic",
    label: DOCTOR_FORM_FIELDS.CLINIC,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: DOCTOR_FORM_OPTIONS.CLINICS,
    defaultValue: "Main Clinic",
    validation: (value: string) => {
      if (!value || value.trim() === '') {
        return "Clinic is required";
      }
      return null;
    },
  },
];

/**
 * Clinic form field configuration
 * Each object defines a field for the clinic form
 */
export const clinicFormConfig = [
  {
    name: "name",
    label: CLINIC_FORM_FIELDS.CLINIC_NAME,
    type: FIELD_TYPES.TEXT,
    required: true,
    defaultValue: "",
  },
  {
    name: "address",
    label: CLINIC_FORM_FIELDS.ADDRESS,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: CLINIC_FORM_PLACEHOLDERS.ADDRESS,
    defaultValue: "",
  },
  {
    name: "timezone",
    label: "Timezone",
    type: FIELD_TYPES.TEXT,
    required: true,
    defaultValue: "Asia/Kolkata",
  },
  {
    name: "email",
    label: CLINIC_FORM_FIELDS.EMAIL,
    type: FIELD_TYPES.EMAIL,
    required: true,
    defaultValue: "",
  },
  {
    name: "phone",
    label: CLINIC_FORM_FIELDS.PHONE,
    type: FIELD_TYPES.PHONE,
    required: true,
    placeholder: CLINIC_FORM_PLACEHOLDERS.PHONE,
    defaultValue: "",
    country: "us", // Will be overridden dynamically in CommonForm
    enableAreaCodes: true,
    validation: (value: string) => {
      const validation = validatePhoneNumber(value);
      if (!validation.isValid) {
        return validation.error || 'Invalid phone number';
      }
      return null;
    },
  },
  {
    name: "isActive",
    label: "Status",
    type: FIELD_TYPES.SELECT,
    required: true,
    options: CLINIC_STATUS_OPTIONS,
    defaultValue: "true",
    gridCols: 2,
  },
  {
    name: "workingHours",
    label: CLINIC_FORM_FIELDS.WORKING_HOURS,
    type: FIELD_TYPES.TEXT,
    required: true,
    defaultValue: CLINIC_FORM_DEFAULTS.WORKING_HOURS,
    gridCols: 2,
  },
  {
    name: "workingDays",
    label: CLINIC_FORM_FIELDS.WORKING_DAYS,
    type: FIELD_TYPES.TEXT,
    required: true,
    defaultValue: CLINIC_FORM_DEFAULTS.WORKING_DAYS,
  },
  {
    name: "sms_service",
    label: "SMS Service",
    type: FIELD_TYPES.CHECKBOX,
    required: false,
    defaultValue: false,
    gridCols: 2,
  },
  {
    name: "email_service",
    label: "Email Service",
    type: FIELD_TYPES.CHECKBOX,
    required: false,
    defaultValue: false,
    gridCols: 2,
  },
];

/**
 * Campaign form field configuration
 * Each object defines a field for the campaign form
 */
export const campaignFormConfig = [
  {
    name: "name",
    label: CAMPAIGN_FORM_FIELDS.CAMPAIGN_NAME,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: CAMPAIGN_FORM_PLACEHOLDERS.CAMPAIGN_NAME,
  },
  {
    name: "startDate",
    label: CAMPAIGN_FORM_FIELDS.START_DATE,
    type: FIELD_TYPES.DATE,
    required: true,
    gridCols: 2,
  },
  {
    name: "endDate",
    label: CAMPAIGN_FORM_FIELDS.END_DATE,
    type: FIELD_TYPES.DATE,
    required: true,
    gridCols: 2,
    validation: (value: string) => {
      const startDate = new Date();
      const endDate = new Date(value);
      if (endDate <= startDate) {
        return FORM_VALIDATION.END_DATE_AFTER_START;
      }
      return null;
    },
  },
  {
    name: "targetPatients",
    label: CAMPAIGN_FORM_FIELDS.TARGET_PATIENTS,
    type: FIELD_TYPES.NUMBER,
    required: true,
    defaultValue: CAMPAIGN_FORM_DEFAULTS.TARGET_PATIENTS,
  },
];

/**
 * Template form field configuration
 * Each object defines a field for the template form
 */
export const templateFormConfig = [
  {
    name: "name",
    label: TEMPLATE_FORM_FIELDS.TEMPLATE_NAME,
    type: FIELD_TYPES.TEXT,
    required: true,
    placeholder: TEMPLATE_FORM_PLACEHOLDERS.TEMPLATE_NAME,
    validation: (value: string) => {
      const result = templateValidationSchemas.templateName.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "type",
    label: TEMPLATE_FORM_FIELDS.TYPE,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: TEMPLATE_TYPE_OPTIONS,
    defaultValue: TEMPLATE_FORM_DEFAULTS.TYPE,
    validation: (value: string) => {
      const result = templateValidationSchemas.templateType.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
  {
    name: "content",
    label: TEMPLATE_FORM_FIELDS.CONTENT,
    type: FIELD_TYPES.TEXTAREA,
    required: true,
    placeholder: TEMPLATE_FORM_PLACEHOLDERS.CONTENT,
    rows: 6,
    validation: (value: string) => {
      const result = templateValidationSchemas.templateContent.safeParse(value);
      return result.success ? null : result.error.issues[0].message;
    },
  },
];

/**
 * Appointment form field configuration
 * Each object defines a field for the appointment form
 */
export const appointmentFormConfig = [
  {
    name: "patient_id",
    label: APPOINTMENT_FORM_FIELDS.PATIENT,
    type: FIELD_TYPES.SELECT,
    required: true,
    placeholder: APPOINTMENT_FORM_PLACEHOLDERS.PATIENT,
    options: [], // Will be populated dynamically
  },
  {
    name: "clinic_id",
    label: APPOINTMENT_FORM_FIELDS.CLINIC,
    type: FIELD_TYPES.SELECT,
    required: true,
    placeholder: APPOINTMENT_FORM_PLACEHOLDERS.CLINIC,
    options: [], // Will be populated dynamically
  },
  {
    name: "doctor_id",
    label: APPOINTMENT_FORM_FIELDS.DOCTOR,
    type: FIELD_TYPES.SELECT,
    required: true,
    placeholder: APPOINTMENT_FORM_PLACEHOLDERS.DOCTOR,
    options: [], // Will be populated dynamically
  },
  {
    name: "appointment_date",
    label: APPOINTMENT_FORM_FIELDS.DATE,
    type: FIELD_TYPES.DATE,
    required: true,
    placeholder: APPOINTMENT_FORM_PLACEHOLDERS.DATE,
    gridCols: 1,
  },
  {
    name: "appointment_time",
    label: APPOINTMENT_FORM_FIELDS.TIME,
    type: "time",
    required: true,
    placeholder: APPOINTMENT_FORM_PLACEHOLDERS.TIME,
    gridCols: 1,
  },
  {
    name: "status",
    label: APPOINTMENT_FORM_FIELDS.STATUS,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: APPOINTMENT_STATUS_OPTIONS,
    defaultValue: APPOINTMENT_FORM_DEFAULTS.STATUS,
    gridCols: 1,
  },
  {
    name: "source",
    label: APPOINTMENT_FORM_FIELDS.SOURCE,
    type: FIELD_TYPES.SELECT,
    required: true,
    options: APPOINTMENT_SOURCE_OPTIONS,
    defaultValue: APPOINTMENT_FORM_DEFAULTS.SOURCE,
    gridCols: 1,
  },
];

/**
 * Mapping of form types to their respective field configs
 * Used by CommonForm to select the correct config
 */
export const formConfigs = {
  patient: patientFormConfig,
  doctor: doctorFormConfig,
  clinic: clinicFormConfig,
  campaign: campaignFormConfig,
  template: templateFormConfig,
  appointment: appointmentFormConfig,
};
