import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { ChevronDown } from 'lucide-react';
import { getPatientOverviewData, PatientData } from '@/services/dashboardDataService';
import { DASHBOARD_PATIENTS_OVERVIEW } from '@/Constants/Dashboard';

const DashboardPatientsOverview = () => {
  const [data, setData] = useState<PatientData[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      setLoading(true);
      const patientData = await getPatientOverviewData();
      setData(patientData);
    } catch (error) {
      console.error(DASHBOARD_PATIENTS_OVERVIEW.ERROR_FETCHING, error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6" style={{ width: '360px', height: '396px' }}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-xl border border-gray-200 shadow-sm p-6"
      style={{ 
        width: '360px', 
        height: '396px',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px'
      }}
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{DASHBOARD_PATIENTS_OVERVIEW.TITLE}</h3>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-3 py-1 cursor-pointer hover:bg-gray-200 transition-colors">
            <span className="text-sm font-medium text-gray-700">{DASHBOARD_PATIENTS_OVERVIEW.TIME_PERIOD}</span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </div>
      </div>
      
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 5, right: 15, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="day" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6b7280' }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6b7280' }}
              domain={[100, 1000]}
              ticks={[100, 250, 400, 550, 700, 850, 1000]}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: 'white',
                border: 'none',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Line 
              type="monotone" 
              dataKey="enters" 
              stroke={DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.ENTERS}
              strokeWidth={2.5}
              dot={{ fill: DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.ENTERS, strokeWidth: 2, r: 3 }}
              activeDot={{ r: 5, stroke: DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.ENTERS, strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="exits" 
              stroke={DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.EXITS}
              strokeWidth={2.5}
              dot={{ fill: DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.EXITS, strokeWidth: 2, r: 3 }}
              activeDot={{ r: 5, stroke: DASHBOARD_PATIENTS_OVERVIEW.CHART_COLORS.EXITS, strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      
      <div className="flex items-center justify-center space-x-6 mt-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-sm text-gray-600">{DASHBOARD_PATIENTS_OVERVIEW.LEGEND.PATIENT_ENTERS}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600">{DASHBOARD_PATIENTS_OVERVIEW.LEGEND.PATIENT_OUT}</span>
        </div>
      </div>
    </div>
  );
};

export default DashboardPatientsOverview;
