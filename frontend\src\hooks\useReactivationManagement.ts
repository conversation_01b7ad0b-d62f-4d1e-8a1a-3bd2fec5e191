import { useState, useCallback } from 'react';
import { reactivationService, ReactivationRecord, ReactivationStats, CreateReactivationData } from '@/services/reactivationService';
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from '@/utils/commonFunctions';

// Re-export types for components to use
export type { ReactivationRecord, ReactivationStats, CreateReactivationData };

export interface ReactivationSummary {
  total_campaigns: number;
  active_campaigns: number;
  completed_campaigns: number;
  failed_campaigns: number;
  total_patients_contacted: number;
  success_rate: number;
}

export function useReactivationManagement() {
  const [reactivations, setReactivations] = useState<ReactivationRecord[]>([]);
  const [stats, setStats] = useState<ReactivationStats[]>([]);
  const [summary, setSummary] = useState<ReactivationSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch reactivations for a clinic
  const getReactivationsByClinic = useCallback(async (clinicId: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await reactivationService.getReactivationsByClinic(clinicId);
      setReactivations(data);
      return { success: true, data };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch reactivations');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch reactivation statistics for a clinic
  const getReactivationStats = useCallback(async (clinicId: number, dateRange?: { start_date?: string; end_date?: string }) => {
    setLoading(true);
    setError(null);
    try {
      const data = await reactivationService.getReactivationStats(clinicId, dateRange);
      setStats(data);
      return { success: true, data };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch reactivation statistics');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Get reactivation summary for dashboard
  const getReactivationSummary = useCallback(async (clinicId?: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await reactivationService.getReactivationSummary(clinicId);
      setSummary(data);
      return { success: true, data };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch reactivation summary');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new reactivation record
  const createReactivation = useCallback(async (data: CreateReactivationData) => {
    setLoading(true);
    setError(null);
    try {
      const newReactivation = await reactivationService.createReactivation(data);
      setReactivations(prev => [newReactivation, ...prev]);
      compactSuccessMessage('Reactivation campaign created successfully');
      return { success: true, data: newReactivation };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to create reactivation campaign');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Update reactivation status
  const updateReactivationStatus = useCallback(async (id: number, status: string, additionalData?: Record<string, unknown>) => {
    setLoading(true);
    setError(null);
    try {
      const updatedReactivation = await reactivationService.updateReactivationStatus(id, status, additionalData);
      setReactivations(prev => 
        prev.map(r => r.id === id ? updatedReactivation : r)
      );
      compactSuccessMessage(`Reactivation status updated to ${status}`);
      return { success: true, data: updatedReactivation };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to update reactivation status');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a reactivation record
  const deleteReactivation = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);
    try {
      await reactivationService.deleteReactivation(id);
      setReactivations(prev => prev.filter(r => r.id !== id));
      compactSuccessMessage('Reactivation campaign deleted successfully');
      return { success: true };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to delete reactivation campaign');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Get all reactivations with filters
  const getAllReactivations = useCallback(async (filters?: Record<string, unknown>) => {
    setLoading(true);
    setError(null);
    try {
      const data = await reactivationService.getReactivations(filters);
      setReactivations(data);
      return { success: true, data };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch reactivations');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Clear all data
  const clearData = useCallback(() => {
    setReactivations([]);
    setStats([]);
    setSummary(null);
    setError(null);
  }, []);

  // Get reactivation by ID
  const getReactivationById = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);
    try {
      const data = await reactivationService.getReactivationById(id);
      return { success: true, data };
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch reactivation');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Filter reactivations by status
  const getReactivationsByStatus = useCallback((status: string) => {
    return reactivations.filter(r => r.status === status);
  }, [reactivations]);

  // Get recent reactivations (last 7 days)
  const getRecentReactivations = useCallback(() => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return reactivations.filter(r => 
      new Date(r.created_at) >= sevenDaysAgo
    );
  }, [reactivations]);

  // Get reactivation count by status
  const getReactivationCountByStatus = useCallback(() => {
    const counts = {
      pending: 0,
      in_progress: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
    };

    reactivations.forEach(r => {
      if (counts.hasOwnProperty(r.status)) {
        counts[r.status as keyof typeof counts]++;
      }
    });

    return counts;
  }, [reactivations]);

  return {
    // State
    reactivations,
    stats,
    summary,
    loading,
    error,
    
    // Actions
    getReactivationsByClinic,
    getReactivationStats,
    getReactivationSummary,
    createReactivation,
    updateReactivationStatus,
    deleteReactivation,
    getAllReactivations,
    getReactivationById,
    
    // Utility functions
    clearError,
    clearData,
    getReactivationsByStatus,
    getRecentReactivations,
    getReactivationCountByStatus,
  };
}
