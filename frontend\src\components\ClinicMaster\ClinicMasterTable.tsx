import React, { useState } from "react";
import Table from "@/components/CommonComponents/Table";
import Pagination from "@/components/CommonComponents/Pagination";
import { Eye, Edit, Trash2, Calendar } from "lucide-react";
import { clinicTableColumns } from '@/utils/column';
import ConfirmDeleteDialog from '@/components/CommonComponents/ConfirmDeleteDialog';
// Define Clinic type locally since we're not using supabaseService
interface Clinic {
  id: number;
  name?: string;
  clinic_name?: string;
  address?: string;
  location?: string;
  email?: string;
  clinic_email?: string;
  contact_number?: string;
  clinic_phonenumber?: string;
  working_hours?: string;
  workingHours?: string;
  working_days?: string;
  workingDays?: string;
  timezone?: string;
  is_active?: boolean;
  sms_service?: boolean;
  email_service?: boolean;
  is_deleted?: boolean;
}

interface ClinicMasterTableProps {
  clinics: Clinic[];
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onViewClinic: (clinic: Clinic) => void;
  onEditClinic?: (clinic: Clinic) => void;
  onDeleteClinic?: (clinic: Clinic) => void;
  onReactivationSchedule?: (clinic: Clinic) => void;
  variant?: 'striped' | 'hover' | 'bordered';
}

const ClinicMasterTable: React.FC<ClinicMasterTableProps> = ({
  clinics,
  page,
  pageSize,
  total,
  onPageChange,
  onViewClinic,
  onEditClinic,
  onDeleteClinic,
  onReactivationSchedule,
  variant = 'striped',
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [clinicToDelete, setClinicToDelete] = useState<Clinic | null>(null);

  const handleActions = (row: Record<string, unknown>) => {
    const clinic = row as unknown as Clinic;
    return (
      <div className="flex gap-3 items-center">
        <button
          className="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded-full hover:bg-blue-50"
          onClick={() => onReactivationSchedule && onReactivationSchedule(clinic)}
          title="Reactivation Schedule"
          style={{ background: 'none', border: 'none' }}
        >
          <Calendar className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => onViewClinic(clinic)}
          title="View"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Eye className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => onEditClinic && onEditClinic(clinic)}
          title="Edit"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Edit className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => { setClinicToDelete(clinic); setDeleteDialogOpen(true); }}
          title="Delete"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>
    );
  };

  return (
    <div>
      <Table
        columns={clinicTableColumns}
        data={clinics as unknown as Record<string, unknown>[]}
        variant={variant}
        onActions={handleActions}
      />
      <div className="mt-4 flex justify-end">
        <Pagination
          currentPage={page}
          totalPages={Math.ceil(total / pageSize)}
          onPageChange={onPageChange}
        />
      </div>
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Clinic"
        description={<span>Are you sure you want to delete <b>{clinicToDelete ? (clinicToDelete.name || '') : ''}</b>? This action cannot be undone.</span>}
        onConfirm={() => { if (onDeleteClinic && clinicToDelete) onDeleteClinic(clinicToDelete); setClinicToDelete(null); }}
      />
    </div>
  );
};

export default ClinicMasterTable; 