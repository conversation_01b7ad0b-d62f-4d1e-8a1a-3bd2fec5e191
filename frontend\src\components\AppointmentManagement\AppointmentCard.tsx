import React from "react";
import { Appointment } from "@/hooks/useAppointment";
import { User, Calendar, Clock, Phone, Edit } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatPhoneNumber } from "@/utils/commonFunctions";
import {
  getAppointmentStatusColor,
  getAppointmentTypeColor,
  capitalizeFirstLetter,
  UNKNOWN_PATIENT,
  UNASSIGNED_DOCTOR,
  NA_VALUE,
  LABEL_VIEW_DETAILS,
} from "@/Constants/Appointment";

interface AppointmentCardProps {
  appointment: Appointment;
  onSelect: (appointment: Appointment) => void;
  onEdit: (appointment: Appointment) => void;
}

const AppointmentCard: React.FC<AppointmentCardProps> = ({
  appointment,
  onSelect,
  onEdit,
}) => {
  return (
    <div
      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-lg transition-all duration-300 bg-white animate-in fade-in-0 hover:scale-[1.02] hover:-translate-y-1 focus-within:ring-2 focus-within:ring-blue-400 focus-within:ring-offset-2"
      tabIndex={0}
      role="button"
      aria-label={`${LABEL_VIEW_DETAILS} ${
        appointment.patient ? `${appointment.patient.first_name} ${appointment.patient.last_name}` : UNKNOWN_PATIENT
      }`}
      onClick={() => onSelect(appointment)}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") onSelect(appointment);
      }}
    >
      {/* Left: Patient avatar and info */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full">
          <User className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">
            {appointment.patient ? `${appointment.patient.first_name} ${appointment.patient.last_name}` : UNKNOWN_PATIENT}
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            {/* Appointment date */}
            <span className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              {appointment.appointment_date
                ? new Date(appointment.appointment_date).toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })
                : NA_VALUE}
            </span>
            {/* Appointment time */}
            <span className="flex items-center">
              <Clock className="h-4 w-4 mr-1" />
              {appointment.appointment_time
                ? new Date(appointment.appointment_time).toLocaleTimeString("en-US", {
                    hour: "numeric",
                    minute: "numeric",
                    hour12: true,
                  })
                : NA_VALUE}
            </span>
            {/* Doctor name */}
            <span>{appointment.doctor?.doctor_name || UNASSIGNED_DOCTOR}</span>
            {/* Patient phone */}
            <span className="flex items-center">
              <Phone className="h-4 w-4 mr-1" />
              {appointment.patient?.phone_number ? formatPhoneNumber(appointment.patient.phone_number) : NA_VALUE}
            </span>
          </div>
        </div>
      </div>
      {/* Right: Status/type badges and edit icon */}
      <div className="flex items-center space-x-3">
        <Badge
          variant="outline"
          className={
            getAppointmentTypeColor(appointment.source || "") +
            " transition-colors duration-200 hover:bg-blue-200 hover:text-blue-900"
          }
        >
          {capitalizeFirstLetter(appointment.source || "Standard")}
        </Badge>
        <Badge
          variant="outline"
          className={
            getAppointmentStatusColor(appointment.status || "") +
            " transition-colors duration-200 hover:bg-green-200 hover:text-green-900"
          }
        >
          {capitalizeFirstLetter(appointment.status || "Unknown")}
        </Badge>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onEdit(appointment);
          }}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
          aria-label="Edit appointment"
        >
          <Edit className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default AppointmentCard;
