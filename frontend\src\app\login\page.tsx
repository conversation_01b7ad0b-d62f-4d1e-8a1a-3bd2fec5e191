"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Toaster } from "@/components/ui/toaster";
import { Mail, Lock, Eye, EyeOff, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { useAuth } from "@/components/contexts/AuthContext";
import {
  LOGIN_CONSTANTS,
  VALIDATION_MESSAGES,
  TOAST_MESSAGES,
  FORM_FIELDS,
  FORM_IDS,
  ACCESSIBILITY,
  ASSETS,
  ANIMATION,
  INPUT_TYPES,
  BUTTON_TYPES,
  FORM_ATTRIBUTES,
  ROUTES,
} from "@/Constants/Login";
import { ERROR_MESSAGES } from "@/Constants/HooksAPI";
import styles from "./Login.module.css";
import { useRouter } from "next/navigation";
import { toastSuccess, toastError } from "@/hooks/use-toast";

const loginSchema = z.object({
  [FORM_FIELDS.EMAIL]: z
    .string()
    .email({ message: VALIDATION_MESSAGES.EMAIL_INVALID }),
  [FORM_FIELDS.PASSWORD]: z
    .string()
    .min(1, { message: VALIDATION_MESSAGES.PASSWORD_REQUIRED }),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const { login, loading } = useAuth();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({ resolver: zodResolver(loginSchema) });

  const onSubmit = async (data: LoginFormData) => {
    try {
      const result = await login(
        data[FORM_FIELDS.EMAIL],
        data[FORM_FIELDS.PASSWORD]
      );
      if (result.success) {
        toastSuccess(TOAST_MESSAGES.LOGIN_SUCCESS);
        router.push(ROUTES.DASHBOARD);
      } else {
        toastError(result.error || ERROR_MESSAGES.AUTHENTICATION_FAILED);
      }
    } catch {
      toastError(ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
    }
  };

  return (
    <div className={styles.loginContainer}>
      <Toaster />
      <motion.div
        initial={{
          opacity: ANIMATION.INITIAL_OPACITY,
          scale: ANIMATION.INITIAL_SCALE,
        }}
        animate={{
          opacity: ANIMATION.FINAL_OPACITY,
          scale: ANIMATION.FINAL_SCALE,
        }}
        transition={{ duration: ANIMATION.DURATION }}
        className={styles.loginMotion}
      >
        <Card className={styles.loginCard}>
          <CardHeader className={styles.loginHeader}>
            <Image
              src={ASSETS.LOGO_SRC}
              alt={ACCESSIBILITY.DENSY_AI_LOGO}
              width={ASSETS.LOGO_WIDTH}
              height={ASSETS.LOGO_HEIGHT}
              className={styles.loginLogo}
              priority
            />
            <p className={styles.loginSubtitle}>{LOGIN_CONSTANTS.SUBTITLE}</p>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className={styles.loginForm}
            >
              <div>
                <Label htmlFor={FORM_IDS.EMAIL} className="text-sm font-medium">
                  {LOGIN_CONSTANTS.EMAIL_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.loginIcon}>
                    <Mail size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.EMAIL}
                    type={INPUT_TYPES.EMAIL}
                    aria-label="Email address"
                    placeholder={LOGIN_CONSTANTS.EMAIL_PLACEHOLDER}
                    autoComplete={FORM_ATTRIBUTES.EMAIL_AUTOCOMPLETE}
                    {...register(FORM_FIELDS.EMAIL)}
                    className={styles.loginInput}
                  />
                </div>
                {errors[FORM_FIELDS.EMAIL] && (
                  <span className={styles.loginError}>
                    {errors[FORM_FIELDS.EMAIL]?.message}
                  </span>
                )}
              </div>
              <div>
                <Label
                  htmlFor={FORM_IDS.PASSWORD}
                  className="text-sm font-medium"
                >
                  {LOGIN_CONSTANTS.PASSWORD_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.loginIcon}>
                    <Lock size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.PASSWORD}
                    type={
                      showPassword ? INPUT_TYPES.TEXT : INPUT_TYPES.PASSWORD
                    }
                    aria-label="Password"
                    placeholder={LOGIN_CONSTANTS.PASSWORD_PLACEHOLDER}
                    autoComplete={FORM_ATTRIBUTES.CURRENT_PASSWORD_AUTOCOMPLETE}
                    {...register(FORM_FIELDS.PASSWORD)}
                    className={styles.loginInputPassword}
                  />
                  <button
                    type={BUTTON_TYPES.BUTTON}
                    aria-label={
                      showPassword
                        ? ACCESSIBILITY.HIDE_PASSWORD
                        : ACCESSIBILITY.SHOW_PASSWORD
                    }
                    className={styles.loginPasswordToggle}
                    onClick={() => setShowPassword((v) => !v)}
                    tabIndex={-1}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors[FORM_FIELDS.PASSWORD] && (
                  <span className={styles.loginError}>
                    {errors[FORM_FIELDS.PASSWORD]?.message}
                  </span>
                )}
              </div>
              <div className="flex justify-end">
                <Link
                  href={ROUTES.FORGOT_PASSWORD}
                  className={styles.loginForgot}
                >
                  {LOGIN_CONSTANTS.FORGOT_PASSWORD}
                </Link>
              </div>
              <Button
                type={BUTTON_TYPES.SUBMIT}
                className={styles.loginButton}
                disabled={isSubmitting || loading}
                aria-label={ACCESSIBILITY.SIGN_IN}
              >
                {isSubmitting || loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={18} />
                    {LOGIN_CONSTANTS.LOADING}
                  </span>
                ) : (
                  LOGIN_CONSTANTS.LOGIN
                )}
              </Button>
            </form>
            <div className={styles.loginRegister}>
              <span>
                {LOGIN_CONSTANTS.REGISTER_PROMPT}{" "}
                <Link
                  href={ROUTES.REGISTER}
                  className={styles.loginRegisterLink}
                >
                  {LOGIN_CONSTANTS.REGISTER_LINK}
                </Link>
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
  
