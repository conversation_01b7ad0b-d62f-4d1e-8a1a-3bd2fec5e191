version: '3.8'

services:
  # MongoDB Services
  mongo_db:
    container_name: db_container
    image: mongo:latest
    restart: always
    volumes:
      - mongo_db:/data/db
    ports:
      - '2717:27017'

  # Node Api Services
  api:
    build: .
    ports:
      - '3000:3000'
    volumes:
      - .:/usr/src/app
    environment:
      PORT: 3000
      DB_TYPE: mongodb
      MONGODB_URI: mongodb://mongo_db:27017/Mobio-Node-Init
      MONGODB_DATABASE_NAME: Mobio-Node-Init
    depends_on:
      - mongo_db

volumes:
  mongo_db: {}
