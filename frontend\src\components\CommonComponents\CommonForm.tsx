// CommonForm.tsx
// Renders a reusable modal form for patient, doctor, clinic, or campaign data entry.
// Dynamically builds fields and handles validation, submission, and layout.

import React, { useState } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import styles from './PhoneInput.module.css';
import { normalizePhoneForAPI, validatePhoneNumber } from '@/utils/phoneUtils';
import {
  FORM_TITLES,
  FORM_BUTTONS,
  FIELD_TYPES,
  FORM_VALIDATION,
} from '@/Constants/CommonComponents';

/**
 * Describes a single form field for CommonForm
 * @property name - Field name (key in form data)
 * @property label - Field label
 * @property type - Field type (text, select, textarea, etc.)
 * @property required - Whether the field is required
 * @property placeholder - Optional placeholder text
 * @property options - For select fields, array of options
 * @property defaultValue - Default value for the field
 * @property validation - Optional custom validation function
 * @property gridCols - Optional grid column span for layout
 */
interface FormField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  defaultValue?: string | boolean;
  validation?: (value: string) => string | null;
  gridCols?: number;
  isMulti?: boolean; // Added for multi-select
  rows?: number; // Added for textarea rows
  country?: string; // Added for phone input country
  enableAreaCodes?: boolean; // Added for phone input area codes
}

/**
 * Props for the CommonForm component
 * @property isOpen - Whether the dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission
 * @property formType - Type of form ('patient', 'doctor', 'clinic', 'campaign')
 * @property title - Optional custom dialog title
 * @property submitButtonText - Optional custom submit button text
 * @property fields - Array of FormField objects
 * @property initialData - Optional initial values for the form
 */
interface CommonFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Record<string, unknown>) => void;
  formType: 'patient' | 'doctor' | 'clinic' | 'campaign' | 'template' | 'appointment';
  title?: string;
  submitButtonText?: string;
  fields: FormField[];
  initialData?: Record<string, string | boolean>;
}

/**
 * Renders a modal form for adding/editing patients, doctors, clinics, or campaigns.
 * Dynamically builds fields, validates input, and processes submission based on formType.
 */
const CommonForm: React.FC<CommonFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  formType,
  title,
  submitButtonText,
  fields,
  initialData = {},
}) => {
  const [formData, setFormData] = useState<Record<string, string | boolean>>(() => {
    const defaultData: Record<string, string | boolean> = {};
    fields.forEach(field => {
      defaultData[field.name] = initialData[field.name] || field.defaultValue || '';
    });
    return defaultData;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = (name: string, value: string | boolean): string | null => {
    const field = fields.find(f => f.name === name);
    if (!field) return null;

    // Use custom validation if provided
    if (field.validation) {
      return field.validation(value as string);
    }

    // Fallback to basic validation
    if (field.required && (name!=='clinic_id' && !value)) {
      return FORM_VALIDATION.REQUIRED;
    }

    if (field.type === FIELD_TYPES.EMAIL && value && typeof value === 'string') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return FORM_VALIDATION.INVALID_EMAIL;
      }
    }

    if (field.type === FIELD_TYPES.NUMBER && value && typeof value === 'string') {
      if (isNaN(Number(value)) || Number(value) < 0) {
        return 'Please enter a valid number';
      }
    }

    if (field.type === FIELD_TYPES.PHONE && value && typeof value === 'string') {
      const phoneValidation = validatePhoneNumber(value);
      if (!phoneValidation.isValid) {
        return phoneValidation.error || 'Invalid phone number';
      }
    }

    return null;
  };

  const handleFieldChange = (name: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing (don't validate on every keystroke)
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFieldBlur = (name: string, value: string | boolean) => {
    // Validate field on blur
    const error = validateField(name, value);
    setErrors(prev => ({ ...prev, [name]: error || '' }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate all fields
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    fields.forEach(field => {
      const error = validateField(field.name, formData[field.name]);
      if (error) {
        newErrors[field.name] = error;
        hasErrors = true;
      }
    });

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Process form data based on form type
    let processedData: Record<string, unknown> = { ...formData };

    switch (formType) {
      case 'patient':
        processedData = {
          first_name: formData.first_name as string,
          last_name: formData.last_name as string,
          dob: formData.dob as string,
          clinic_id: formData.clinic_id as string,
          doctors: [formData.doctor_id as string],
          gender: formData.gender as string,
          phone_number: normalizePhoneForAPI(formData.phone_number as string),
          address: formData.address as string,
          email: formData.email as string,
          preferences: null, // Set to null as it's optional and not provided in form
        };
        break;
      case 'doctor':
        processedData = {
          id: Date.now(),
          name: formData.name as string,
          specialization: formData.specialization as string,
          qualification: formData.qualification as string,
          phone: normalizePhoneForAPI(formData.phone as string),
          email: formData.email as string,
          clinic: formData.clinic as string,
          consultationHours: formData.consultationHours as string,
          workingDays: formData.workingDays as string,
          experience: formData.experience as string,
          patients: 0,
          status: 'Available',
          nextAvailable: 'Now',
        };
        break;
      case 'clinic':
        // Pass raw formData for clinic so parent can map to API
        processedData = { 
          ...formData,
          phone: normalizePhoneForAPI(formData.phone as string)
        };
        break;
      case 'campaign':
        processedData = {
          id: Date.now(),
          name: formData.name as string,
          status: 'Scheduled',
          startDate: formData.startDate as string,
          endDate: formData.endDate as string,
          targetPatients: parseInt(formData.targetPatients as string),
          contactedPatients: 0,
          successfulBookings: 0,
          successRate: '0%',
          lastRun: 'Not started',
        };
        break;
      case 'template':
        processedData = {
          id: Date.now(),
          name: formData.name as string,
          type: formData.type as string,
          content: formData.content as string,
          createdDate: new Date().toISOString().split('T')[0],
        };
        break;
    }

    onSubmit(processedData);
    
    // Reset form
    const resetData: Record<string, string | boolean> = {};
    fields.forEach(field => {
      resetData[field.name] = field.defaultValue || '';
    });
    setFormData(resetData);
    setErrors({});
    onClose();
  };

  const renderField = (field: FormField) => {
    const fieldError = errors[field.name];
    const errorClass = fieldError ? 'border-red-500' : 'border-gray-200';
    const inputClass = errorClass;
    const selectTextareaClass = `w-full p-2 border rounded-md ${errorClass}`;
    
    // Create props for text-based inputs (text, email, number, etc.)
    const textInputProps = {
      id: field.name,
      value: (typeof formData[field.name] === 'string' ? formData[field.name] : '') as string,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => 
        handleFieldChange(field.name, e.target.value),
      onBlur: (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => 
        handleFieldBlur(field.name, e.target.value),
      placeholder: field.placeholder,
    };



    switch (field.type) {
      case FIELD_TYPES.SELECT:
        return (
          <select {...textInputProps} className={selectTextareaClass}>
            <option value="">Select {field.label}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      case FIELD_TYPES.TEXTAREA:
        return (
          <Textarea
            {...textInputProps}
            rows={field.rows || 3}
            className={selectTextareaClass}
          />
        );
      case FIELD_TYPES.PHONE:
        const initialCountry = field.country || 'us'; // Use field country or default to 'us'
        const phoneErrorClass = fieldError ? styles.error : '';
        return (
          <div className={`${styles.phoneInputContainer} ${phoneErrorClass}`}>
            <PhoneInput
              country={initialCountry}
              value={(typeof formData[field.name] === 'string' ? formData[field.name] : '') as string}
              onChange={(phone: string) => handleFieldChange(field.name, phone)}
              onBlur={() => handleFieldBlur(field.name, (typeof formData[field.name] === 'string' ? formData[field.name] : '') as string)}
              placeholder={field.placeholder}
              enableAreaCodes={field.enableAreaCodes || false}
              inputProps={{
                required: field.required,
              }}
              enableSearch={true}
              disableSearchIcon={false}
              searchPlaceholder="Search country..."
              preferredCountries={['us', 'in', 'gb', 'ca', 'au']}
            />
          </div>
        );
      case FIELD_TYPES.CHECKBOX:
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={field.name}
              checked={formData[field.name] === true}
              onCheckedChange={(checked) => handleFieldChange(field.name, checked === true)}
            />
            <Label htmlFor={field.name} className="text-sm font-normal">
              Enable {field.label}
            </Label>
          </div>
        );
      default:
        return <Input {...textInputProps} className={inputClass} type={field.type} />;
    }
  };

  const renderFieldGroup = (fieldGroup: FormField[]) => {
    if (fieldGroup.length === 1) {
      const field = fieldGroup[0];
      return (
        <div key={field.name}>
          <Label htmlFor={field.name} className="font-semibold">{field.label}</Label>
          {renderField(field)}
          {errors[field.name] && (
            <p className="text-red-500 text-sm mt-1 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors[field.name]}
            </p>
          )}
        </div>
      );
    }

    return (
      <div key={fieldGroup[0].name} className={`grid grid-cols-${fieldGroup[0].gridCols || 2} gap-4`}>
        {fieldGroup.map(field => (
          <div key={field.name}>
            <Label htmlFor={field.name} className="font-semibold">{field.label}</Label>
            {renderField(field)}
            {errors[field.name] && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors[field.name]}
              </p>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Group fields by grid layout
  const groupedFields: FormField[][] = [];
  let i = 0;

  while (i < fields.length) {
    const currentField = fields[i];
    const nextField = fields[i + 1];

    // If current field has gridCols > 1 and next field also has gridCols > 1
    if (currentField.gridCols && currentField.gridCols > 1 && 
        nextField && nextField.gridCols && nextField.gridCols > 1) {
      // Group these two fields together
      groupedFields.push([currentField, nextField]);
      i += 2; // Skip both fields
    } else if (currentField.gridCols && currentField.gridCols > 1) {
      // Single field with gridCols > 1 (shouldn't happen with our config, but handle it)
      groupedFields.push([currentField]);
      i += 1;
    } else {
      // Single field without gridCols
      groupedFields.push([currentField]);
      i += 1;
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{title || FORM_TITLES[`ADD_${formType.toUpperCase()}` as keyof typeof FORM_TITLES]}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 p-2 max-h-[80vh] overflow-y-auto" noValidate>
          {groupedFields.map((fieldGroup) => renderFieldGroup(fieldGroup))}
          
          <div className="flex gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose} className="flex-1">
              {FORM_BUTTONS.CANCEL}
            </Button>
            <Button type="submit" variant="main" className="flex-1">
              {submitButtonText || FORM_BUTTONS[`ADD_${formType.toUpperCase()}` as keyof typeof FORM_BUTTONS]}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CommonForm; 