import jwt from 'jsonwebtoken';
import * as jwtUtil from '../utils/jwt.util.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';

/**
 * Middleware to verify JWT access token
 */

export const verifyToken = (req, res, next) => {
  let token;
  const authHeader = req.headers['authorization'];
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
  } else if (req.cookies && req.cookies[constants.ACCESS_TOKEN_COOKIE_NAME]) {
    token = req.cookies[constants.ACCESS_TOKEN_COOKIE_NAME];
  }

  if (!token) {
    logger.warn(loggerMessages.AUTH_HEADER_MISSING);
    return res
      .status(status.STATUS_CODE_UNAUTHORIZED)
      .json(errorResponse(constants.TOKEN_REQUIRED));
  }
  try {
    const decoded = jwtUtil.verifyAccessToken(token);
    req.user = decoded;
    logger.info(loggerMessages.TOKEN_VERIFIED(decoded.id));
    next();
  } catch (err) {
    const errorMessage =
      err.name === 'TokenExpiredError'
        ? constants.TOKEN_EXPIRED
        : constants.INVALID_TOKEN;
    logger.error(loggerMessages.TOKEN_VERIFICATION_FAILED(errorMessage), {
      error: err,
    });
    return res
      .status(status.STATUS_CODE_UNAUTHORIZED)
      .json(errorResponse(errorMessage));
  }
};
