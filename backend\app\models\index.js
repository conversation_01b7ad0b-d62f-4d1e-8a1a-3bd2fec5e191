import { sequelize } from '../config/aws-config.js';
import UserModel from './user.model.js';
import RoleModel from './role.model.js';
import ClinicModel from './clinic.model.js';
import DoctorModel from './doctor.model.js';
import PatientModel from './patient.model.js';
import AppointmentModel from './appointment.model.js';
import TemplateModel from './template.model.js';
import CampaignModel from './campaign.model.js';
import EscalationModel from './escalation.model.js';
import TokenModel from './token.model.js';
import AICallLogModel from './ai_call_log.model.js';
import UserAuditLogModel from './user_audit_log.model.js';
import ClinicAuditLogModel from './clinic_audit_log.model.js';
import DoctorAuditLogModel from './doctor_audit_log.model.js';
import PatientAuditLogModel from './patient_audit_log.model.js';
import AppointmentAuditLogModel from './appointment_audit_log.model.js';
import TemplateAuditLogModel from './template_audit_log.model.js';
import CampaignAuditLogModel from './campaign_audit_log.model.js';
import EscalationAuditLogModel from './escalation_audit_log.model.js';
import ReactivationModel from './reactivation.model.js';


const User = UserModel(sequelize);
const Role = RoleModel(sequelize);
const Clinic = ClinicModel(sequelize);
const Doctor = DoctorModel(sequelize);
const Patient = PatientModel(sequelize);
const Appointment = AppointmentModel(sequelize);
const Template = TemplateModel(sequelize);
const Campaign = CampaignModel(sequelize);
const Escalation = EscalationModel(sequelize);
const Token = TokenModel(sequelize);
const AICallLog = AICallLogModel(sequelize);
const UserAuditLog = UserAuditLogModel(sequelize);
const ClinicAuditLog = ClinicAuditLogModel(sequelize);
const DoctorAuditLog = DoctorAuditLogModel(sequelize);
const PatientAuditLog = PatientAuditLogModel(sequelize);
const AppointmentAuditLog = AppointmentAuditLogModel(sequelize);
const TemplateAuditLog = TemplateAuditLogModel(sequelize);
const CampaignAuditLog = CampaignAuditLogModel(sequelize);
const EscalationAuditLog = EscalationAuditLogModel(sequelize);
const Reactivation = ReactivationModel(sequelize);


// Set up associations
Role.hasMany(User, { foreignKey: 'role_id' });
User.belongsTo(Role, { foreignKey: 'role_id' });

User.hasMany(Token, { foreignKey: 'userId', as: 'tokens' });
Token.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Clinic.hasMany(Doctor, { foreignKey: 'clinic_id' });
Doctor.belongsTo(Clinic, { foreignKey: 'clinic_id' });

Clinic.hasMany(Patient, { foreignKey: 'clinic_id' });
Patient.belongsTo(Clinic, { foreignKey: 'clinic_id' });

User.hasMany(Doctor, { foreignKey: 'created_by', as: 'CreatedDoctors' });
User.hasMany(Doctor, { foreignKey: 'updated_by', as: 'UpdatedDoctors' });
Doctor.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Doctor.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

User.hasMany(Patient, { foreignKey: 'created_by', as: 'CreatedPatients' });
User.hasMany(Patient, { foreignKey: 'updated_by', as: 'UpdatedPatients' });
Patient.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Patient.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

Clinic.hasMany(Appointment, { foreignKey: 'clinic_id' });
Appointment.belongsTo(Clinic, { foreignKey: 'clinic_id' });

Patient.hasMany(Appointment, { foreignKey: 'patient_id' });
Appointment.belongsTo(Patient, { foreignKey: 'patient_id' });

Doctor.hasMany(Appointment, { foreignKey: 'doctor_id' });
Appointment.belongsTo(Doctor, { foreignKey: 'doctor_id' });

User.hasMany(Appointment, { foreignKey: 'created_by', as: 'CreatedAppointments' });
User.hasMany(Appointment, { foreignKey: 'updated_by', as: 'UpdatedAppointments' });
Appointment.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Appointment.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

User.hasMany(Template, { foreignKey: 'created_by', as: 'CreatedTemplates' });
User.hasMany(Template, { foreignKey: 'updated_by', as: 'UpdatedTemplates' });
Template.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Template.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

Clinic.hasMany(Campaign, { foreignKey: 'clinic_id' });
Campaign.belongsTo(Clinic, { foreignKey: 'clinic_id' });

User.hasMany(Campaign, { foreignKey: 'created_by', as: 'CreatedCampaigns' });
User.hasMany(Campaign, { foreignKey: 'updated_by', as: 'UpdatedCampaigns' });
Campaign.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Campaign.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

// Escalation associations
Clinic.hasMany(Escalation, { foreignKey: 'clinic_id' });
Escalation.belongsTo(Clinic, { foreignKey: 'clinic_id' });

Patient.hasMany(Escalation, { foreignKey: 'patient_id' });
Escalation.belongsTo(Patient, { foreignKey: 'patient_id' });

User.hasMany(Escalation, { foreignKey: 'assignee_id', as: 'AssignedEscalations' });
User.hasMany(Escalation, { foreignKey: 'created_by', as: 'CreatedEscalations' });
User.hasMany(Escalation, { foreignKey: 'updated_by', as: 'UpdatedEscalations' });
Escalation.belongsTo(User, { foreignKey: 'assignee_id', as: 'Assignee' });
Escalation.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Escalation.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

// AICallLog associations
Clinic.hasMany(AICallLog, { foreignKey: 'clinic_id' });
AICallLog.belongsTo(Clinic, { foreignKey: 'clinic_id' });

Patient.hasMany(AICallLog, { foreignKey: 'patient_id' });
AICallLog.belongsTo(Patient, { foreignKey: 'patient_id' });

User.hasMany(AICallLog, { foreignKey: 'created_by', as: 'CreatedAICallLogs' });
User.hasMany(AICallLog, { foreignKey: 'updated_by', as: 'UpdatedAICallLogs' });
AICallLog.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
AICallLog.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });

// Reactivation associations
Clinic.hasMany(Reactivation, { foreignKey: 'clinic_id', as: 'Reactivations' });
Reactivation.belongsTo(Clinic, { foreignKey: 'clinic_id', as: 'Clinic' });

User.hasMany(Reactivation, { foreignKey: 'created_by', as: 'CreatedReactivations' });
User.hasMany(Reactivation, { foreignKey: 'updated_by', as: 'UpdatedReactivations' });
Reactivation.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
Reactivation.belongsTo(User, { foreignKey: 'updated_by', as: 'Updater' });



// AppointmentAuditLog associations
User.hasMany(AppointmentAuditLog, { foreignKey: 'user_id' });
AppointmentAuditLog.belongsTo(User, { foreignKey: 'user_id' });

export {
  sequelize,
  User,
  Role,
  Clinic,
  Doctor,
  Token,
  Patient,
  Appointment,
  Template,
  Campaign,
  Escalation,
  AICallLog,
  UserAuditLog,
  ClinicAuditLog,
  DoctorAuditLog,
  PatientAuditLog,
  AppointmentAuditLog,
  TemplateAuditLog,
  CampaignAuditLog,
  EscalationAuditLog,
  Reactivation,
};
