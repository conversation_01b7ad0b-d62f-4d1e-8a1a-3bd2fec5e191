import { apiRequest } from "../utils/axios.utils";
import { extractErrorMessage, compactErrorMessage, compactSuccessMessage } from "@/utils/commonFunctions";
import {
  ESCALATION_CREATE_FAILED,
  ESCALATION_UPDATE_FAILED,
  ESCALATION_DELETE_FAILED,
  ESCALATION_FETCH_FAILED,
  ESCALATIONS_FETCH_FAILED,
} from "@/Constants/Escalation";

const ESCALATION_CREATE_ENDPOINT = "/escalation/create-with-transcript";
const ESCALATION_LIST_ENDPOINT = "/escalation";
const ESCALATION_DETAIL_ENDPOINT = (id: number) => `/escalation/${id}`;

export interface CreateEscalationPayload {
  clinic_id: number;
  patient_id: number;
  summary: string;
  status: string;
  tags: string;
  assignee_id: number;
  conversation_id: string;
  api_key: string;
}

export interface EscalationData {
  id: number;
  status: string;
  created_at: string;
  updated_at: string;
  clinic_id: number;
  patient_id: number;
  summary: string;
  tags: string;
  assignee_id: number;
  created_by: number;
  updated_by: number;
  patient_name?: string;
  patient_phone?: string;
  assignee_name?: string;
  clinic_name?: string;
  transcript?: Array<string | {
    role: string;
    message: string;
    time_in_call_secs: number;
  }>;
}

export const useEscalation = () => {
  const createEscalation = async (escalationData: CreateEscalationPayload) => {
    try {
      const response = await apiRequest.post(ESCALATION_CREATE_ENDPOINT, escalationData) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || "Escalation created successfully");
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || ESCALATION_CREATE_FAILED);
        return {
          success: false,
          error: response?.message || ESCALATION_CREATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ESCALATION_CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const getEscalations = async (params?: { 
    is_deleted?: boolean; 
    is_active?: boolean;
    status?: string;
    clinic_id?: number;
  }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.is_deleted !== undefined) {
        queryParams.append('is_deleted', params.is_deleted.toString());
      }
      if (params?.is_active !== undefined) {
        queryParams.append('is_active', params.is_active.toString());
      }
      if (params?.status) {
        queryParams.append('status', params.status);
      }
      if (params?.clinic_id) {
        queryParams.append('clinic_id', params.clinic_id.toString());
      }
      
      const endpoint = queryParams.toString() 
        ? `${ESCALATION_LIST_ENDPOINT}?${queryParams.toString()}`
        : ESCALATION_LIST_ENDPOINT;
        
      const response = await apiRequest.get(endpoint) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response?.message || ESCALATIONS_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ESCALATIONS_FETCH_FAILED);
      return { success: false, error: errorMsg };
    }
  };

  const getEscalationById = async (id: number) => {
    try {
      const response = await apiRequest.get(ESCALATION_DETAIL_ENDPOINT(id)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        return {
          success: false,
          error: response?.message || ESCALATION_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ESCALATION_FETCH_FAILED);
      return { success: false, error: errorMsg };
    }
  };

  const updateEscalation = async (id: number, escalationData: Record<string, unknown>) => {
    try {
      const response = await apiRequest.put(ESCALATION_DETAIL_ENDPOINT(id), escalationData) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || "Escalation updated successfully");
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || ESCALATION_UPDATE_FAILED);
        return {
          success: false,
          error: response?.message || ESCALATION_UPDATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ESCALATION_UPDATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const deleteEscalation = async (id: number) => {
    try {
      const response = await apiRequest.delete(ESCALATION_DETAIL_ENDPOINT(id)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || "Escalation deleted successfully");
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || ESCALATION_DELETE_FAILED);
        return {
          success: false,
          error: response?.message || ESCALATION_DELETE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ESCALATION_DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  return {
    createEscalation,
    getEscalations,
    getEscalationById,
    updateEscalation,
    deleteEscalation,
  };
}; 