// columns.ts
// Dynamic columns definition for the Clinic table
import { formatPhoneNumber } from "./commonFunctions";

function formatTime(time: string) {
  if (!time) return '-';
  time = time.trim();
  // If already in 12-hour format with AM/PM, return as is
  if (/\b(AM|PM)\b/i.test(time)) return time.toUpperCase();
  const [h, m] = time.split(':');
  let hour = parseInt(h, 10);
  const min = m;
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12;
  return `${hour}:${min} ${ampm}`;
}

function formatDays(days: unknown): string {
  if (!days) return '-';
  let arr: string[] = [];
  try {
    if (typeof days === 'string') {
      // Try to parse JSON, if fails, treat as comma-separated
      try {
        const parsed = JSON.parse(days);
        if (Array.isArray(parsed)) arr = parsed;
        else arr = days.split(',');
      } catch {
        arr = days.split(',');
      }
    } else if (Array.isArray(days)) {
      arr = days;
    }
    arr = arr.map((d) => {
      const trimmed = d.trim();
      if (!trimmed) return '';
      // If the string contains '-', split and capitalize each part
      if (trimmed.includes('-')) {
        return trimmed.split('-').map(part => {
          const p = part.trim();
          return p.charAt(0).toUpperCase() + p.slice(1).toLowerCase();
        }).join(' - ');
      }
      return trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();
    }).filter(Boolean);
    return arr.length ? arr.join(' - ') : '-';
  } catch {
    return '-';
  }
}

export const clinicTableColumns = [
  { header: 'Clinic Name', accessor: 'clinic_name' },
  { header: 'Location', accessor: 'location' },
  { header: 'Email', accessor: 'clinic_email' },
  { 
    header: 'Phone', 
    accessor: 'clinic_phonenumber',
    render: (row: Record<string, unknown>) => formatPhoneNumber(String(row.clinic_phonenumber || ''))
  },
  {
    header: 'Working Hours', accessor: 'working_hours',
    render: (row: Record<string, unknown>) => {
      if (!row.working_hours) return '-';
      try {
        const wh = typeof row.working_hours === 'string' ? JSON.parse(row.working_hours) : row.working_hours;
        // Get the first key's value (e.g., monday)
        const firstDay = Object.keys(wh)[0];
        const range = wh[firstDay];
        if (typeof range === 'string' && range.includes('-')) {
          const [start, end] = range.split('-').map((t: string) => t.trim());
          return `${formatTime(start)} - ${formatTime(end)}`;
        }
        return range;
      } catch {
        return typeof row.working_hours === 'string' ? row.working_hours : '-';
      }
    }
  },
  {
    header: 'Working Days', accessor: 'working_days',
    render: (row: Record<string, unknown>) => formatDays(row.working_days)
  },
];

export const doctorTableColumns = (clinics: { id: number; clinic_name: string }[]) => [
  { header: 'Name', accessor: 'doctor_name' },
  { header: 'Specialization', accessor: 'specialization' },
  { header: 'Qualification', accessor: 'qualification' },
  {
    header: 'Clinic',
    accessor: 'clinic_id',
    render: (row: Record<string, unknown>) => {
      const clinic = clinics.find(c => c.id === row.clinic_id);
      return clinic ? clinic.clinic_name : 'N/A';
    },
  },
  { 
    header: 'Phone', 
    accessor: 'doctor_phonenumber',
    render: (row: Record<string, unknown>) => formatPhoneNumber(String(row.doctor_phonenumber || ''))
  },
  { header: 'Email', accessor: 'doctor_email' },
  {
    header: 'Experience',
    accessor: 'experience_years',
    render: (row: Record<string, unknown>) =>
      row.experience_years !== undefined ? `${row.experience_years} years` : '-',
  },
];

export const patientTableColumns = [
  { header: 'Name', accessor: 'name' },
  { header: 'Email', accessor: 'email' },
  { 
    header: 'Phone', 
    accessor: 'phone_number', 
    render: (row: Record<string, unknown>) => formatPhoneNumber(String(row.phone_number || ''))
  },
  { 
    header: 'Gender', 
    accessor: 'gender',
    render: (row: Record<string, unknown>) => {
      const gender = String(row.gender || '');
      return gender ? gender.charAt(0).toUpperCase() + gender.slice(1).toLowerCase() : '-';
    }
  },
  { header: 'Date of Birth', accessor: 'dob' },
  { header: 'Status', accessor: 'status' },
];

export const reactivationCallTableColumns = [
  { header: 'Patient', accessor: 'patientName' },
  { header: 'Contact', accessor: 'phone' },
  { header: 'Last Visit', accessor: 'lastVisit' },
  { header: 'Call Date', accessor: 'callDate' },
  { header: 'Call Time', accessor: 'callTime' },
  { header: 'Outcome', accessor: 'outcome' },
  { header: 'Appointment Date', accessor: 'appointmentDate' },
  { header: 'Notes', accessor: 'notes' },
];

export const templateTableColumns = [
  { header: 'Template Name', accessor: 'name' },
  { header: 'Type', accessor: 'type' },
  { 
    header: 'Content', 
    accessor: 'content',
    render: (row: Record<string, unknown>) => {
      const content = String(row.content || '');
      return content.length > 50 ? `${content.substring(0, 50)}...` : content;
    }
  },
  { 
    header: 'Created Date', 
    accessor: 'created_at',
    render: (row: Record<string, unknown>) => {
      if (!row.created_at) return '-';
      return new Date(String(row.created_at)).toLocaleDateString();
    }
  },
];
