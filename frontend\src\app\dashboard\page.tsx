'use client';

import React, { useState, useEffect, useCallback } from 'react';
import DashboardStatsGrid from '@/components/Dashboard/DashboardStatsGrid';
import DashboardPatientsOverview from '@/components/Dashboard/DashboardPatientsOverview';
import DashboardTotalCalls from '@/components/Dashboard/DashboardTotalCalls';
import DashboardAppointments from '@/components/Dashboard/DashboardAppointments';
import DashboardSchedule from '@/components/Dashboard/DashboardSchedule';
import { useAppointment } from '@/hooks/useAppointment';
import { usePatients } from '@/hooks/usePatients';
import { useDoctor } from '@/hooks/useDoctor';
import { useAICallLog } from '@/hooks/useAICallLog';

interface DashboardStats {
  title: string;
  value: string | number;
  icon: string;
}

interface Doctor {
  is_deleted: boolean;
  [key: string]: unknown;
}

const Dashboard = () => {
  const { appointments, getAllAppointments } = useAppointment();
  const { patients, getAllPatients } = usePatients();
  const { getDoctors } = useDoctor();
  const { aiCallLogs, getAllAICallLogs } = useAICallLog();
  const [loading, setLoading] = useState(true);
  const [refreshKey] = useState(0);
  const [doctorsData, setDoctorsData] = useState<Doctor[]>([]);

  const fetchAllData = useCallback(async () => {
    try {
      setLoading(true);
      const [, , doctorsResult] = await Promise.all([
        getAllAppointments(),
        getAllPatients(),
        getDoctors(),
        getAllAICallLogs(),
      ]);
      
      // Handle doctors data
      if (doctorsResult.success && doctorsResult.data) {
        setDoctorsData(Array.isArray(doctorsResult.data) ? doctorsResult.data : []);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  }, [getAllAppointments, getAllPatients, getDoctors, getAllAICallLogs]);

  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // Calculate AI call metrics
  const totalAICalls = aiCallLogs.length;
  
  // Calculate dashboard stats from real data
  const dashboardStats: DashboardStats[] = [
    {
      title: "Total Appointments",
      value: appointments.length,
      icon: "Calendar"
    },
    {
      title: "Total Doctors",
      value: doctorsData.filter((doc: Doctor) => !doc.is_deleted).length,
      icon: "User"
    },
    {
      title: "Total Patients",
      value: patients.length,
      icon: "Users"
    },
    {
      title: "AI Calls Handled",
      value: totalAICalls,
      icon: "Phone"
    }
  ];

  return (
    <div className="p-8 space-y-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-gray-900">Welcome Back</h1>
          <p className="text-gray-600">Here&apos;s what happen with your dashboard today</p>
        </div>
      </div>

      {/* Stats Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
              <div className="flex items-center justify-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <DashboardStatsGrid stats={dashboardStats} />
      )}

      {/* Charts and Overview - 3 columns */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <DashboardPatientsOverview key={`patients-${refreshKey}`} />
        <DashboardTotalCalls key={`calls-${refreshKey}`} />
        <DashboardAppointments key={`appointments-${refreshKey}`} />
      </div>

      {/* Schedule - Full width */}
      <div className="w-full">
        <DashboardSchedule key={`schedule-${refreshKey}`} />
      </div>
    </div>
  );
};

export default Dashboard;
