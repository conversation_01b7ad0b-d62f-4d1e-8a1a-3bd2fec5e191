/**
 * Email Template Service: Generates email templates for various system emails
 */
import * as constants from '../utils/constants.utils.js';
import { convertUTCToIndianTime } from '../utils/timezone.util.js';

/**
 * Generate appointment confirmation email template
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} doctorData - Doctor data
 * @param {Object} clinicData - Clinic data
 * @returns {Object} Email template with subject, text, and HTML
 */
export const generateAppointmentConfirmationEmail = (appointmentData, patientData, doctorData, clinicData) => {
  // Convert UTC time from database to Indian time for display
  const appointmentDate = convertUTCToIndianTime(appointmentData.appointment_time);
  const formattedDate = appointmentDate.toLocaleDateString('en-IN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const formattedTime = appointmentDate.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const subject = `Appointment Confirmation - ${clinicData.clinic_name}`;
  
  const text = `
Dear ${patientData.first_name} ${patientData.last_name},

Your appointment has been successfully scheduled.

Appointment Details:
- Date: ${formattedDate}
- Time: ${formattedTime}
- Doctor: ${doctorData.doctor_name}
- Clinic: ${clinicData.clinic_name}
- Location: ${clinicData.location || 'Please contact clinic for location'}

Please arrive 15 minutes before your scheduled appointment time.

If you need to reschedule or cancel, please contact us at ${clinicData.clinic_phonenumber}.

Thank you for choosing ${clinicData.clinic_name}.

Best regards,
${clinicData.clinic_name} Team
  `.trim();

  const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; }
        .appointment-details { background-color: white; padding: 15px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #4CAF50; }
        .detail-row { margin: 10px 0; }
        .label { font-weight: bold; color: #555; }
        .value { color: #333; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .contact-info { background-color: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Appointment Confirmation</h1>
        </div>
        
        <div class="content">
            <p>Dear <strong>${patientData.first_name} ${patientData.last_name}</strong>,</p>
            
            <p>Your appointment has been successfully scheduled. Please review the details below:</p>
            
            <div class="appointment-details">
                <div class="detail-row">
                    <span class="label">Date:</span>
                    <span class="value">${formattedDate}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Time:</span>
                    <span class="value">${formattedTime}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Doctor:</span>
                    <span class="value">${doctorData.doctor_name}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Clinic:</span>
                    <span class="value">${clinicData.clinic_name}</span>
                </div>
                ${clinicData.location ? `
                <div class="detail-row">
                    <span class="label">Location:</span>
                    <span class="value">${clinicData.location}</span>
                </div>
                ` : ''}
            </div>
            
            <div class="contact-info">
                <p><strong>Important:</strong> Please arrive 15 minutes before your scheduled appointment time.</p>
            </div>
            
            <p>If you need to reschedule or cancel your appointment, please contact us at:</p>
            <p><strong>Phone:</strong> ${clinicData.clinic_phonenumber}</p>
            <p><strong>Email:</strong> ${clinicData.clinic_email}</p>
            
            <p>Thank you for choosing <strong>${clinicData.clinic_name}</strong>.</p>
            
            <div class="footer">
                <p>Best regards,<br>
                <strong>${clinicData.clinic_name}</strong> Team</p>
            </div>
        </div>
    </div>
</body>
</html>
  `.trim();

  return { subject, text, html };
};

/**
 * Generate appointment cancellation email template
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} clinicData - Clinic data
 * @returns {Object} Email template with subject, text, and HTML
 */
export const generateAppointmentCancellationEmail = (appointmentData, patientData, clinicData) => {
  // Convert UTC time from database to Indian time for display
  const appointmentDate = convertUTCToIndianTime(appointmentData.appointment_time);
  const formattedDate = appointmentDate.toLocaleDateString('en-IN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const formattedTime = appointmentDate.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const subject = `Appointment Cancellation - ${clinicData.clinic_name}`;
  
  const text = `
Dear ${patientData.first_name} ${patientData.last_name},

Your appointment has been cancelled.

Cancelled Appointment Details:
- Date: ${formattedDate}
- Time: ${formattedTime}
- Clinic: ${clinicData.clinic_name}

If you would like to reschedule, please contact us at ${clinicData.clinic_phonenumber}.

Thank you for your understanding.

Best regards,
${clinicData.clinic_name} Team
  `.trim();

  const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Cancellation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f44336; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; }
        .appointment-details { background-color: white; padding: 15px; margin: 20px 0; border-radius: 5px; border-left: 4px solid #f44336; }
        .detail-row { margin: 10px 0; }
        .label { font-weight: bold; color: #555; }
        .value { color: #333; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
        .contact-info { background-color: #ffebee; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Appointment Cancellation</h1>
        </div>
        
        <div class="content">
            <p>Dear <strong>${patientData.first_name} ${patientData.last_name}</strong>,</p>
            
            <p>Your appointment has been cancelled. Please review the details below:</p>
            
            <div class="appointment-details">
                <div class="detail-row">
                    <span class="label">Date:</span>
                    <span class="value">${formattedDate}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Time:</span>
                    <span class="value">${formattedTime}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Clinic:</span>
                    <span class="value">${clinicData.clinic_name}</span>
                </div>
            </div>
            
            <div class="contact-info">
                <p><strong>Need to reschedule?</strong> Please contact us to book a new appointment.</p>
            </div>
            
            <p>If you would like to reschedule, please contact us at:</p>
            <p><strong>Phone:</strong> ${clinicData.clinic_phonenumber}</p>
            <p><strong>Email:</strong> ${clinicData.clinic_email}</p>
            
            <p>Thank you for your understanding.</p>
            
            <div class="footer">
                <p>Best regards,<br>
                <strong>${clinicData.clinic_name}</strong> Team</p>
            </div>
        </div>
    </div>
</body>
</html>
  `.trim();

  return { subject, text, html };
};
