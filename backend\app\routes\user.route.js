import express from 'express';
import * as userController from '../controllers/user.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';
import { validate } from '../middleware/validate.middleware.js';
import { updateUserSchema } from '../validators/user.validator.js';

const router = express.Router();


// Get all users
router.get(
  '/list',
  verifyToken,
  userController.getAllUsers
);

// Get user by ID
router.get(
  '/:id',
  verifyToken,
  userController.getUserById
);

// Update user by ID
router.put(
  '/:id',
  verifyToken,
  validate(updateUserSchema),
  userController.updateUser
);

// Delete user by ID
router.delete(
  '/:id',
  verifyToken,
  userController.deleteUser
);

export default router;
