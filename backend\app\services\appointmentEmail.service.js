/**
 * Appointment Email Service: Handles sending emails related to appointments
 */
import { sendEmail } from './email.service.js';
import { generateAppointmentConfirmationEmail, generateAppointmentCancellationEmail } from './emailTemplate.service.js';
import { sendAppointmentConfirmationSMS, sendAppointmentCancellationSMS, sendAppointmentUpdateSMS } from './sms.service.js';
import * as patientService from './patient.service.js';
import * as doctorService from './doctor.service.js';
import * as clinicService from './clinic.service.js';
import logger from '../config/logger.config.js';
import * as constants from '../utils/constants.utils.js';
import * as loggerMessages from '../utils/log_messages.utils.js';

/**
 * Send appointment confirmation email to patient
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} doctorData - Doctor data
 * @param {Object} clinicData - Clinic data
 * @returns {Promise<boolean>} Success status
 */
export const sendAppointmentConfirmationEmail = async (appointmentData, patientData, doctorData, clinicData) => {
  try {
    // Validate required data
    if (!patientData || !patientData.email) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    if (!doctorData) {
      logger.warn(loggerMessages.DOCTOR_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    if (!clinicData) {
      logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    // Generate email template
    const emailTemplate = generateAppointmentConfirmationEmail(
      appointmentData,
      patientData,
      doctorData,
      clinicData
    );

    // Send email
    await sendEmail(
      patientData.email,
      emailTemplate.subject,
      emailTemplate.text,
      emailTemplate.html
    );

    logger.info(loggerMessages.APPOINTMENT_CONFIRMATION_EMAIL_SENT(patientData, appointmentData));

    return true;
  } catch (error) {
    
    logger.error(constants.APPOINTMENT_CONFIRMATION_EMAIL_FAILED(appointmentData), error);
    return false;
  }
};

/**
 * Send appointment cancellation email to patient
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} clinicData - Clinic data
 * @returns {Promise<boolean>} Success status
 */
export const sendAppointmentCancellationEmail = async (appointmentData, patientData, clinicData) => {
  try {
    // Validate required data
    if (!patientData || !patientData.email) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    if (!clinicData) {
      logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    // Generate email template
    const emailTemplate = generateAppointmentCancellationEmail(
      appointmentData,
      patientData,
      clinicData
    );

    // Send email
    await sendEmail(
      patientData.email,
      emailTemplate.subject,
      emailTemplate.text,
      emailTemplate.html
    );

    logger.info(loggerMessages.APPOINTMENT_CANCELLATION_EMAIL_SENT(patientData, appointmentData));

    return true;
  } catch (error) {
    logger.error(constants.APPOINTMENT_CANCELLATION_EMAIL_FAILED(appointmentData), error);
    return false;
  }
};

/**
 * Send appointment update email to patient
 * @param {Object} appointmentData - Updated appointment data
 * @returns {Promise<boolean>} Success status
 */
export const sendAppointmentUpdateEmail = async (appointmentData) => {
  try {
    // Get patient information
    const patient = await patientService.getPatientById(appointmentData.patient_id);
    if (!patient || !patient.email) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    // Get doctor information
    const doctor = await doctorService.getDoctorById(appointmentData.doctor_id);
    if (!doctor) {
      logger.warn(loggerMessages.DOCTOR_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    // Get clinic information
    const clinic = await clinicService.getClinicById(appointmentData.clinic_id);
    if (!clinic) {
      logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(appointmentData));
      return false;
    }

    logger.info(loggerMessages.APPOINTMENT_UPDATE_EMAIL_SENT(patient, appointmentData));

    return true;
  } catch (error) {
    logger.error(constants.APPOINTMENT_UPDATE_EMAIL_FAILED(appointmentData), error);
    return false;
  }
};
