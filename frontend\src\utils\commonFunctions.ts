import { toast } from "@/hooks/use-toast";

// Regex for route capitalization (adjust as needed)
export const CONVERT_ROUTE_TO_FIRST_LETTER_CAPITALIZE = /\/([a-z-]+)/g;

export const handleErrorResponse = (error: unknown) => {
  const errObj = error as { response?: { data?: { errors?: { msg?: string }[]; message?: string } } };
  const payload = {
    status: false,
    message: errObj?.response?.data?.errors
      ? errObj?.response?.data?.errors[0]?.msg
      : errObj?.response?.data?.message,
  };
  return payload;
};

export const errorMessage = (text: string) => {
  toast({
    title: "Error",
    description: text,
    variant: "destructive",
  });
};

export const successMessage = (text: string) => {
  toast({
    title: "Success",
    description: text,
    variant: "success",
  });
};

export const compactSuccessMessage = (text: string) => {
  toast({
    description: text,
    variant: "compact",
  });
};

export const compactErrorMessage = (text: string) => {
  toast({
    description: text,
    variant: "compact-error",
  });
};

export const convertStringToFirstLetterCapitalize = (pathname: string) => {
  if (pathname === "/") {
    return "Clinic Appointment - Homepage";
  }
  return pathname.replace(
    CONVERT_ROUTE_TO_FIRST_LETTER_CAPITALIZE,
    (match, p1) => {
      return p1
        .split("-")
        .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
    }
  );
};

/**
 * Extracts error message from axios error or any other error
 * @param error - The error object from catch block
 * @param fallbackMessage - Fallback message if no error message is found
 * @returns The error message string
 */
export const extractErrorMessage = (error: unknown, fallbackMessage: string): string => {
  // Handle axios errors that contain API response messages
  if (error && typeof error === "object" && "response" in error) {
    const axiosError = error as { 
      response?: { 
        data?: { 
          message?: string;
          errors?: Array<{ msg?: string; message?: string }>;
        };
        status?: number;
      };
      message?: string;
    };
    
    // Try to get message from response.data.message
    if (axiosError.response?.data?.message) {
      return axiosError.response.data.message;
    }
    
    // Try to get message from response.data.errors array
    if (axiosError.response?.data?.errors && Array.isArray(axiosError.response.data.errors)) {
      const firstError = axiosError.response.data.errors[0];
      if (firstError?.msg) {
        return firstError.msg;
      }
      if (firstError?.message) {
        return firstError.message;
      }
    }
    
    // Fall back to axios error message
    if (axiosError.message) {
      return axiosError.message;
    }
  }
  
  // Handle standard Error objects
  if (error instanceof Error) {
    return error.message;
  }
  
  // Handle string errors
  if (typeof error === "string") {
    return error;
  }
  
  // Return fallback message if no error message found
  return fallbackMessage;
}; 

/**
 * Formats a date string to DD/MM/YYYY format
 * @param dateString - The date string to format (e.g., "2025-08-07")
 * @param fallbackText - Text to show if date is empty (optional)
 * @returns Formatted date string in DD/MM/YYYY format
 */
export const formatDateAsDDMMYYYY = (dateString: string, fallbackText: string = "No date selected"): string => {
  if (!dateString) return fallbackText;
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return fallbackText;
    }
    
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  } catch {
    return fallbackText;
  }
};

/**
 * Formats phone numbers to display format with country code
 * @param phone - The phone number to format
 * @returns Formatted phone number string
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '-';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  if (digits.length === 0) return '-';
  
  // Format by counting from the end:
  // Last 4 digits, then 3, then 3, and the rest as country code
  if (digits.length >= 10) {
    const last4 = digits.slice(-4);
    const next3 = digits.slice(-7, -4);
    const next3Before = digits.slice(-10, -7);
    const countryCode = digits.slice(0, -10);
    
    if (countryCode.length > 0) {
      return `+${countryCode} (${next3Before}) ${next3}-${last4}`;
    } else {
      // If no country code, assume it's a 10-digit number
      return `+1 (${next3Before}) ${next3}-${last4}`;
    }
  }
  
  // For shorter numbers, just add + prefix
  return `+${digits}`;
};



/**
 * Formats phone numbers for API calls
 * @param phone - The phone number to format
 * @returns Formatted phone number string for API calls
 */
export const formatPhoneNumberForCall = (phone: string): string => {
  if (!phone) return '';
  
  // Return the phone number exactly as stored, without adding any country code prefixes
  return phone;
}; 