import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  ANALYTICS_APPOINTMENT_DATA,
  ANALYTICS_CHART_APPOINTMENT_TRENDS,
  ANALYTICS_CHART_LEGEND_APPOINTMENTS,
  ANALYTICS_CHART_LEGEND_AI_CALLS,
} from "@/Constants/Analytics";

// Props for the AppointmentTrendsChart component
interface AppointmentTrendsChartProps {
  data?: typeof ANALYTICS_APPOINTMENT_DATA; // Optional custom data, defaults to ANALYTICS_APPOINTMENT_DATA
}

/**
 * Renders a bar chart-like visualization of weekly appointment and AI call trends.
 * Each row shows a day, appointment bar, and AI call bar.
 */
const AppointmentTrendsChart: React.FC<AppointmentTrendsChartProps> = ({
  data = ANALYTICS_APPOINTMENT_DATA,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{ANALYTICS_CHART_APPOINTMENT_TRENDS}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Render each day's appointment and AI call bars */}
          {data.map((dayData, index) => (
            <div key={index} className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600 w-12">
                {dayData.day}
              </span>
              <div className="flex-1 mx-4">
                <div className="flex items-center gap-2">
                  {/* Appointments bar */}
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(dayData.appointments / 50) * 100}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-8">
                    {dayData.appointments}
                  </span>
                </div>
                <div className="flex items-center gap-2 mt-1">
                  {/* AI Calls bar */}
                  <div className="flex-1 bg-gray-200 rounded-full h-1">
                    <div
                      className="bg-green-600 h-1 rounded-full"
                      style={{ width: `${(dayData.aiCalls / 30) * 100}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-600 w-8">
                    {dayData.aiCalls}
                  </span>
                </div>
              </div>
            </div>
          ))}
          {/* Chart legend */}
          <div className="flex justify-center gap-6 pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-600 rounded mr-2" />
              <span className="text-sm text-gray-600">
                {ANALYTICS_CHART_LEGEND_APPOINTMENTS}
              </span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-600 rounded mr-2" />
              <span className="text-sm text-gray-600">
                {ANALYTICS_CHART_LEGEND_AI_CALLS}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AppointmentTrendsChart;
