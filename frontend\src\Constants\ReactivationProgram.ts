export const REACTIVATION_PROGRAM_TITLE = "Reactivation Program";
export const REACTIVATION_PROGRAM_SUBTITLE =
  "AI-driven patient reactivation campaigns";
export const REACTIVATION_PROGRAM_ADD_NEW = "New Reactivation Program";
export const REACTIVATION_PROGRAM_STAT_ACTIVE_APPOINTMENTS =
  "Today’s Appointments";
export const REACTIVATION_PROGRAM_STAT_CALLS = "AI Calls Handled";
export const REACTIVATION_PROGRAM_STAT_SUCCESS_RATE = "Reactivation Success";
export const REACTIVATION_PROGRAM_STAT_PATIENTS = "Patient Returns";
export const REACTIVATION_PROGRAM_RECENT_ACTIVITY = "Recent Call Activity";
export const REACTIVATION_PROGRAM_SEARCH_PLACEHOLDER = "Search calls...";
export const REACTIVATION_PROGRAM_FILTER = "Filter";
export const REACTIVATION_PROGRAM_TABLE_PATIENT = "Patient";
export const REACTIVATION_PROGRAM_TABLE_CONTACT = "Contact";
export const REACTIVATION_PROGRAM_TABLE_LAST_VISIT = "Last Visit";
export const REACTIVATION_PROGRAM_TABLE_CALL_DETAILS = "Call Details";
export const REACTIVATION_PROGRAM_TABLE_OUTCOME = "Outcome";
export const REACTIVATION_PROGRAM_TABLE_NOTES = "Notes";

export const CAMPAIGN_FORM_FIELDS = {
  CAMPAIGN_NAME: "Campaign Name",
  START_DATE: "Start Date",
  END_DATE: "End Date",
  TARGET_PATIENTS: "Target Patients",
};

export const CAMPAIGN_FORM_PLACEHOLDERS = {
  CAMPAIGN_NAME: "Q2 2024 Reactivation",
};

export const CAMPAIGN_FORM_DEFAULTS = {
  TARGET_PATIENTS: "100",
};

export const CAMPAIGNS_DATA = [
  {
    id: 1,
    name: "Q1 2024 Reactivation",
    status: "Active",
    startDate: "2024-01-01",
    endDate: "2024-03-31",
    targetPatients: 150,
    contactedPatients: 89,
    successfulBookings: 24,
    successRate: "27%",
    lastRun: "2 hours ago",
  },
  {
    id: 2,
    name: "Cardiology Follow-ups",
    status: "Completed",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    targetPatients: 75,
    contactedPatients: 75,
    successfulBookings: 32,
    successRate: "43%",
    lastRun: "1 week ago",
  },
  {
    id: 3,
    name: "Pediatric Checkups",
    status: "Scheduled",
    startDate: "2024-02-01",
    endDate: "2024-02-29",
    targetPatients: 120,
    contactedPatients: 0,
    successfulBookings: 0,
    successRate: "0%",
    lastRun: "Not started",
  },
];

export const RECENT_CALLS_DATA = [
  {
    id: 1,
    patientName: "Sarah Wilson",
    phone: "+****************",
    lastVisit: "2023-08-15",
    callDate: "2024-01-20",
    callTime: "10:30 AM",
    outcome: "Booked",
    appointmentDate: "2024-01-25",
    notes: "Patient interested in follow-up consultation",
  },
  {
    id: 2,
    patientName: "Michael Chen",
    phone: "+****************",
    lastVisit: "2023-09-10",
    callDate: "2024-01-20",
    callTime: "11:15 AM",
    outcome: "Follow-up",
    appointmentDate: null,
    notes: "Patient requested callback next week",
  },
  {
    id: 3,
    patientName: "Emma Davis",
    phone: "+****************",
    lastVisit: "2023-07-20",
    callDate: "2024-01-20",
    callTime: "2:45 PM",
    outcome: "No Answer",
    appointmentDate: null,
    notes: "Left voicemail, will retry tomorrow",
  },
  {
    id: 4,
    patientName: "Robert Johnson",
    phone: "+****************",
    lastVisit: "2023-06-05",
    callDate: "2024-01-20",
    callTime: "3:20 PM",
    outcome: "Declined",
    appointmentDate: null,
    notes: "Patient not interested at this time",
  },
];

// Patient Management Constants
export const PATIENT_MANAGEMENT_TITLE = "Patient Management & Batch Calls";
export const CLINIC_SELECTOR_TITLE = "Select Clinic";
export const PATIENT_LIST_TITLE = "Patients";
export const BATCH_CALL_TITLE = "Batch Call Submission";
export const BATCH_CALL_SUCCESS_TITLE = "Batch Call Submitted Successfully!";
export const BATCH_CALL_SUCCESS_SUBTITLE =
  "Your batch call has been processed and scheduled";

// Reactivation Campaign Constants
export const REACTIVATION_CAMPAIGNS_TITLE = "Reactivation Campaigns";
export const REACTIVATION_CAMPAIGNS_SUBTITLE =
  "Manage and monitor your reactivation campaigns";
export const REACTIVATION_CAMPAIGNS_EMPTY =
  "No reactivation campaigns found for this clinic.";
export const REACTIVATION_CAMPAIGNS_EMPTY_SUBTITLE =
  "Create your first campaign to get started.";
export const REACTIVATION_CAMPAIGNS_SELECT_CLINIC =
  "Please select a clinic to view reactivation campaigns.";

// Campaign Status Constants
export const CAMPAIGN_STATUS = {
  PENDING: "pending",
  IN_PROGRESS: "in_progress",
  COMPLETED: "completed",
  FAILED: "failed",
  CANCELLED: "cancelled",
};

export const CAMPAIGN_STATUS_LABELS = {
  pending: "Pending",
  in_progress: "In Progress",
  completed: "Completed",
  failed: "Failed",
  cancelled: "Cancelled",
};

// Campaign Actions
export const CAMPAIGN_ACTIONS = {
  VIEW: "View",
  START: "Start",
  COMPLETE: "Complete",
  CANCEL: "Cancel",
  DELETE: "Delete",
};

// Statistics Constants
export const STATISTICS_TITLE = "Call Statistics & Analytics";
export const STATISTICS_SELECT_CLINIC =
  "Please select a clinic to view call statistics.";
export const STAT_TOTAL_CAMPAIGNS = "Total Campaigns";
export const STAT_SUCCESS_RATE = "Success Rate";
export const STAT_PATIENTS_CONTACTED = "Patients Contacted";

// Coming Soon Messages
export const COMING_SOON_TITLE = "Coming Soon";
export const COMING_SOON_DESCRIPTION =
  "Campaign management features are currently under development.";

// Button Labels
export const BUTTON_REFRESH = "Refresh";
export const BUTTON_REFRESH_STATS = "Refresh Stats";

// Loading Messages
export const LOADING_STATISTICS = "Loading statistics...";

// Error Messages
export const ERROR_LOADING_STATISTICS = "Error loading statistics:";

// Statistics Breakdown Labels
export const STAT_ACTIVE_CAMPAIGNS = "Active Campaigns";
export const STAT_COMPLETED_CAMPAIGNS = "Completed Campaigns";
export const STAT_FAILED_CAMPAIGNS = "Failed Campaigns";
export const STAT_CAMPAIGN_EFFICIENCY = "Campaign Efficiency";
export const STAT_PATIENTS_PER_CAMPAIGN = "patients/campaign";

// Performance Metrics
export const PERFORMANCE_METRICS_TITLE = "Performance Metrics";
export const CAMPAIGN_STATUS_BREAKDOWN_TITLE = "Campaign Status Breakdown";

// Batch Call Results Messages
export const BATCH_CALL_RESULTS = {
  SUCCESS_TITLE: "Batch Call Submitted Successfully!",
  SUCCESS_SUBTITLE: "Your batch call has been processed and scheduled for",
  CLOSE_BUTTON: "Close",
  BATCH_CALL_SUMMARY_TITLE: "Batch Call Summary",
  TOTAL_PATIENTS: "Total Patients",
  CLINIC_ID: "Clinic ID",
  SCHEDULED_TIME: "Scheduled Time",
  STATUS: "Status",
  ACTIVE_STATUS: "Active",
  PATIENT_DETAILS_TITLE: "Patient Details with User Information",
  EXPAND_ALL: "Expand All",
  COLLAPSE_ALL: "Collapse All",
  PATIENT_INFORMATION: "Patient Information",
  USER_INFORMATION: "User Information",
  CREATED_BY: "Created By",
  LAST_UPDATED_BY: "Last Updated By",
  NAME_LABEL: "Name:",
  EMAIL_LABEL: "Email:",
  PHONE_LABEL: "Phone:",
  ADDRESS_LABEL: "Address:",
  LAST_VISIT_SUMMARY_LABEL: "Last Visit Summary:",
  LAST_VISIT_PREFIX: "Last visit:",
  NEVER_VISITED: "Never visited",
  GENDER_PREFIX: "Gender:",
  INVALID_DATE: "Invalid date",
};

// Clinic Selector Messages
export const CLINIC_SELECTOR = {
  TITLE: "Select Clinic",
  LOADING_PLACEHOLDER: "Loading clinics...",
  CHOOSE_PLACEHOLDER: "Choose a clinic",
};

// Patient List Messages
export const PATIENT_LIST = {
  TITLE: "Patients",
  LOADING_TITLE: "Patients",
  NO_PATIENTS_MESSAGE: "No patients found. Please select a clinic first.",
  SELECT_ALL: "Select All",
  DESELECT_ALL: "Deselect All",
  SELECTED_COUNT: "selected",
  SEARCH_PLACEHOLDER: "Search patients by name, email, or phone...",
  ID_PREFIX: "ID:",
  LAST_VISIT_PREFIX: "Last visit:",
  CREATED_BY_PREFIX: "Created by:",
  NEVER_VISITED: "Never visited",
  INVALID_DATE: "Invalid date",
};

// Reactivation Stats Table Messages
export const REACTIVATION_STATS_TABLE = {
  LOADING_MESSAGE: "Loading campaigns...",
  NO_CAMPAIGNS_TITLE: "No reactivation campaigns found.",
  PATIENTS_LABEL: "patients",
  DAYS_LABEL: "days",
  SCHEDULED_LABEL: "Scheduled",
  STARTED_LABEL: "Started",
  PATIENT_INFORMATION: "Patient Information",
  TIMELINE: "Timeline",
  CLINIC_INFO: "Clinic Info",
  CREATED_BY: "Created By",
  CALL_RESULTS: "Call Results",
  ERROR_DETAILS: "Error Details",
  TOTAL_PATIENTS: "Total Patients:",
  INACTIVE_PERIOD: "Inactive Period:",
  CAMPAIGN_TYPE: "Campaign Type:",
  PATIENT_REACTIVATION: "Patient Reactivation",
  CREATED: "Created:",
  SCHEDULED: "Scheduled:",
  STARTED: "Started:",
  COMPLETED: "Completed:",
  NO_CLINIC_INFO: "No clinic info",
  NO_CREATOR_INFO: "No creator info",
};

// Reactivation Campaigns List Messages
export const REACTIVATION_CAMPAIGNS_LIST = {
  LOADING_MESSAGE: "Loading campaigns...",
  ERROR_PREFIX: "Error:",
  NO_CAMPAIGNS_TITLE: "No reactivation campaigns found for this clinic.",
  NO_CAMPAIGNS_SUBTITLE: "Create your first campaign to get started.",
  ALL_FILTER: "All",
  PENDING_LABEL: "Pending",
  IN_PROGRESS_LABEL: "In Progress",
  COMPLETED_LABEL: "Completed",
  FAILED_LABEL: "Failed",
  CANCELLED_LABEL: "Cancelled",
  VIEW_BUTTON: "View",
  START_BUTTON: "Start",
  COMPLETE_BUTTON: "Complete",
  CANCEL_BUTTON: "Cancel",
  BATCH_ID_LABEL: "Batch ID",
  INACTIVE_PERIOD_LABEL: "Inactive Period",
  PATIENTS_LABEL: "Patients",
  SCHEDULED_LABEL: "Scheduled",
  STARTED_LABEL: "Started",
  DAYS_LABEL: "days",
  ERROR_PREFIX_LABEL: "Error:",
  RESULTS_PREFIX_LABEL: "Results:",
  CREATED_PREFIX: "Created",
};
