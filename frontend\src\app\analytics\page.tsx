
'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Download,
  Filter
} from 'lucide-react';
import AnalyticsStatsGrid from '@/components/Analytics/AnalyticsStatsGrid';
import TimeRangeSelector from '@/components/Analytics/TimeRangeSelector';
import AppointmentTrendsChart from '@/components/Analytics/AppointmentTrendsChart';
import DoctorPerformanceChart from '@/components/Analytics/DoctorPerformanceChart';
import ReactivationPerformanceTable from '@/components/Analytics/ReactivationPerformanceTable';
import {
  ANALYTICS_TITLE,
  ANALYTICS_SUBTITLE,
  ANALYTICS_BUTTON_FILTER,
  ANALYTICS_BUTTON_EXPORT
} from '@/Constants/Analytics';
import PageSection from '@/components/CommonComponents/PageSection';

const Analytics = () => {
  const [timeRange, setTimeRange] = useState('7d');

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">{ANALYTICS_TITLE}</h2>
          <p className="text-gray-600">{ANALYTICS_SUBTITLE}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="bg-white border border-gray-200 text-black">
            <Filter className="h-4 w-4 mr-1.5" />
            {ANALYTICS_BUTTON_FILTER}
          </Button>
          <Button variant="outline" size="sm" className="bg-white border border-gray-200 text-black">
            <Download className="h-4 w-4 mr-1.5" />
            {ANALYTICS_BUTTON_EXPORT}
          </Button>
        </div>
      </div>

      {/* Time Range Selector */}
      <TimeRangeSelector 
        timeRange={timeRange} 
        onTimeRangeChange={setTimeRange} 
      />

      {/* Key Metrics */}
      <AnalyticsStatsGrid />

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Appointment Trends */}
        <AppointmentTrendsChart />

        {/* Doctor Performance */}
        <DoctorPerformanceChart />
      </div>

      {/* Reactivation Campaign Performance */}
      <ReactivationPerformanceTable />
    </PageSection>
  );
};

export default Analytics;
