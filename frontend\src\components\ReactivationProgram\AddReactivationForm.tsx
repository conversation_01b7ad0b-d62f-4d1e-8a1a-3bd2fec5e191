// AddReactivationForm.tsx
// Renders a modal form for adding a new reactivation program with patient management functionality.

import React, { useState, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Calendar, Clock } from "lucide-react";
import { FORM_BUTTONS } from "@/Constants/CommonComponents";
import ClinicSelector from "./ClinicSelector";
import PatientList from "./PatientList";
import BatchCallSubmission from "./BatchCallSubmission";
import BatchCallResults from "./BatchCallResults";
import {
  usePatientManagement,
  BatchCallResponse,
} from "@/hooks/usePatientManagement";

/**
 * Props for the AddReactivationForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (campaign data)
 */
interface AddReactivationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: Record<string, unknown>) => void;
}

/**
 * Renders a modal form for adding a new reactivation program.
 * Includes patient management functionality with clinic selection, patient list, and batch call submission.
 */
const AddReactivationForm: React.FC<AddReactivationFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  // Scheduling form state
  const [programName, setProgramName] = useState("");
  const [daysAfter, setDaysAfter] = useState("7");
  const [time, setTime] = useState("09:00");
  const [amPm, setAmPm] = useState("AM");
  const [date, setDate] = useState("");

  // Patient management state
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const [selectedPatientIds, setSelectedPatientIds] = useState<number[]>([]);
  const [batchCallResults, setBatchCallResults] =
    useState<BatchCallResponse | null>(null);
  const [showResults, setShowResults] = useState(false);

  const {
    patients,
    loading,
    getPatientsByClinic,
    submitBatchCall,
    clearPatients,
  } = usePatientManagement();

  // Handle clinic selection
  const handleClinicSelect = useCallback(
    async (clinicId: number) => {
      setSelectedClinicId(clinicId);
      setSelectedPatientIds([]);
      setBatchCallResults(null);
      setShowResults(false);

      // Fetch patients for the selected clinic
      const patientsResult = await getPatientsByClinic(clinicId);

      if (!patientsResult.success) {
        console.error("Failed to fetch patients:", patientsResult.error);
      }
    },
    [getPatientsByClinic]
  );

  // Handle patient selection change
  const handlePatientSelectionChange = useCallback((patientIds: number[]) => {
    setSelectedPatientIds(patientIds);
  }, []);

  // Handle batch call submission
  const handleBatchCallSubmit = useCallback(
    async (time?: string) => {
      if (!selectedClinicId || selectedPatientIds.length === 0) {
        return;
      }

      const result = await submitBatchCall(
        selectedClinicId,
        selectedPatientIds,
        time
      );
      if (result.success && result.data) {
        setBatchCallResults(result.data as BatchCallResponse);
        setShowResults(true);
      }
    },
    [selectedClinicId, selectedPatientIds, submitBatchCall]
  );

  // Clear results and reset
  const handleCloseResults = useCallback(() => {
    setShowResults(false);
    setBatchCallResults(null);
    setSelectedPatientIds([]);
    clearPatients();
  }, [clearPatients]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{"Add Reactivation Program"}</DialogTitle>
        </DialogHeader>

        {/* Schedule After Configuration Section */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Calendar className="h-5 w-5" />
            <h3 className="text-lg font-semibold">
              Schedule After Configuration
            </h3>
          </div>
          <p className="text-sm text-gray-600 mb-4">
            Set up automatic reactivation calls after a specified number of days
          </p>

          {/* Program Name */}
          <div className="space-y-2">
            <Label htmlFor="program-name">Name</Label>
            <Input
              id="program-name"
              placeholder="Enter program name"
              value={programName}
              onChange={(e) => setProgramName(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Days After, Time, and Date Row */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="days-after">Days After</Label>
              <Select value={daysAfter} onValueChange={setDaysAfter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 Day</SelectItem>
                  <SelectItem value="3">3 Days</SelectItem>
                  <SelectItem value="7">7 Days</SelectItem>
                  <SelectItem value="14">14 Days</SelectItem>
                  <SelectItem value="30">30 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Time</Label>
              <div className="flex gap-2">
                <div className="flex items-center gap-2 flex-1">
                  <Clock className="h-4 w-4" />
                  <Input
                    id="time"
                    type="time"
                    value={time}
                    onChange={(e) => setTime(e.target.value)}
                    className="flex-1"
                  />
                </div>
                <Select value={amPm} onValueChange={setAmPm}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AM">AM</SelectItem>
                    <SelectItem value="PM">PM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="w-full"
              />
            </div>
          </div>

          {/* Confirmation Message */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-4">
            <p className="text-sm text-green-800">
              Reactivation calls will be scheduled {daysAfter} day(s) after the
              last visit at {time} {amPm}
            </p>
          </div>
        </div>

        {/* Select Clinic Section */}
        <div className="space-y-4 mb-6">
          <h3 className="text-lg font-semibold">Select Clinic</h3>
          <ClinicSelector
            selectedClinicId={selectedClinicId}
            onClinicSelect={handleClinicSelect}
          />
        </div>

        {/* Patients Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Patients</h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <PatientList
                patients={patients}
                loading={loading}
                onPatientSelectionChange={handlePatientSelectionChange}
              />
            </div>

            <div className="space-y-6">
              <BatchCallSubmission
                selectedPatients={patients.filter((p) =>
                  selectedPatientIds.includes(p.id)
                )}
                onSubmit={handleBatchCallSubmit}
                loading={loading}
              />
            </div>
          </div>

          {showResults && batchCallResults && (
            <BatchCallResults
              results={batchCallResults}
              onClose={handleCloseResults}
            />
          )}
        </div>

        <div className="flex gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            {FORM_BUTTONS.CANCEL}
          </Button>
          <Button type="submit" variant="main" className="flex-1">
            {FORM_BUTTONS.SAVE_SCHEDULE}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddReactivationForm;
