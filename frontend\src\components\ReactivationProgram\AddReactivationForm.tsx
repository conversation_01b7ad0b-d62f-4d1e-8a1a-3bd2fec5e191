// AddReactivationForm.tsx
// Renders a modal form for adding a new reactivation program with patient management functionality.

import React, { useState, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { FORM_BUTTONS } from "@/Constants/CommonComponents";
import { PATIENT_MANAGEMENT_TITLE } from "@/Constants/ReactivationProgram";
import ClinicSelector from "./ClinicSelector";
import PatientList from "./PatientList";
import BatchCallSubmission from "./BatchCallSubmission";
import BatchCallResults from "./BatchCallResults";
import {
  usePatientManagement,
  BatchCallResponse,
} from "@/hooks/usePatientManagement";

/**
 * Props for the AddReactivationForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - <PERSON>ler for form submission (campaign data)
 */
interface AddReactivationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: Record<string, unknown>) => void;
}

/**
 * Renders a modal form for adding a new reactivation program.
 * Includes patient management functionality with clinic selection, patient list, and batch call submission.
 */
const AddReactivationForm: React.FC<AddReactivationFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  // Patient management state
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const [selectedPatientIds, setSelectedPatientIds] = useState<number[]>([]);
  const [batchCallResults, setBatchCallResults] =
    useState<BatchCallResponse | null>(null);
  const [showResults, setShowResults] = useState(false);

  const {
    patients,
    loading,
    getPatientsByClinic,
    submitBatchCall,
    clearPatients,
  } = usePatientManagement();

  // Handle clinic selection
  const handleClinicSelect = useCallback(
    async (clinicId: number) => {
      setSelectedClinicId(clinicId);
      setSelectedPatientIds([]);
      setBatchCallResults(null);
      setShowResults(false);

      // Fetch patients for the selected clinic
      const patientsResult = await getPatientsByClinic(clinicId);

      if (!patientsResult.success) {
        console.error("Failed to fetch patients:", patientsResult.error);
      }
    },
    [getPatientsByClinic]
  );

  // Handle patient selection change
  const handlePatientSelectionChange = useCallback((patientIds: number[]) => {
    setSelectedPatientIds(patientIds);
  }, []);

  // Handle batch call submission
  const handleBatchCallSubmit = useCallback(
    async (time?: string) => {
      if (!selectedClinicId || selectedPatientIds.length === 0) {
        return;
      }

      const result = await submitBatchCall(
        selectedClinicId,
        selectedPatientIds,
        time
      );
      if (result.success && result.data) {
        setBatchCallResults(result.data as BatchCallResponse);
        setShowResults(true);
      }
    },
    [selectedClinicId, selectedPatientIds, submitBatchCall]
  );

  // Clear results and reset
  const handleCloseResults = useCallback(() => {
    setShowResults(false);
    setBatchCallResults(null);
    setSelectedPatientIds([]);
    clearPatients();
  }, [clearPatients]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{"Add Reactivation Program"}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-gray-900">
              {PATIENT_MANAGEMENT_TITLE}
            </h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <ClinicSelector
                selectedClinicId={selectedClinicId}
                onClinicSelect={handleClinicSelect}
              />

              <PatientList
                patients={patients}
                loading={loading}
                onPatientSelectionChange={handlePatientSelectionChange}
              />
            </div>

            <div className="space-y-6">
              <BatchCallSubmission
                selectedPatients={patients.filter((p) =>
                  selectedPatientIds.includes(p.id)
                )}
                onSubmit={handleBatchCallSubmit}
                loading={loading}
              />
            </div>
          </div>

          {showResults && batchCallResults && (
            <BatchCallResults
              results={batchCallResults}
              onClose={handleCloseResults}
            />
          )}
        </div>

        <div className="flex gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            {FORM_BUTTONS.CANCEL}
          </Button>
          <Button type="submit" variant="main" className="flex-1">
            {FORM_BUTTONS.SAVE_SCHEDULE}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddReactivationForm;
