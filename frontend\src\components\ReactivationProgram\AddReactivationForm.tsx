// AddCampaignForm.tsx
// Renders a modal form for adding a new campaign using CommonForm and campaign field config.

import React from "react";
import CommonForm from "@/components/CommonComponents/CommonForm";
import { formConfigs } from "@/components/CommonComponents/formConfigs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import { FORM_BUTTONS } from "@/Constants/CommonComponents";

/**
 * Props for the AddCampaignForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (campaign data)
 */
interface AddReactivationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: Record<string, unknown>) => void;
}

/**
 * Renders a modal form for adding a new campaign.
 * Uses CommonForm with campaign field configuration.
 */
const AddReactivationForm: React.FC<AddReactivationFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{"Add Reactivation Program"}</DialogTitle>
        </DialogHeader>
        <div className="flex gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            {FORM_BUTTONS.CANCEL}
          </Button>
          <Button type="submit" variant="main" className="flex-1">
            {FORM_BUTTONS.SAVE_SCHEDULE}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddReactivationForm;
