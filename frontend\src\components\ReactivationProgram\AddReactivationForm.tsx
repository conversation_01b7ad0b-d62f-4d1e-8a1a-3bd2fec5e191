// AddReactivationForm.tsx
// Renders a modal form for adding a new reactivation program with patient management functionality.

import React, { useState, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Calendar, Clock, CheckCircle, Settings } from "lucide-react";
import { FORM_BUTTONS } from "@/Constants/CommonComponents";
import ClinicSelector from "./ClinicSelector";
import PatientList from "./PatientList";
import BatchCallSubmission from "./BatchCallSubmission";
import BatchCallResults from "./BatchCallResults";
import {
  usePatientManagement,
  BatchCallResponse,
} from "@/hooks/usePatientManagement";

/**
 * Props for the AddReactivationForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (campaign data)
 */
interface AddReactivationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: Record<string, unknown>) => void;
}

/**
 * Renders a modal form for adding a new reactivation program.
 * Includes patient management functionality with clinic selection, patient list, and batch call submission.
 */
const AddReactivationForm: React.FC<AddReactivationFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  // Scheduling form state
  const [programName, setProgramName] = useState("");
  const [daysAfter, setDaysAfter] = useState("7");
  const [time, setTime] = useState("09:00");
  const [amPm, setAmPm] = useState("AM");
  const [date, setDate] = useState("");
  const [isSchedulingLoading, setIsSchedulingLoading] = useState(false);
  const [schedulingSuccess, setSchedulingSuccess] = useState(false);

  // Patient management state
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const [selectedPatientIds, setSelectedPatientIds] = useState<number[]>([]);
  const [batchCallResults, setBatchCallResults] =
    useState<BatchCallResponse | null>(null);
  const [showResults, setShowResults] = useState(false);

  const {
    patients,
    loading,
    getPatientsByClinic,
    submitBatchCall,
    clearPatients,
  } = usePatientManagement();

  // Handle clinic selection
  const handleClinicSelect = useCallback(
    async (clinicId: number) => {
      setSelectedClinicId(clinicId);
      setSelectedPatientIds([]);
      setBatchCallResults(null);
      setShowResults(false);

      // Fetch patients for the selected clinic
      const patientsResult = await getPatientsByClinic(clinicId);

      if (!patientsResult.success) {
        console.error("Failed to fetch patients:", patientsResult.error);
      }
    },
    [getPatientsByClinic]
  );

  // Handle patient selection change
  const handlePatientSelectionChange = useCallback((patientIds: number[]) => {
    setSelectedPatientIds(patientIds);
  }, []);

  // Handle scheduling form submission
  const handleSchedulingSubmit = useCallback(async () => {
    if (!programName.trim()) {
      alert("Please enter a program name");
      return;
    }

    setIsSchedulingLoading(true);
    try {
      // Simulate API call for scheduling
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setSchedulingSuccess(true);
      setTimeout(() => setSchedulingSuccess(false), 3000);

      // Call the onSubmit prop with scheduling data
      onSubmit({
        programName,
        daysAfter: parseInt(daysAfter),
        time,
        amPm,
        date,
        scheduledDateTime: date ? `${date}T${time}` : null,
      });
    } catch (error) {
      console.error("Failed to create scheduling program:", error);
      alert("Failed to create scheduling program. Please try again.");
    } finally {
      setIsSchedulingLoading(false);
    }
  }, [programName, daysAfter, time, amPm, date, onSubmit]);

  // Clear scheduling form
  const handleClearScheduling = useCallback(() => {
    setProgramName("");
    setDaysAfter("7");
    setTime("09:00");
    setAmPm("AM");
    setDate("");
    setSchedulingSuccess(false);
  }, []);

  // Check if scheduling form is valid
  const isSchedulingFormValid = programName.trim().length > 0;

  // Handle batch call submission
  const handleBatchCallSubmit = useCallback(
    async (time?: string) => {
      if (!selectedClinicId || selectedPatientIds.length === 0) {
        return;
      }

      const result = await submitBatchCall(
        selectedClinicId,
        selectedPatientIds,
        time
      );
      if (result.success && result.data) {
        setBatchCallResults(result.data as BatchCallResponse);
        setShowResults(true);
      }
    },
    [selectedClinicId, selectedPatientIds, submitBatchCall]
  );

  // Clear results and reset
  const handleCloseResults = useCallback(() => {
    setShowResults(false);
    setBatchCallResults(null);
    setSelectedPatientIds([]);
    clearPatients();
  }, [clearPatients]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{"Add Reactivation Program"}</DialogTitle>
        </DialogHeader>

        {/* Schedule After Configuration Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Schedule After Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-gray-600">
              Set up automatic reactivation calls after a specified number of
              days
            </p>

            {/* Program Name */}
            <div className="space-y-2">
              <Label htmlFor="program-name">Name</Label>
              <Input
                id="program-name"
                placeholder="Enter program name"
                value={programName}
                onChange={(e) => setProgramName(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Days After, Time, and Date Row */}
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="days-after">Days After</Label>
                <Select value={daysAfter} onValueChange={setDaysAfter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Day</SelectItem>
                    <SelectItem value="3">3 Days</SelectItem>
                    <SelectItem value="7">7 Days</SelectItem>
                    <SelectItem value="14">14 Days</SelectItem>
                    <SelectItem value="30">30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <div className="flex gap-2">
                  <div className="flex items-center gap-2 flex-1">
                    <Clock className="h-4 w-4" />
                    <Input
                      id="time"
                      type="time"
                      value={time}
                      onChange={(e) => setTime(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                  <Select value={amPm} onValueChange={setAmPm}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AM">AM</SelectItem>
                      <SelectItem value="PM">PM</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  className="w-full"
                />
              </div>
            </div>

            {/* Configuration Summary */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-blue-900">
                  Configuration Summary
                </h4>
                <Badge
                  variant="secondary"
                  className="bg-blue-100 text-blue-800"
                >
                  {isSchedulingFormValid ? "Ready" : "Incomplete"}
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-blue-600" />
                  <span className="text-blue-700">
                    Program: {programName || "Not set"}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-green-700">
                    {daysAfter} day(s) at {time} {amPm}
                  </span>
                </div>
              </div>
            </div>

            {/* Confirmation Message */}
            {isSchedulingFormValid && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-800">
                  Reactivation calls will be scheduled {daysAfter} day(s) after
                  the last visit at {time} {amPm}
                  {date && ` starting from ${date}`}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <Button
                onClick={handleSchedulingSubmit}
                disabled={!isSchedulingFormValid || isSchedulingLoading}
                className="flex-1"
                variant="default"
              >
                {isSchedulingLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Creating Program...
                  </div>
                ) : schedulingSuccess ? (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Program Created!
                  </div>
                ) : (
                  <>
                    <Settings className="h-4 w-4 mr-2" />
                    Create Program
                  </>
                )}
              </Button>

              <Button
                onClick={handleClearScheduling}
                disabled={isSchedulingLoading}
                variant="outline"
                className="flex-1"
              >
                <Clock className="h-4 w-4 mr-2" />
                Clear Form
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Select Clinic Section */}
        <div className="space-y-4 mb-6">
          <h3 className="text-lg font-semibold">Select Clinic</h3>
          <ClinicSelector
            selectedClinicId={selectedClinicId}
            onClinicSelect={handleClinicSelect}
          />
        </div>

        {/* Patients Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Patients</h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <PatientList
                patients={patients}
                loading={loading}
                onPatientSelectionChange={handlePatientSelectionChange}
              />
            </div>
          </div>

          {showResults && batchCallResults && (
            <BatchCallResults
              results={batchCallResults}
              onClose={handleCloseResults}
            />
          )}
        </div>

        <div className="flex gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            {FORM_BUTTONS.CANCEL}
          </Button>
          <Button type="submit" variant="main" className="flex-1">
            {FORM_BUTTONS.SAVE_SCHEDULE}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddReactivationForm;
