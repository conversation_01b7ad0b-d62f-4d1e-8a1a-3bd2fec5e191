/** @type {import('sequelize-cli').Migration} */
export const up = async (queryInterface, Sequelize) => {
  // Rename the column from reactivitation_days to reactivation_days
  await queryInterface.renameColumn('clinics', 'reactivitation_days', 'reactivation_days');
};

export const down = async (queryInterface, Sequelize) => {
  // Revert the column name back to reactivitation_days
  await queryInterface.renameColumn('clinics', 'reactivation_days', 'reactivitation_days');
};
