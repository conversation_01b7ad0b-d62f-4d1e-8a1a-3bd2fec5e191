import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Eye, Edit } from "lucide-react";
import {
  CLINIC_CARD_BUTTON_VIEW_DETAILS,
  CLINIC_CARD_BUTTON_EDIT,
  CLINIC_CARD_LABEL_EMAIL,
  CLINIC_CARD_LABEL_PHONE,
  CLINIC_CARD_LABEL_WORKING_HOURS,
  CLINIC_CARD_LABEL_WORKING_DAYS,
} from "@/Constants/ClinicMaster";

// Props for the DetailedClinicCard component
interface DetailedClinicCardProps {
  clinic_name: string;
  location: string;
  clinic_email: string;
  clinic_phonenumber: string;
  working_hours: string;
  working_days: string;
  statusBadge?: React.ReactNode;
  hideActions?: boolean;
}

// Helper to format working hours JSON string
function formatWorkingHours(working_hours: string): string {
  try {
    const wh = typeof working_hours === 'string' ? JSON.parse(working_hours) : working_hours;
    const hoursArr = Object.values(wh);
    const allSame = hoursArr.every((h) => h === hoursArr[0]);
    if (allSame) {
      return String(hoursArr[0]);
    }
    // Otherwise, show each day's hours
    return Object.entries(wh)
      .map(([day, hours]) => `${day.charAt(0).toUpperCase() + day.slice(1)}: ${hours}`)
      .join(', ');
  } catch {
    return working_hours;
  }
}

function formatWorkingDays(working_days: string): string {
  try {
    let days = working_days;
    if (typeof days === 'string') {
      // Try to parse JSON array
      try {
        const parsed = JSON.parse(days as string);
        if (Array.isArray(parsed)) {
          days = parsed.join(', ');
        } else if (typeof parsed === 'string') {
          days = parsed;
        } else {
          days = String(parsed);
        }
      } catch {
        // Not JSON, use as is
      }
    }
    // Ensure days is a string
    days = String(days);
    // Remove brackets/quotes if present
    days = days.replace(/\[|\]|"/g, '');
    // Capitalize first letter and after dashes
    days = days.replace(/(^| - )(\w)/g, (match, p1, p2) => p1 + p2.toUpperCase());
    return days;
  } catch {
    return working_days;
  }
}

/**
 * Renders a detailed card for a clinic.
 * Shows name, address, status, contact, hours, capacity, doctors, facilities, and actions.
 */
const DetailedClinicCard: React.FC<DetailedClinicCardProps> = ({
  clinic_name,
  location,
  clinic_email,
  clinic_phonenumber,
  working_hours,
  working_days,
  statusBadge,
  hideActions = false,
}) => (
  <Card className="hover:shadow-lg transition-shadow">
    <CardHeader className="pb-4">
      <div className="flex items-start justify-between">
        <div>
          <CardTitle className="text-xl text-gray-900">{clinic_name}</CardTitle>
          <div className="flex items-start mt-2">
            <MapPin className="h-4 w-4 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
            <span className="text-sm text-gray-600">{location}</span>
          </div>
        </div>
        {statusBadge}
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="text-sm text-gray-600">
            <span className="font-semibold">{CLINIC_CARD_LABEL_EMAIL}</span> {clinic_email}
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-semibold">{CLINIC_CARD_LABEL_PHONE}</span> {clinic_phonenumber}
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-semibold">{CLINIC_CARD_LABEL_WORKING_HOURS}</span> {formatWorkingHours(working_hours)}
          </div>
          <div className="text-sm text-gray-600">
            <span className="font-semibold">{CLINIC_CARD_LABEL_WORKING_DAYS}</span> {formatWorkingDays(working_days)}
          </div>
        </div>
      </div>
      {/* Hide actions for view modal */}
      {!hideActions && (
        <div className="flex gap-4 pt-4 border-t border-gray-200">
          <Button variant="outline" className="flex-1 border-gray-200">
            <Eye className="h-4 w-4 mr-2" />
            {CLINIC_CARD_BUTTON_VIEW_DETAILS}
          </Button>
          <Button variant="outline" className="flex-1 border-gray-200">
            <Edit className="h-4 w-4 mr-2" />
            {CLINIC_CARD_BUTTON_EDIT}
          </Button>
        </div>
      )}
    </CardContent>
  </Card>
);

export default DetailedClinicCard;
