import React from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { TEMPLATE_STATS } from "@/Constants/TemplateMaster";
import { FileText, MessageSquare, Mail, Bot } from "lucide-react";

interface Template {
  id: number;
  name: string;
  type: string;
  content: string;
  created_at?: string;
}

interface TemplateStatsGridProps {
  templates?: Template[];
}

const TemplateStatsGrid: React.FC<TemplateStatsGridProps> = ({ templates = [] }) => {
  // Calculate real stats from templates data
  const stats = {
    totalTemplates: templates.length,
    smsTemplates: templates.filter(t => t.type === 'sms').length,
    emailTemplates: templates.filter(t => t.type === 'email').length,
    agentPrompts: templates.filter(t => t.type === 'call_prompt').length,
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title={TEMPLATE_STATS.TOTAL_TEMPLATES}
        value={stats.totalTemplates.toString()}
        icon={<FileText className="text-blue-600" />}
      />
      <StatCard
        title={TEMPLATE_STATS.SMS_TEMPLATES}
        value={stats.smsTemplates.toString()}
        icon={<MessageSquare className="text-green-600" />}
      />
      <StatCard
        title={TEMPLATE_STATS.EMAIL_TEMPLATES}
        value={stats.emailTemplates.toString()}
        icon={<Mail className="text-purple-600" />}
      />
      <StatCard
        title={TEMPLATE_STATS.AGENT_PROMPTS}
        value={stats.agentPrompts.toString()}
        icon={<Bot className="text-orange-600" />}
      />
    </div>
  );
};

export default TemplateStatsGrid; 