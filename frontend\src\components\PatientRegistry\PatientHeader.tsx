import React from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { Plus } from "lucide-react";
import {
  TITLE_PATIENT_REGISTRY,
  SUBTITLE_PATIENT_REGISTRY,
  BUTTON_ADD_NEW_PATIENT,
} from "@/Constants/PatientRegistry";
import { type VariantProps } from "class-variance-authority";

/**
 * Props for the PatientHeader component
 * @property onAdd - Handler for the add new patient button
 */
interface PatientHeaderProps {
  onAdd: () => void;
  buttonVariant?: VariantProps<typeof buttonVariants>["variant"];
}

/**
 * Renders the header for the Patient Registry page, including title, subtitle, and add patient button.
 */
const PatientHeader: React.FC<PatientHeaderProps> = ({
  onAdd,
  buttonVariant,
}) => (
  <div className="flex items-center justify-between">
    <div>
      <h2 className="text-3xl font-bold text-gray-900">
        {TITLE_PATIENT_REGISTRY}
      </h2>
      <p className="text-gray-600">{SUBTITLE_PATIENT_REGISTRY}</p>
    </div>
    <Button variant={buttonVariant} onClick={onAdd}>
      <Plus className="h-4 w-4 mr-2" />
      {BUTTON_ADD_NEW_PATIENT}
    </Button>
  </div>
);

export default PatientHeader;
