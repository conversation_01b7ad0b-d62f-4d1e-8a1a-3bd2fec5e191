import React, { useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { getScheduleData, ScheduleData } from '@/services/dashboardDataService';
import { DASHBOARD_SCHEDULE_COMPONENT } from '@/Constants/Dashboard';

const timeSlots = DASHBOARD_SCHEDULE_COMPONENT.TIME_SLOTS;

const DashboardSchedule = () => {
  const [scheduleData, setScheduleData] = useState<ScheduleData[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      setLoading(true);
      const schedule = await getScheduleData();
      setScheduleData(schedule);
    } catch (error) {
      console.error(DASHBOARD_SCHEDULE_COMPONENT.ERROR_FETCHING, error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{DASHBOARD_SCHEDULE_COMPONENT.TITLE}</h3>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-3 py-1 cursor-pointer hover:bg-gray-200 transition-colors">
            <span className="text-sm font-medium text-gray-700">{DASHBOARD_SCHEDULE_COMPONENT.TIME_PERIOD}</span>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </div>
      </div>
      
      <div className="w-full">
        <div className="grid grid-cols-8 gap-1">
          {/* Time slots header */}
          <div className="text-xs text-gray-500 font-medium text-center">
            <div className="w-12 h-6 flex items-center justify-center">
              Time
            </div>
          </div>
          {timeSlots.map((time) => (
            <div key={time} className="text-xs text-gray-500 font-medium text-center">
              <div className="w-12 h-6 flex items-center justify-center">
                {time}
              </div>
            </div>
          ))}
          
          {/* Schedule rows */}
          {scheduleData.map((day) => (
            <React.Fragment key={day.day}>
              <div className="text-sm font-medium text-gray-700 flex items-center justify-center">
                {day.day}
              </div>
              {timeSlots.map((time) => {
                const timeHour = parseInt(time.split(':')[0]);
                const appointmentsInSlot = day.appointments.filter(appointment => {
                  const startHour = parseInt(appointment.startTime.split(':')[0]);
                  const endHour = parseInt(appointment.endTime.split(':')[0]);
                  return timeHour >= startHour && timeHour < endHour;
                });
                
                return (
                  <div key={time} className="relative h-8 rounded border border-gray-200">
                    {appointmentsInSlot.map((appointment) => {
                      const startHour = parseInt(appointment.startTime.split(':')[0]);
                      const endHour = parseInt(appointment.endTime.split(':')[0]);
                      const duration = endHour - startHour;
                      const startPosition = timeHour - startHour;
                      
                      // Only show appointment if it starts in this time slot
                      if (startPosition === 0) {
                        return (
                          <div
                            key={`${appointment.id}-${time}`}
                            className={`absolute inset-0 ${appointment.color} rounded border-l-4 border-l-blue-500 px-2 py-1 text-white text-xs font-medium flex items-center`}
                            style={{
                              width: `${Math.max(duration, 1) * 100}%`,
                              zIndex: 10
                            }}
                          >
                            <span className="truncate font-semibold">{appointment.doctor}</span>
                          </div>
                        );
                      }
                      
                      // For continuation slots, show a subtle indicator
                      if (startPosition > 0 && startPosition < duration) {
                        return (
                          <div
                            key={`${appointment.id}-${time}-cont`}
                            className={`absolute inset-0 ${appointment.color} opacity-80 rounded px-2 py-1 text-white text-xs font-medium flex items-center`}
                            style={{
                              width: `${Math.max(duration - startPosition, 1) * 100}%`,
                              left: '0',
                              zIndex: 5
                            }}
                          >
                            <span className="truncate font-semibold">{appointment.doctor}</span>
                          </div>
                        );
                      }
                      
                      return null;
                    })}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardSchedule;
