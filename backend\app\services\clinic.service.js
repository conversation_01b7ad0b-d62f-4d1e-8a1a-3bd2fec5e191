/**
 * Clinic Service: Handles all business logic and DB operations for clinics.
 * Uses the Clinic Sequelize model.
 * <AUTHOR>
 */
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { Clinic } from '../models/index.js';
import { Op } from 'sequelize';
import { CLINIC_ALREADY_EXISTS } from '../utils/constants.utils.js';

/**
 * Check if a clinic exists by email or phone number
 * @param {Object} params - { clinic_email, clinic_phonenumber }
 * @returns {Promise<Object|null>} The clinic if found, else null
 * <AUTHOR>
 */
export const findClinicByEmailOrPhone = async ({ clinic_email, clinic_phonenumber }) => {
  const orConditions = [];
  if (clinic_email) orConditions.push({ clinic_email });
  if (clinic_phonenumber) orConditions.push({ clinic_phonenumber });
  if (orConditions.length === 0) return null;
  return await Clinic.findOne({
    where: {
      [Op.or]: orConditions,
    },
  });
};


/**
 * Get all clinics, with optional filters for is_active and is_deleted
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of clinics
 * <AUTHOR>
 */
export const getAllClinics = async (filters = {}) => {
  try {
    logger.info(loggerMessages.FETCHING_CLINICS);
    // Build where clause from filters
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    // Query clinics
    const clinics = await Clinic.findAll({ 
      where,
      order: [['created_at', 'DESC']]
    });
    return clinics;
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_CLINICS, error);
    throw new Error(error.message);
  }
};

/**
 * Get a clinic by its ID
 * @param {number} id - Clinic ID
 * @returns {Promise<Object|null>} The clinic or null if not found
 * <AUTHOR>
 */
export const getClinicById = async (id) => {
  try {
    logger.info(`${loggerMessages.FETCHING_CLINIC_BY_ID}: ${id}`);
    // Query by primary key
    const clinic = await Clinic.findByPk(id);
    return clinic;
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_FETCHING_CLINIC_BY_ID}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Update a clinic by its ID
 * @param {number} id - Clinic ID
 * @param {Object} clinicData - Data to update
 * @returns {Promise<Object|null>} The updated clinic or null if not found
 * <AUTHOR>
 */
export const updateClinic = async (id, clinicData) => {
  try {
    logger.info(`${loggerMessages.UPDATING_CLINIC}: ${id}`);
    // Update the clinic
    await Clinic.update(clinicData, { where: { id } });
    // Fetch and return the updated clinic
    const updatedClinic = await Clinic.findByPk(id);
    return updatedClinic;
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_UPDATING_CLINIC}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Soft delete a clinic by its ID (sets is_deleted to true)
 * @param {number} id - Clinic ID
 * @returns {Promise<Object|null>} The deleted clinic or null if not found
 * <AUTHOR>
 */
export const deleteClinic = async (id) => {
  try {
    logger.info(`${loggerMessages.DELETING_CLINIC}: ${id}`);
    // Set is_deleted to true
    const [updatedRows] = await Clinic.update({ is_deleted: true }, { where: { id } });
    if (!updatedRows) return null;
    // Return the soft-deleted clinic
    return await Clinic.findByPk(id);
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_DELETING_CLINIC}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Create a new clinic
 * @param {Object} clinicData - Data for the new clinic
 * @returns {Promise<Object>} The newly created clinic
 * <AUTHOR>
 */
export const createClinic = async (clinicData) => {
  try {
    const newClinic = await Clinic.create(clinicData);
    return newClinic;
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_CLINIC, error);
    throw new Error(error.message);
  }
}; 