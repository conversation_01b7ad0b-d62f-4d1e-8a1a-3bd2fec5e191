/**
 * Doctor Service: Handles all business logic and DB operations for doctors.
 * Uses the Doctor Sequelize model.
 * <AUTHOR>
 */
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { Doctor } from '../models/index.js';
import { Op } from 'sequelize';
import * as constants from '../utils/constants.utils.js';

/**
 * Check if a doctor exists by email or phone number
 * @param {Object} params - { doctor_email, doctor_phonenumber }
 * @returns {Promise<Object|null>} The doctor if found, else null
 * <AUTHOR>
 */
export const findDoctorByEmailOrPhone = async ({ doctor_email, clinic_id }) => {  
  return await Doctor.findOne({
    where: {
      doctor_email: doctor_email,
      clinic_id: clinic_id
    },
  });
};

/**
 * Create a new doctor
 * @param {Object} doctorData - Data for the new doctor
 * @returns {Promise<Object>} The created doctor
 * <AUTHOR>
 */
export const createDoctor = async (doctorData) => {
  try {
    // Log the creation attempt
    logger.info(loggerMessages.CREATING_DOCTOR);
    // Create and return the new doctor
    const newDoctor = await Doctor.create(doctorData);
    return newDoctor;
  } catch (error) {
    // Log and rethrow errors
    logger.error(loggerMessages.ERROR_CREATING_DOCTOR, error);
    throw new Error(error.message);
  }
};

/**
 * Get all doctors, with optional filters for is_active and is_deleted
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of doctors
 * <AUTHOR>
 */
export const getAllDoctors = async (filters = {}) => {
  try {
    // Log the fetch attempt
    logger.info(loggerMessages.FETCHING_DOCTORS);
    // Build where clause from filters
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.clinic_id !== undefined) where.clinic_id = filters.clinic_id;
    // Query doctors
    const doctors = await Doctor.findAll({ 
      where,
      order: [['created_at', 'DESC']]
    });
    return doctors;
  } catch (error) {
    // Log and rethrow errors
    logger.error(loggerMessages.ERROR_FETCHING_DOCTORS, error);
    throw new Error(error.message);
  }
};

/**
 * Get a doctor by its ID
 * @param {number} id - Doctor ID
 * @returns {Promise<Object|null>} The doctor or null if not found
 * <AUTHOR>
 */
export const getDoctorById = async (id) => {
  try {
    // Log the fetch attempt
    logger.info(loggerMessages.FETCHING_DOCTOR_BY_ID);
    // Query by primary key
    const doctor = await Doctor.findByPk(id);
    return doctor;
  } catch (error) {
    // Log and rethrow errors
    logger.error(loggerMessages.ERROR_FETCHING_DOCTOR_BY_ID, error);
    throw new Error(error.message);
  }
};

/**
 * Update a doctor by its ID
 * @param {number} id - Doctor ID
 * @param {Object} doctorData - Data to update
 * @returns {Promise<Object|null>} The updated doctor or null if not found
 * <AUTHOR>
 */
export const updateDoctor = async (id, doctorData) => {
  try {
    // Log the update attempt
    logger.info(loggerMessages.UPDATING_DOCTOR);
    // Update the doctor
    await Doctor.update(doctorData, { where: { id } });
    // Fetch and return the updated doctor
    const updatedDoctor = await Doctor.findByPk(id);
    return updatedDoctor;
  } catch (error) {
    // Log and rethrow errors
    logger.error(loggerMessages.ERROR_UPDATING_DOCTOR, error);
    throw new Error(error.message);
  }
};

/**
 * Soft delete a doctor by its ID (sets is_deleted to true)
 * @param {number} id - Doctor ID
 * @returns {Promise<Object|null>} The deleted doctor or null if not found
 * <AUTHOR>
 */
export const deleteDoctor = async (id) => {
  try {
    // Log the delete attempt
    logger.info(loggerMessages.DELETING_DOCTOR);
    // Set is_deleted to true
    const [updatedRows] = await Doctor.update({ is_deleted: true }, { where: { id } });
    if (!updatedRows) return null;
    // Return the soft-deleted doctor
    return await Doctor.findByPk(id);
  } catch (error) {
    // Log and rethrow errors
    logger.error(loggerMessages.ERROR_DELETING_DOCTOR, error);
    throw new Error(error.message);
  }
}; 