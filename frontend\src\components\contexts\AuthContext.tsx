"use client";
import React, { createContext, useContext, useEffect } from "react";
import { useAuth as useAuthHook } from "../../hooks/useAuth";
import { setupSessionManager } from "../../hooks/useSessionRefresh";

interface AuthContextType {
  user: Record<string, unknown> | null;
  accessToken: string | null;
  refreshTokenValue: string | null;
  loading: boolean;
  error: string;
  login: ReturnType<typeof useAuthHook>["login"];
  register: ReturnType<typeof useAuthHook>["register"];
  logout: ReturnType<typeof useAuthHook>["logout"];
  refreshToken: ReturnType<typeof useAuthHook>["refreshToken"];
  forgotPassword: ReturnType<typeof useAuthHook>["forgotPassword"];
  resetPassword: ReturnType<typeof useAuthHook>["resetPassword"];
  changePassword: ReturnType<typeof useAuthHook>["changePassword"];
  setAccessToken: (token: string | null) => void;
  setRefreshToken: (token: string | null) => void;
  setUser: (user: Record<string, unknown> | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const auth = useAuthHook();

  useEffect(() => {
    // Only setup session manager if we're in the browser
    if (typeof window !== "undefined") {
      // Pass setAccessToken to session manager for proactive refresh
      const cleanup = setupSessionManager(
        auth.logout,
        auth.setAccessToken
      );
      return cleanup;
    }
  }, [auth.logout, auth.setAccessToken, auth.getToken]);

  // Provide refreshTokenValue and setRefreshToken in context
  const contextValue = {
    ...auth,
    refreshTokenValue: auth.refreshTokenValue,
    setRefreshToken: auth.setRefreshToken,
  };
  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
