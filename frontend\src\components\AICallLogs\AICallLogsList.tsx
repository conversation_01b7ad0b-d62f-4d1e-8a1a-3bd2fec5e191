import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Content } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import {
  PhoneCall,
  PhoneIncoming,
  PhoneOutgoing,
  Clock,
  MessageSquare,
  FileText,
  User,
  Bot,
} from "lucide-react";
import {
  AI_CALL_LOGS_RECENT_ACTIVITY,
  AI_CALL_LOGS_CONVERSATION_SUMMARY,
  AI_CALL_LOGS_CALL_DETAILS,
  AI_CALL_LOGS_SENTIMENT,
} from "@/Constants/AICallLogs";

import { TransformedCallLog, TranscriptMessage } from "@/utils/aiCallLogUtils";

// Props for the AICallLogsList component
interface AICallLogsListProps {
  callLogs: TransformedCallLog[]; // List of call logs to display
  selectedCall: number | null; // Currently expanded call log (by id)
  setSelectedCall: (id: number | null) => void; // Handler to expand/collapse call details
  getStatusIcon: (status: string) => React.ElementType; // Returns icon for call status
  getStatusColor: (status: string) => string; // Returns color class for status badge
  getSentimentColor: (sentiment: string) => string; // Returns color class for sentiment
}

/**
 * Formats time in seconds to MM:SS format
 */
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Renders a list of AI call logs with expandable details for each call.
 * Shows call type, patient, phone, time, duration, status, outcome, and details on expand.
 */
const AICallLogsList: React.FC<AICallLogsListProps> = ({
  callLogs,
  selectedCall,
  setSelectedCall,
  getStatusIcon,
  getStatusColor,
  getSentimentColor,
}) => {
  const [transcriptDialogOpen, setTranscriptDialogOpen] = useState(false);
  const [selectedTranscript, setSelectedTranscript] = useState<TranscriptMessage[]>([]);
  const [selectedCallInfo, setSelectedCallInfo] = useState<{ patient: string; time: string } | null>(null);

  const handleTranscriptClick = (transcriptMessages: TranscriptMessage[], callInfo: { patient: string; time: string }) => {
    setSelectedTranscript(transcriptMessages);
    setSelectedCallInfo(callInfo);
    setTranscriptDialogOpen(true);
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            {/* Main icon and section title */}
            <PhoneCall className="h-5 w-5 mr-2 text-blue-600" />
            {AI_CALL_LOGS_RECENT_ACTIVITY}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Render each call log entry */}
            {callLogs.map((call) => {
              const StatusIcon = getStatusIcon(call.status);
              
              return (
                <div
                  key={call.id}
                  className="p-4 border border-gray-200 rounded-lg hover:shadow-lg focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-[1.02] active:scale-[0.98]"
                  onClick={() =>
                    setSelectedCall(selectedCall === call.id ? null : call.id)
                  }
                >
                  {/* Top row: call type, patient info, status/outcome badges */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {/* Call type icon (incoming/outgoing) */}
                      <div
                        className={`p-2 rounded-full ${
                          call.type === "incoming" ? "bg-green-100" : "bg-blue-100"
                        }`}
                      >
                        {call.type === "incoming" ? (
                          <PhoneIncoming className="h-5 w-5 text-green-600" />
                        ) : (
                          <PhoneOutgoing className="h-5 w-5 text-blue-600" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {call.patient}
                        </h3>
                        <div className="flex items-center space-x-3 text-sm text-gray-600">
                          <span>{call.phone}</span>
                          <span>{call.time}</span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {call.duration}
                          </span>
                        </div>
                      </div>
                    </div>
                    {/* Status and outcome badges */}
                    <div className="flex items-center space-x-3">
                      <Badge className={getStatusColor(call.status)}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {call.status}
                      </Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        {call.outcome}
                      </Badge>
                    </div>
                  </div>
                  {/* Expanded Details (shown when selected) */}
                  {selectedCall === call.id && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {/* Conversation summary section */}
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                            <MessageSquare className="h-4 w-4 mr-2" />
                            {AI_CALL_LOGS_CONVERSATION_SUMMARY}
                          </h4>
                          <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                            {call.transcript}
                          </p>
                        </div>
                        {/* Call details section */}
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">
                            {AI_CALL_LOGS_CALL_DETAILS}
                          </h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-600">
                                {AI_CALL_LOGS_SENTIMENT}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span
                                  className={`font-medium capitalize ${getSentimentColor(
                                    call.sentiment
                                  )}`}
                                >
                                  {call.sentiment}
                                </span>
                                {/* Transcript Button - Only show if there are actual transcript messages */}
                                {call.transcriptMessages && call.transcriptMessages.length > 0 ? (
                                  <Badge
                                    variant="outline"
                                    className="cursor-pointer hover:bg-purple-50 hover:text-purple-700 hover:border-purple-300 transition-colors bg-purple-50 text-purple-700 border-purple-200"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleTranscriptClick(call.transcriptMessages, {
                                        patient: call.patient,
                                        time: call.time
                                      });
                                    }}
                                    title="View Transcript"
                                  >
                                    <FileText className="h-3 w-3 mr-1" />
                                    Transcript
                                  </Badge>
                                ) : null}
                              </div>
                            </div>
                            {/* Tags for the call */}
                            <div className="flex flex-wrap gap-1 mt-2">
                              {call.tags.map((tag, index) => (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Transcript Dialog */}
      <Dialog open={transcriptDialogOpen} onOpenChange={setTranscriptDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-blue-600" />
              Call Transcript
              {selectedCallInfo && (
                <span className="text-sm font-normal text-gray-600 ml-2">
                  - {selectedCallInfo.patient} ({selectedCallInfo.time})
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-3 pr-4">
              {selectedTranscript.length > 0 ? (
                selectedTranscript.map((message, index) => (
                  <div
                    key={index}
                    className={`flex items-start space-x-3 p-3 rounded-lg ${
                      message.role === "agent" 
                        ? "bg-blue-50 border-l-4 border-blue-400" 
                        : "bg-gray-50 border-l-4 border-gray-400"
                    }`}
                  >
                    <div className="flex-shrink-0">
                      {message.role === "agent" ? (
                        <Bot className="h-4 w-4 text-blue-600" />
                      ) : (
                        <User className="h-4 w-4 text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {message.role}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatTime(message.time_in_call_secs)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700">
                        {message.message}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center text-gray-500 py-8">
                  <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                  <p className="text-lg font-medium">No transcript available</p>
                  <p className="text-sm">This call does not have a detailed transcript.</p>
                </div>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AICallLogsList;
