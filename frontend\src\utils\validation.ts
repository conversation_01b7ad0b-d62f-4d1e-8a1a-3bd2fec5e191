// validation.ts
// Common validation utilities using Shadcn validation patterns
// Provides reusable validation schemas and functions for forms

import * as z from "zod";

// Base validation schemas
export const baseValidationSchemas = {
  // Email validation
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),

  // Phone validation
  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^[\+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number"),

  // Name validation
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces"),

  // Required string validation
  requiredString: z
    .string()
    .min(1, "This field is required"),

  // Optional string validation
  optionalString: z
    .string()
    .optional(),

  // Number validation
  number: z
    .string()
    .min(1, "This field is required")
    .transform((val) => parseInt(val, 10))
    .refine((val) => !isNaN(val) && val > 0, "Please enter a valid number"),

  // Date validation
  date: z
    .string()
    .min(1, "Date is required")
    .refine((date) => {
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    }, "Please enter a valid date"),

  // URL validation
  url: z
    .string()
    .min(1, "URL is required")
    .url("Please enter a valid URL"),

  // Password validation
  password: z
    .string()
    .min(1, "Password is required")
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one uppercase letter, one lowercase letter, and one number"),
};

// Template-specific validation schemas
export const templateValidationSchemas = {
  // Template name validation
  templateName: z
    .string()
    .min(1, "Template name is required")
    .min(3, "Template name must be at least 3 characters")
    .max(100, "Template name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_]+$/, "Template name can only contain letters, numbers, spaces, hyphens, and underscores"),

  // Template type validation
  templateType: z
    .string()
    .min(1, "Template type is required")
    .refine((type) => ["sms", "email", "call_prompt"].includes(type), "Please select a valid template type"),

  // Template content validation
  templateContent: z
    .string()
    .min(1, "Template content is required"),
};

// Patient-specific validation schemas
export const patientValidationSchemas = {
  // Patient name validation
  patientName: baseValidationSchemas.name,

  // Patient date of birth validation
  patientAge: z
    .string()
    .min(1, "Date of birth is required")
    .refine((dateStr: string) => {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return false;
      }
      
      // Check if date is in the future
      const today = new Date();
      if (date > today) {
        return false;
      }
      
      // Calculate age
      const age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();
      
      let actualAge = age;
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        actualAge--;
      }
      
      return actualAge >= 0 && actualAge <= 150;
    }, "Date of birth cannot be in the future and age must be between 0-150 years"),

  // Patient gender validation
  patientGender: z
    .string()
    .min(1, "Gender is required")
    .refine((gender) => {
      const validGenders = ["male", "female", "other", "Male", "Female", "Other"];
      return validGenders.includes(gender);
    }, "Please select a valid gender"),

  // Patient phone validation
  patientPhone: baseValidationSchemas.phone,

  // Patient email validation
  patientEmail: baseValidationSchemas.email,
};

// Doctor-specific validation schemas
export const doctorValidationSchemas = {
  // Doctor name validation
  doctorName: baseValidationSchemas.name,

  // Doctor specialization validation
  doctorSpecialization: z
    .string()
    .min(1, "Specialization is required")
    .min(2, "Specialization must be at least 2 characters")
    .max(100, "Specialization must be less than 100 characters"),

  // Doctor qualification validation
  doctorQualification: z
    .string()
    .min(1, "Qualification is required")
    .min(2, "Qualification must be at least 2 characters")
    .max(200, "Qualification must be less than 200 characters"),

  // Doctor experience validation
  doctorExperience: z
    .string()
    .min(1, "Experience is required")
    .min(1, "Experience must be at least 1 character")
    .max(100, "Experience must be less than 100 characters"),

  // Doctor phone validation
  doctorPhone: baseValidationSchemas.phone,

  // Doctor email validation
  doctorEmail: baseValidationSchemas.email,
};

// Clinic-specific validation schemas
export const clinicValidationSchemas = {
  // Clinic name validation
  clinicName: z
    .string()
    .min(1, "Clinic name is required")
    .min(2, "Clinic name must be at least 2 characters")
    .max(100, "Clinic name must be less than 100 characters"),

  // Clinic address validation
  clinicAddress: z
    .string()
    .min(1, "Address is required")
    .min(5, "Address must be at least 5 characters")
    .max(500, "Address must be less than 500 characters"),

  // Clinic phone validation
  clinicPhone: baseValidationSchemas.phone,

  // Clinic email validation
  clinicEmail: baseValidationSchemas.email,

  // Working hours validation
  workingHours: z
    .string()
    .min(1, "Working hours are required")
    .min(5, "Working hours must be at least 5 characters")
    .max(100, "Working hours must be less than 100 characters"),

  // Working days validation
  workingDays: z
    .string()
    .min(1, "Working days are required")
    .min(3, "Working days must be at least 3 characters")
    .max(200, "Working days must be less than 200 characters"),
};

// Campaign-specific validation schemas
export const campaignValidationSchemas = {
  // Campaign name validation
  campaignName: z
    .string()
    .min(1, "Campaign name is required")
    .min(3, "Campaign name must be at least 3 characters")
    .max(100, "Campaign name must be less than 100 characters"),

  // Start date validation
  startDate: baseValidationSchemas.date,

  // End date validation
  endDate: baseValidationSchemas.date,

  // Target patients validation
  targetPatients: z
    .string()
    .min(1, "Target patients is required")
    .transform((val) => parseInt(val, 10))
    .refine((val) => !isNaN(val) && val > 0 && val <= 10000, "Please enter a valid number of target patients (1-10000)"),
};

// Complete form schemas
export const formSchemas = {
  // Template form schema
  template: z.object({
    name: templateValidationSchemas.templateName,
    type: templateValidationSchemas.templateType,
    content: templateValidationSchemas.templateContent,
  }),

  // Patient form schema
  patient: z.object({
    name: patientValidationSchemas.patientName,
    age: patientValidationSchemas.patientAge,
    gender: patientValidationSchemas.patientGender,
    phone: patientValidationSchemas.patientPhone,
    email: patientValidationSchemas.patientEmail,
    primaryDoctor: z.string().min(1, "Primary doctor is required"),
  }),

  // Doctor form schema
  doctor: z.object({
    name: doctorValidationSchemas.doctorName,
    specialization: doctorValidationSchemas.doctorSpecialization,
    qualification: doctorValidationSchemas.doctorQualification,
    experience: doctorValidationSchemas.doctorExperience,
    phone: doctorValidationSchemas.doctorPhone,
    email: doctorValidationSchemas.doctorEmail,
    clinic: z.string().min(1, "Clinic is required"),
  }),

  // Clinic form schema
  clinic: z.object({
    name: clinicValidationSchemas.clinicName,
    address: clinicValidationSchemas.clinicAddress,
    email: clinicValidationSchemas.clinicEmail,
    phone: clinicValidationSchemas.clinicPhone,
    workingHours: clinicValidationSchemas.workingHours,
    workingDays: clinicValidationSchemas.workingDays,
    timezone: z.string().min(1, "Timezone is required"),
    isActive: z.string().min(1, "Status is required"),
  }),

  // Campaign form schema
  campaign: z.object({
    name: campaignValidationSchemas.campaignName,
    startDate: campaignValidationSchemas.startDate,
    endDate: campaignValidationSchemas.endDate,
    targetPatients: campaignValidationSchemas.targetPatients,
  }),
};

// Validation helper functions
export const validationHelpers = {
  // Validate a single field
  validateField: (schema: z.ZodSchema, value: unknown) => {
    try {
      schema.parse(value);
      return { isValid: true, error: null };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return { isValid: false, error: error.issues[0].message };
      }
      return { isValid: false, error: "Validation failed" };
    }
  },

  // Validate entire form
  validateForm: (schema: z.ZodSchema, data: unknown) => {
    try {
      const result = schema.parse(data);
      return { isValid: true, data: result, errors: null };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.issues.forEach((err: z.ZodIssue) => {
          if (err.path.length > 0) {
            errors[err.path[0] as string] = err.message;
          }
        });
        return { isValid: false, data: null, errors };
      }
      return { isValid: false, data: null, errors: { general: "Validation failed" } };
    }
  },

  // Get field error from form errors
  getFieldError: (errors: Record<string, string> | null, fieldName: string) => {
    return errors?.[fieldName] || null;
  },
};

// Export types
export type TemplateFormData = z.infer<typeof formSchemas.template>;
export type PatientFormData = z.infer<typeof formSchemas.patient>;
export type DoctorFormData = z.infer<typeof formSchemas.doctor>;
export type ClinicFormData = z.infer<typeof formSchemas.clinic>;
export type CampaignFormData = z.infer<typeof formSchemas.campaign>; 