// AddDoctorForm.tsx
// Renders a modal form for adding a new doctor using CommonForm and doctor field config.

import React from "react";
import CommonForm from "@/components/CommonComponents/CommonForm";
import { formConfigs } from "@/components/CommonComponents/formConfigs";

// Import the FormField interface to ensure type compatibility
interface FormField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  defaultValue?: string;
  validation?: (value: string) => string | null;
  gridCols?: number;
  isMulti?: boolean;
  rows?: number;
  country?: string;
  enableAreaCodes?: boolean;
}

/**
 * Props for the AddDoctorForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (doctor data)
 */
interface AddDoctorFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (doctor: Record<string, unknown>) => void;
  initialData?: Record<string, unknown>;
  submitButtonText?: string;
  title?: string;
  clinics?: { id: number; clinic_name: string }[];
}

/**
 * Renders a modal form for adding a new doctor.
 * Uses CommonForm with doctor field configuration.
 */
const AddDoctorForm: React.FC<AddDoctorFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  submitButtonText,
  title,
  clinics,
}) => {
  // Ensure initialData is always Record<string, string>
  const stringInitialData: Record<string, string> | undefined = initialData
    ? Object.fromEntries(Object.entries(initialData).map(([k, v]) => [k, String(v ?? '')]))
    : undefined;

  // Dynamically set clinic options if clinics prop is provided
  let doctorFields: FormField[] = formConfigs.doctor as FormField[];
  if (clinics && clinics.length > 0) {
    doctorFields = doctorFields.map(field => {
      if (field.name === 'clinic') {
        return {
          ...field,
          options: clinics.map(c => ({ value: String(c.id), label: c.clinic_name })),
          defaultValue: '',
          placeholder: 'Select Clinic',
        };
      }
      return field;
    });
  }

  return (
    <CommonForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      formType="doctor"
      fields={doctorFields}
      initialData={stringInitialData}
      submitButtonText={submitButtonText}
      title={title}
    />
  );
};

export default AddDoctorForm;
