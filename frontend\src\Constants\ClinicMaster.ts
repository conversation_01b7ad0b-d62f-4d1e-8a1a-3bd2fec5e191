export const CLINIC_MASTER_TITLE = "Clinic Management";
export const CLINIC_MASTER_SUBTITLE = "Manage clinic locations and facilities";
export const CLINIC_MASTER_ADD_NEW = "Add New Clinic";
export const CLINIC_MASTER_SEARCH_PLACEHOLDER = "Search by clinic name or address...";
export const CLINIC_MASTER_FILTER_ALL = "All";
export const CLINIC_MASTER_FILTER_ACTIVE = "Active";
export const CLINIC_MASTER_FILTER_CLOSED = "Closed";
export const CLINIC_MASTER_LOADING = "Loading clinics...";
export const CLINIC_MASTER_ERROR = "Failed to load clinic data";
export const CLINIC_MASTER_RETRY = "Retry";

export const CLINIC_CARD_LABEL_ADDRESS = "Address:";
export const CLINIC_CARD_LABEL_CONTACT = "Contact:";
export const CLINIC_CARD_LABEL_CAPACITY = "Capacity:";
export const CLINIC_CARD_LABEL_DOCTORS = "Doctors:";
export const CLINIC_CARD_LABEL_TODAY = "Today:";
export const CLINIC_CARD_LABEL_ASSIGNED_DOCTORS = "Assigned Doctors:";
export const CLINIC_CARD_LABEL_FACILITIES = "Facilities:";
export const CLINIC_CARD_BUTTON_VIEW_DETAILS = "View Details";
export const CLINIC_CARD_BUTTON_EDIT = "Edit";
export const CLINIC_CARD_LABEL_ACTIVE = "Active";
export const CLINIC_CARD_LABEL_CLOSED = "Closed";
export const CLINIC_CARD_LABEL_TEMP_CLOSED = "Temporarily Closed";
export const CLINIC_CARD_LABEL_CLOSED_TODAY = "Closed Today";
export const CLINIC_CARD_LABEL_EMAIL = "Email:";
export const CLINIC_CARD_LABEL_PHONE = "Phone:";
export const CLINIC_CARD_LABEL_WORKING_HOURS = "Working Hours:";
export const CLINIC_CARD_LABEL_WORKING_DAYS = "Working Days:";

export const CLINIC_FORM_FIELDS = {
  CLINIC_NAME: "Clinic Name",
  ADDRESS: "Address",
  PHONE: "Phone",
  EMAIL: "Email",
  WORKING_HOURS: "Working Hours",
  WORKING_DAYS: "Working Days",
  CAPACITY: "Capacity",
  FACILITIES: "Facilities (comma-separated)",
};

export const CLINIC_FORM_PLACEHOLDERS = {
  ADDRESS: "123 Healthcare Avenue, Medical District, City 12345",
  PHONE: "Enter the Number with Country Code",
  FACILITIES: "X-Ray, ECG, Lab, Pharmacy",
};

export const CLINIC_FORM_DEFAULTS = {
  WORKING_HOURS: "9:00 AM - 6:00 PM",
  WORKING_DAYS: "Monday - Friday",
  CAPACITY: "30",
};

// Status dropdown options
export const CLINIC_STATUS_OPTIONS = [
  { value: "true", label: "Active" },
  { value: "false", label: "Inactive" },
];

// API messages
export const CLINIC_CREATE_SUCCESS = "Clinic added successfully!";
export const CLINIC_CREATE_FAILED = "Failed to add clinic";
export const CLINIC_UPDATE_SUCCESS = "Clinic updated successfully!";
export const CLINIC_UPDATE_FAILED = "Failed to update clinic";
export const CLINIC_DELETE_SUCCESS = "Clinic deleted successfully!";
export const CLINIC_DELETE_FAILED = "Failed to delete clinic";
export const CLINIC_FETCH_FAILED = "Failed to fetch clinic";
export const CLINICS_FETCH_FAILED = "Failed to fetch clinics";
export const CLINIC_ALREADY_EXISTS = "Clinic already exists";
export const CLINIC_NOT_FOUND = "Clinic not found";
export const CLINIC_FETCHED_SUCCESSFULLY = "Clinic fetched successfully";
export const CLINICS_FETCHED_SUCCESSFULLY = "Clinics fetched successfully";
export const CLINIC_DELETED_SUCCESSFULLY = "Clinic deleted successfully";
export const CLINIC_UPDATED_SUCCESSFULLY = "Clinic updated successfully";
export const CLINIC_CREATED_SUCCESSFULLY = "Clinic created successfully";
export const INTERNAL_SERVER_ERROR = "Internal server error";
export const CLINIC_FETCH_DETAILS_FAILED = "Failed to fetch clinic details";
export const CLINIC_EMAIL_REQUIRED = "Clinic email is required";
export const CLINIC_DETAILS_TITLE = "Clinic Details";

// Delete confirmation dialog
export const CLINIC_DELETE_CONFIRM_TITLE = "Confirm Delete";
export const CLINIC_DELETE_CONFIRM_MESSAGE = "Are you sure you want to delete clinic";

// Patient upload messages
export const PATIENT_UPLOAD_SUCCESS = "Patients uploaded successfully!";
export const PATIENT_UPLOAD_FAILED = "Failed to upload patients"; 
export const PATIENT_UPLOAD_FILE_REQUIRED = "File is required";

// Import confirmation dialog
export const PATIENT_IMPORT_CONFIRM_TITLE = "Confirm Patient Import";
export const PATIENT_IMPORT_CONFIRM_MESSAGE = "Are you sure you want to import patients from";
export const PATIENT_IMPORT_CONFIRM_DESCRIPTION = "This will add new patients to the clinic database.";
export const PATIENT_IMPORT_CONFIRM_BUTTON = "Import Patients";
export const PATIENT_IMPORT_CANCEL_BUTTON = "Cancel";

// Reactivation Schedule Modal
export const REACTIVATION_SCHEDULE_TITLE = "Reactivation Schedule & Frequency";
export const REACTIVATION_SCHEDULE_SUBTITLE = "Configure reactivation settings for";
export const REACTIVATION_SCHEDULE_AFTER = "Schedule After";
export const REACTIVATION_SCHEDULE_BATCH_CALL = "Custom Batch Calling";
export const REACTIVATION_SCHEDULE_AFTER_CONFIG = "Schedule After Configuration";
export const REACTIVATION_SCHEDULE_BATCH_CONFIG = "Custom Batch Calling Configuration";
export const REACTIVATION_SCHEDULE_AFTER_DESC = "Set up automatic reactivation calls after a specified number of days";
export const REACTIVATION_SCHEDULE_BATCH_DESC = "Schedule a specific batch of reactivation calls for a particular date and time";
export const REACTIVATION_SCHEDULE_DAYS_AFTER = "Days After";
export const REACTIVATION_SCHEDULE_TIME = "Time";
export const REACTIVATION_SCHEDULE_CALL_DATE = "Call Date";
export const REACTIVATION_SCHEDULE_CALL_TIME = "Call Time";
export const REACTIVATION_SCHEDULE_SUMMARY = "Summary";
export const REACTIVATION_SCHEDULE_SAVE_SCHEDULE = "Save Schedule";
export const REACTIVATION_SCHEDULE_SAVE_BATCH = "Save Batch Call";
export const REACTIVATION_SCHEDULE_CANCEL = "Cancel";

// Reactivation Schedule Alert Messages
export const REACTIVATION_SCHEDULE_CONFIRM_TITLE = "Confirm Schedule";
export const REACTIVATION_SCHEDULE_CONFIRM_DESC = "Are you sure you want to schedule reactivation calls?";
export const REACTIVATION_SCHEDULE_BATCH_CONFIRM_TITLE = "Confirm Batch Call";
export const REACTIVATION_SCHEDULE_BATCH_CONFIRM_DESC = "Are you sure you want to schedule this batch call?";
export const REACTIVATION_SCHEDULE_SUCCESS_TITLE = "Schedule Saved Successfully!";
export const REACTIVATION_SCHEDULE_BATCH_SUCCESS_TITLE = "Batch Call Scheduled Successfully!";
export const REACTIVATION_SCHEDULE_FAILED_TITLE = "Save Failed";
export const REACTIVATION_SCHEDULE_VALIDATION_TITLE = "⚠️ Required Fields Missing";
export const REACTIVATION_SCHEDULE_DATE_REQUIRED_TITLE = "⚠️ Date Required";
export const REACTIVATION_SCHEDULE_TIME_REQUIRED_TITLE = "⚠️ Time Required";
export const REACTIVATION_SCHEDULE_VALIDATION_DESC = "Please select both days and time for the schedule.";
export const REACTIVATION_SCHEDULE_DATE_REQUIRED_DESC = "Please select a date for the batch call schedule.";
export const REACTIVATION_SCHEDULE_TIME_REQUIRED_DESC = "Please select a time for the batch call schedule.";
export const REACTIVATION_SCHEDULE_FAILED_DESC = "Failed to save the schedule. Please try again.";
export const REACTIVATION_SCHEDULE_BATCH_FAILED_DESC = "Failed to save the batch call schedule. Please try again.";

// Reactivation Schedule Modal Labels and Text
export const REACTIVATION_SCHEDULE_MODAL_TITLE = "Reactivation Schedule & Frequency";
export const REACTIVATION_SCHEDULE_MODAL_SUBTITLE = "Configure reactivation settings for";
export const REACTIVATION_SCHEDULE_TAB_SCHEDULE_AFTER = "Schedule After";
export const REACTIVATION_SCHEDULE_TAB_BATCH_CALL = "Custom Batch Calling";
export const REACTIVATION_SCHEDULE_AFTER_CONFIG_TITLE = "Schedule After Configuration";
export const REACTIVATION_SCHEDULE_AFTER_CONFIG_DESC = "Set up automatic reactivation calls after a specified number of days";
export const REACTIVATION_SCHEDULE_BATCH_CONFIG_TITLE = "Custom Batch Calling Configuration";
export const REACTIVATION_SCHEDULE_BATCH_CONFIG_DESC = "Schedule a specific batch of reactivation calls for a particular date and time";
export const REACTIVATION_SCHEDULE_LABEL_DAYS_AFTER = "Days After";
export const REACTIVATION_SCHEDULE_LABEL_TIME = "Time";
export const REACTIVATION_SCHEDULE_LABEL_CALL_DATE = "Call Date";
export const REACTIVATION_SCHEDULE_LABEL_CALL_TIME = "Call Time";
export const REACTIVATION_SCHEDULE_SUMMARY_TITLE = "Summary";
export const REACTIVATION_SCHEDULE_AFTER_SUMMARY_TEXT = "Reactivation calls will be scheduled";
export const REACTIVATION_SCHEDULE_BATCH_SUMMARY_TEXT = "Batch reactivation calls will be scheduled for";
export const REACTIVATION_SCHEDULE_AFTER_SUMMARY_DAYS = "day(s) after the last visit at";
export const REACTIVATION_SCHEDULE_BATCH_SUMMARY_AT = "at";
export const REACTIVATION_SCHEDULE_BUTTON_CANCEL = "Cancel";
export const REACTIVATION_SCHEDULE_BUTTON_SAVE_SCHEDULE = "Save Schedule";
export const REACTIVATION_SCHEDULE_BUTTON_SAVE_BATCH = "Save Batch Call";
export const REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_CALLS = "Yes, Schedule Calls";
export const REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_BATCH = "Yes, Schedule Batch Call";
export const REACTIVATION_SCHEDULE_BUTTON_OK = "OK";
export const REACTIVATION_SCHEDULE_PLACEHOLDER_SELECT_DAYS = "Select days";
export const REACTIVATION_SCHEDULE_NO_DATE_SELECTED = "No date selected";

// Day options for schedule after
export const REACTIVATION_SCHEDULE_DAY_OPTIONS = [
  { value: "1", label: "1 Day" },
  { value: "3", label: "3 Days" },
  { value: "7", label: "7 Days" },
  { value: "14", label: "14 Days" },
  { value: "21", label: "21 Days" },
  { value: "30", label: "30 Days" },
  { value: "60", label: "60 Days" },
  { value: "90", label: "90 Days" },
];

// AM/PM options for time selection
export const REACTIVATION_SCHEDULE_AM_PM_OPTIONS = [
  { value: "AM", label: "AM" },
  { value: "PM", label: "PM" },
];

// Reactivation Schedule API Messages
export const REACTIVATION_SCHEDULE_API_SUCCESS = "Reactivation schedule updated successfully";
export const REACTIVATION_SCHEDULE_API_FAILED = "Failed to update reactivation schedule";
export const REACTIVATION_SCHEDULE_API_ERROR = "An error occurred while updating reactivation schedule";

// Batch Call API Messages
export const BATCH_CALL_API_SUCCESS = "Batch call triggered successfully";
export const BATCH_CALL_API_FAILED = "Failed to trigger batch call";
export const BATCH_CALL_API_ERROR = "An error occurred while triggering batch call";