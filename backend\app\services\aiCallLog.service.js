import { Op } from 'sequelize';
import { AICallLog, Clinic, Patient, User } from '../models/index.js';
import * as logMessages from '../utils/log_messages.utils.js';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Create a new AI call log
 * @param {Object} callLogData - The call log data
 * @returns {Promise<Object>} The created call log with patient details
 */
export const createAICallLog = async (callLogData) => {
  try {
    // Check if a call log with the same conversation_id already exists
    if (callLogData.conversation_id) {
      const existingCallLog = await AICallLog.findOne({
        where: { conversation_id: callLogData.conversation_id }
      });
      
      if (existingCallLog) {
        throw new Error(`AI call log with conversation_id '${callLogData.conversation_id}' already exists`);
      }
    }
    
    // Create the call log - transcript will be updated later via webhook
    const newCallLog = await AICallLog.create(callLogData);
    
    // Fetch the created call log with patient details
    const callLogWithDetails = await AICallLog.findOne({
      where: { id: newCallLog.id },
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name', 'location'],
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'phone_number'],
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });
    
    return callLogWithDetails;
  } catch (error) {
    throw new Error(`${logMessages.ERROR_CREATING_AI_CALL_LOG}: ${error.message}`);
  }
};

/**
 * Get all AI call logs with optional filters
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} Array of call logs
 */
export const getAllAICallLogs = async (filters = {}) => {
  try {
    const where = {
      is_deleted: false,
      is_active: true,
    };

    // Apply filters
    if (filters.direction) {
      where.direction = filters.direction;
    }

    if (filters.call_status) {
      where.call_status = filters.call_status;
    }

    if (filters.clinic_id) {
      where.clinic_id = filters.clinic_id;
    }

    if (filters.patient_id) {
      where.patient_id = filters.patient_id;
    }

    if (filters.start_date && filters.end_date) {
      where.call_time_date = {
        [Op.between]: [filters.start_date, filters.end_date],
      };
    }

    const callLogs = await AICallLog.findAll({
      where,
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name', 'location'],
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'phone_number'],
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    return callLogs;
  } catch (error) {
    throw new Error(`${logMessages.ERROR_FETCHING_AI_CALL_LOGS}: ${error.message}`);
  }
}; 