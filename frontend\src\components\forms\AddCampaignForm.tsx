// AddCampaignForm.tsx
// Renders a modal form for adding a new campaign using CommonForm and campaign field config.

import React from "react";
import CommonForm from "@/components/CommonComponents/CommonForm";
import { formConfigs } from "@/components/CommonComponents/formConfigs";

/**
 * Props for the AddCampaignForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (campaign data)
 */
interface AddCampaignFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (campaign: Record<string, unknown>) => void;
}

/**
 * Renders a modal form for adding a new campaign.
 * Uses CommonForm with campaign field configuration.
 */
const AddCampaignForm: React.FC<AddCampaignFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  return (
    <CommonForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      formType="campaign"
      fields={formConfigs.campaign}
    />
  );
};

export default AddCampaignForm;
