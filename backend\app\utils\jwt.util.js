import jwt from 'jsonwebtoken';
import * as constants from './constants.utils.js';

const JWT_SECRET = process.env.JWT_SECRET;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;
const REFRESH_TOKEN_EXPIRY = process.env.REFRESH_TOKEN_EXPIRY;

// Generate JWT access token
function createAccessToken(payload, options = {}) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: constants.TOKEN_EXPIRY,
    ...options,
  });
}

// Verify JWT access token
function verifyAccessToken(token) {
  return jwt.verify(token, JWT_SECRET);
}

// Generate JWT refresh token
function createRefreshToken(payload, options = {}) {
  return jwt.sign(payload, JWT_REFRESH_SECRET, {
    expiresIn: REFRESH_TOKEN_EXPIRY,
    ...options,
  });
}

// Verify JWT refresh token
function verifyRefreshToken(token) {
  try {
    return jwt.verify(token, JWT_REFRESH_SECRET);
  } catch (err) {
    const error = new Error(constants.INVALID_REFRESH_TOKEN);
    error.name = constants.JSON_WEB_TOKEN_ERROR;
    throw error;
  }
}

// Generate JWT reset password token
function createResetPasswordToken(payload, options = {}) {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: constants.RESET_PASSWORD_TOKEN_EXPIRY,
    ...options,
  });
}

// Verify JWT reset password token
function verifyResetPasswordToken(token) {
  return jwt.verify(token, JWT_SECRET);
}

export {
  createAccessToken,
  verifyAccessToken,
  createRefreshToken,
  verifyRefreshToken,
  createResetPasswordToken,
  verifyResetPasswordToken,
};
