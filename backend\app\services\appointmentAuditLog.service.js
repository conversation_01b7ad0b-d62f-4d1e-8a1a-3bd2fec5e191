import { Op } from 'sequelize';
import { AppointmentAuditLog, User } from '../models/index.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';

/**
 * Log appointment audit actions
 * @param {Object} auditData - Audit data object
 * @param {string} auditData.action - Action performed (CREATE, UPDATE, DELETE)
 * @param {number} auditData.record_id - ID of the appointment record
 * @param {number} auditData.user_id - ID of the user performing the action
 * @param {string} auditData.old_value - Previous value (for updates/deletes)
 * @param {string} auditData.new_value - New value (for creates/updates)
 * @param {string} auditData.description - Description of the action
 * @param {string} auditData.error_details - Error details if any
 * @returns {Promise<Object>} The created audit log entry
 */
export const logAppointmentAudit = async (auditData) => {
  try {
    const auditLogEntry = await AppointmentAuditLog.create({
      action: auditData.action,
      record_id: auditData.record_id,
      user_id: auditData.user_id,
      old_value: auditData.old_value || null,
      new_value: auditData.new_value || null,
      description: auditData.description || null,
      error_details: auditData.error_details || null,
      timestamp: new Date(),
    });
    return auditLogEntry;
  } catch (error) {
    logger.error(loggerMessages.ERROR_LOGGING_APPOINTMENT_AUDIT, error);
    throw new Error(constants.FAILED_TO_LOG_APPOINTMENT_AUDIT);
  }
};

/**
 * Get audit logs for a specific appointment
 * @param {number} recordId - Appointment ID
 * @returns {Promise<Array>} Array of audit log entries
 */
export const getAppointmentAuditLogs = async (recordId) => {
  try {
    const auditLogs = await AppointmentAuditLog.findAll({
      where: { record_id: recordId },
      order: [['timestamp', 'DESC']],
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    return auditLogs;
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_APPOINTMENT_AUDIT_LOGS, error);
    throw new Error(constants.FAILED_TO_FETCH_APPOINTMENT_AUDIT_LOGS);
  }
};

/**
 * Get all appointment audit logs with optional filters
 * @param {Object} filters - Filter options
 * @returns {Promise<Array>} Array of audit log entries
 */
export const getAllAppointmentAuditLogs = async (filters = {}) => {
  try {
    const where = {};
    if (filters.action) where.action = filters.action;
    if (filters.user_id) where.user_id = filters.user_id;
    if (filters.record_id) where.record_id = filters.record_id;
    if (filters.start_date && filters.end_date) {
      where.timestamp = {
        [Op.between]: [filters.start_date, filters.end_date]
      };
    }

    const auditLogs = await AppointmentAuditLog.findAll({
      where,
      order: [['timestamp', 'DESC']],
      include: [
        {
          model: User,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    return auditLogs;
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_ALL_APPOINTMENT_AUDIT_LOGS, error);
    throw new Error(constants.FAILED_TO_FETCH_APPOINTMENT_AUDIT_LOGS);
  }
}; 