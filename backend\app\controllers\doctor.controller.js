/**
 * Doctor Controller: Handles HTTP requests for doctor CRUD operations.
 * Uses doctorService and returns consistent API responses.
 * <AUTHOR>
 */
import * as doctorService from '../services/doctor.service.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { logDoctorAudit } from '../services/auditLog.service.js';
import { AuditActions, DoctorAuditDescriptions } from '../utils/auditlog_messages.utils.js';

/**
 * Create a new doctor
 * @route POST /v1/doctor/create
 * <AUTHOR>
 */
export const createDoctor = async (req, res) => {
  try {
    // Log the creation attempt
    logger.info(loggerMessages.CREATING_DOCTOR);
    // Extract doctor data from request body
    const doctorData = req.body;
    doctorData.doctor_email = doctorData.doctor_email.toLowerCase()
    // Set created_by and updated_by from authenticated user
    if (req.user && req.user.id) {
      doctorData.created_by = req.user.id;
      doctorData.updated_by = req.user.id;
    }
    // Check for existing doctor by email or phone
    const existingDoctor = await doctorService.findDoctorByEmailOrPhone({
      doctor_email: doctorData.doctor_email,
      clinic_id: doctorData.clinic_id

    });

    if (existingDoctor) {
      return res
        .status(status.STATUS_CODE_CONFLICT)
        .json(errorResponse(constants.DOCTOR_ALREADY_EXISTS));
    }
    // Attempt to create the doctor (service will check for duplicates)
    const newDoctor = await doctorService.createDoctor(doctorData);
    // Respond with success
    await logDoctorAudit({
      action: AuditActions.CREATE,
      record_id: newDoctor.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newDoctor),
      description: DoctorAuditDescriptions.DOCTOR_CREATED,
    });
    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.DOCTOR_CREATED_SUCCESSFULLY, newDoctor));
  } catch (error) {
    // Log and handle errors
    logger.error(loggerMessages.ERROR_CREATING_DOCTOR, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    // Handle duplicate error
    if (errorMessage === constants.DOCTOR_ALREADY_EXISTS) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.DOCTOR_ALREADY_EXISTS));
    }
    // Handle other errors
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Get all doctors (with optional filters)
 * @route GET /v1/doctor/list
 * <AUTHOR>
 */
export const getAllDoctors = async (req, res) => {
  try {
    // Log the fetch attempt
    logger.info(loggerMessages.FETCHING_DOCTORS);
    // Extract filters from query params
    const filters = {
      is_active: req.query.is_active,
      is_deleted: req.query.is_deleted,
      clinic_id: req.query.clinic_id
    };
    // Fetch doctors from service
    const doctors = await doctorService.getAllDoctors(filters);
    // Respond with doctor list
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.DOCTORS_FETCHED_SUCCESSFULLY, doctors));
  } catch (error) {
    // Log and handle errors
    logger.error(loggerMessages.ERROR_FETCHING_DOCTORS, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Get a single doctor by ID
 * @route GET /v1/doctor/:id
 * <AUTHOR>
 */
export const getDoctorById = async (req, res) => {
  try {
    // Log the fetch attempt
    logger.info(loggerMessages.FETCHING_DOCTOR_BY_ID);
    // Extract doctor ID from params
    const { id } = req.params;
    // Fetch doctor from service
    const doctor = await doctorService.getDoctorById(id);
    if (!doctor) {
      // Doctor not found
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.DOCTOR_NOT_FOUND));
    }
    // Respond with doctor data
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.DOCTOR_FETCHED_SUCCESSFULLY, doctor));
  } catch (error) {
    // Log and handle errors
    logger.error(loggerMessages.ERROR_FETCHING_DOCTOR_BY_ID, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Update a doctor by ID
 * @route PUT /v1/doctor/:id
 * <AUTHOR>
 */
export const updateDoctor = async (req, res) => {
  try {
    // Log the update attempt
    logger.info(loggerMessages.UPDATING_DOCTOR);
    // Extract doctor ID and update data
    const { id } = req.params;
    const doctorData = req.body;
    doctorData.doctor_email = doctorData.doctor_email.toLowerCase()
    // Set updated_by from authenticated user
    if (req.user && req.user.id) {
      doctorData.updated_by = req.user.id;
      doctorData.updated_at = Date.now()
    }
    
    const checkDoctorEmail = await doctorService.findDoctorByEmailOrPhone({
      doctor_email: doctorData.doctor_email,
      clinic_id: doctorData.clinic_id
    })

    const oldDoctor = await doctorService.getDoctorById(id);

    if (checkDoctorEmail && checkDoctorEmail.id !== oldDoctor.id){
        return res
        .status(status.STATUS_CODE_CONFLICT)
        .json(errorResponse(constants.DOCTOR_ALREADY_EXISTS));
    }
    // Attempt to update the doctor
    const updatedDoctor = await doctorService.updateDoctor(id, doctorData);
    if (!updatedDoctor) {
      // Doctor not found
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.DOCTOR_NOT_FOUND));
    }
    // Respond with updated doctor
    await logDoctorAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldDoctor),
      new_value: JSON.stringify(updatedDoctor),
      description: DoctorAuditDescriptions.DOCTOR_UPDATED,
    });
    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.DOCTOR_UPDATED_SUCCESSFULLY)
      );
  } catch (error) {
    // Log and handle errors
    logger.error(loggerMessages.ERROR_UPDATING_DOCTOR, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Soft delete a doctor by ID
 * @route DELETE /v1/doctor/:id
 * <AUTHOR>
 */
export const deleteDoctor = async (req, res) => {
  try {
    // Log the delete attempt
    logger.info(loggerMessages.DELETING_DOCTOR);
    // Extract doctor ID from params
    const { id } = req.params;
    const oldDoctor = await doctorService.getDoctorById(id);
    // Attempt to soft delete the doctor
    const deletedDoctor = await doctorService.deleteDoctor(id);
    if (!deletedDoctor) {
      // Doctor not found
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.DOCTOR_NOT_FOUND));
    }
    // Respond with deleted doctor info
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.DOCTOR_DELETED_SUCCESSFULLY, deletedDoctor)
      );
    await logDoctorAudit({
      action: AuditActions.DELETE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldDoctor),
      description: DoctorAuditDescriptions.DOCTOR_DELETED,
    });
  } catch (error) {
    // Log and handle errors
    logger.error(loggerMessages.ERROR_DELETING_DOCTOR, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};
