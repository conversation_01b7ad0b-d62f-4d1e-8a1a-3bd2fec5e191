/**
 * Clinic Controller: Handles HTTP requests for clinic CRUD operations.
 * Uses clinicService and returns consistent API responses.
 * <AUTHOR>
 */
// Import dependencies and utilities
import * as clinicService from '../services/clinic.service.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { logClinicAudit } from '../services/auditLog.service.js';
import { AuditActions, ClinicAuditDescriptions } from '../utils/auditlog_messages.utils.js';

/**
 * Create a new clinic
 * @route POST /v1/clinic/create
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const createClinic = async (req, res) => {
  try {
    logger.info(loggerMessages.CREATING_CLINIC);
    // Extract clinic data from request body
    const clinicData = req.body;
    // Set created_by and updated_by from authenticated user
    if (req.user && req.user.id) {
      clinicData.created_by = req.user.id;
      clinicData.updated_by = req.user.id;
    }
    // Attempt to create the clinic (service will check for duplicates)
    // Check for existing clinic by email or phone number
    const existingClinic = await clinicService.findClinicByEmailOrPhone({
      clinic_email: clinicData.clinic_email,
      clinic_phonenumber: clinicData.clinic_phonenumber,
    });
    if (existingClinic) {
      logger.warn(loggerMessages.CLINIC_ALREADY_EXISTS);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_ALREADY_EXISTS));
    }
    // Create the clinic
    const newClinic = await clinicService.createClinic(clinicData);
    // Respond with success
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.CLINIC_CREATED_SUCCESSFULLY, newClinic));
    await logClinicAudit({
      action: AuditActions.CREATE,
      record_id: newClinic.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newClinic),
      description: ClinicAuditDescriptions.CLINIC_CREATED,
    });
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_CLINIC, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    // Handle duplicate error
    if (errorMessage === constants.CLINIC_ALREADY_EXISTS) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_ALREADY_EXISTS));
    }
    // Handle other errors
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Get all clinics (with optional filters)
 * @route GET /v1/clinic/list
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const getAllClinics = async (req, res) => {
  try {
    logger.info(loggerMessages.FETCHING_CLINICS);
    // Extract filters from query params
    const { is_active, is_deleted } = req.query;
    const filters = {};
    if (is_active !== undefined) filters.is_active = is_active === 'true';
    if (is_deleted !== undefined) filters.is_deleted = is_deleted === 'true';
    // Fetch clinics from service
    const clinics = await clinicService.getAllClinics(filters);
    // Respond with clinic list
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.CLINICS_FETCHED_SUCCESSFULLY, clinics));
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_CLINICS, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Get a single clinic by ID
 * @route GET /v1/clinic/:id
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const getClinicById = async (req, res) => {
  try {
    logger.info(loggerMessages.FETCHING_CLINIC_BY_ID);
    // Extract clinic ID from params
    const { id } = req.params;
    // Fetch clinic from service
    const clinic = await clinicService.getClinicById(id);
    if (!clinic) {
      logger.info(loggerMessages.CLINIC_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_NOT_FOUND));
    }
    // Respond with clinic data
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.CLINIC_FETCHED_SUCCESSFULLY, clinic));
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_CLINIC_BY_ID, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Update a clinic by ID
 * @route PUT /v1/clinic/:id
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const updateClinic = async (req, res) => {
  try {
    logger.info(loggerMessages.UPDATING_CLINIC);
    // Extract clinic ID and update data
    const { id } = req.params;
    const clinicData = req.body;
    // Set updated_by from authenticated user
    if (req.user && req.user.id) {
      clinicData.updated_by = req.user.id;
      clinicData.updated_at = Date.now()
    }
    const oldClinic = await clinicService.getClinicById(id);
    // Attempt to update the clinic
    const updatedClinic = await clinicService.updateClinic(id, clinicData);
    if (!updatedClinic) {
      logger.info(loggerMessages.CLINIC_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_NOT_FOUND));
    }
    // Log audit before sending response
    await logClinicAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldClinic),
      new_value: JSON.stringify(updatedClinic),
      description: ClinicAuditDescriptions.CLINIC_UPDATED,
    });
    
    // Respond with updated clinic
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CLINIC_UPDATED_SUCCESSFULLY, updatedClinic)
      );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_CLINIC, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Soft delete a clinic by ID
 * @route DELETE /v1/clinic/:id
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const deleteClinic = async (req, res) => {
  try {
    logger.info(loggerMessages.DELETING_CLINIC);
    // Extract clinic ID from params
    const { id } = req.params;
    // Attempt to soft delete the clinic
    const deletedClinic = await clinicService.deleteClinic(id);
    if (!deletedClinic) {
      logger.info(loggerMessages.CLINIC_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_NOT_FOUND));
    }
    // Respond with deleted clinic info
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CLINIC_DELETED_SUCCESSFULLY, deletedClinic)
      );
    const oldClinic = await clinicService.getClinicById(id);
    await logClinicAudit({
      action: AuditActions.DELETE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldClinic),
      description: ClinicAuditDescriptions.CLINIC_DELETED,
    });
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_CLINIC, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Update a clinic by ID
 * @route PUT /v1/clinic/:id
 * @param {*} req - Express request object
 * @param {*} res - Express response object
 * @returns {Object} JSON response
 * <AUTHOR>
 */
export const updateReactivationDaysClinic = async (req, res) => {
  try {
    logger.info(loggerMessages.UPDATING_CLINIC);
    // Extract clinic ID and update data
    const { id } = req.params;
    const clinicData = req.body;
    // Set updated_by from authenticated user
    if (req.user && req.user.id) {
      clinicData.updated_by = req.user.id;
      clinicData.updated_at = Date.now()
    }
    const oldClinic = await clinicService.getClinicById(id);
    // Attempt to update the clinic
    const updatedClinic = await clinicService.updateClinic(id, clinicData);
    if (!updatedClinic) {
      logger.info(loggerMessages.CLINIC_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CLINIC_NOT_FOUND));
    }
    await logClinicAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldClinic),
      new_value: JSON.stringify(updatedClinic),
      description: ClinicAuditDescriptions.CLINIC_UPDATED,
    });
    // Respond with updated clinic
    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(constants.CLINIC_UPDATED_SUCCESSFULLY, updatedClinic)
      );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_CLINIC, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

