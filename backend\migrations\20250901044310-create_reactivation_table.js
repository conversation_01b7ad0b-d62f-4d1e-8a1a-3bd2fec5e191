import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('reactivations', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    clinic_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'clinics',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    batch_name: {
      type: Sequelize.TEXT,
      allowNull: false,
      comment: 'Name of the reactivation batch (e.g., reactivation-2025-01-09-batch-1)',
    },
    status: {
      type: Sequelize.ENUM('pending', 'in_progress', 'completed', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
    },
    patient_count: {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Total number of patients in this batch',
    },
    scheduled_time_unix: {
      type: Sequelize.BIGINT,
      allowNull: true,
      comment: 'Unix timestamp when the batch should be executed',
    },
    scheduled_time: {
      type: 'TIMESTAMPTZ',
      allowNull: true,
      comment: 'Human readable scheduled time',
    },
    executed_time: {
      type: 'TIMESTAMPTZ',
      allowNull: true,
      comment: 'When the batch was actually executed',
    },
    completed_time: {
      type: 'TIMESTAMPTZ',
      allowNull: true,
      comment: 'When the batch completed (success or failure)',
    },
    elevenlabs_batch_id: {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'External ID from ElevenLabs for tracking',
    },
    call_results: {
      type: Sequelize.JSONB,
      allowNull: true,
      comment: 'Detailed results of the batch call including success/failure counts',
    },
    error_message: {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Error message if the batch failed',
    },
    reactivation_days: {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Number of days since last visit used to determine inactive patients',
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });

  // Add indexes for better performance
  await queryInterface.addIndex('reactivations', ['clinic_id']);
  await queryInterface.addIndex('reactivations', ['status']);
  await queryInterface.addIndex('reactivations', ['scheduled_time_unix']);
  await queryInterface.addIndex('reactivations', ['created_at']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('reactivations');
};
