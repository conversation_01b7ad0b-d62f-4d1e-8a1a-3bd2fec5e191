import React, { useEffect } from 'react';
import { MoreVertical } from 'lucide-react';
import { useAppointment } from '@/hooks/useAppointment';
import { DASHBOARD_APPOINTMENTS } from '@/Constants/Dashboard';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'text-green-600';
    case 'completed':
      return 'text-blue-600';
    case 'reminder':
      return 'text-orange-600';
    case 'scheduled':
      return 'text-purple-600';
    default:
      return 'text-gray-600';
  }
};

const DashboardAppointments = () => {
  const { appointments, loading, getAllAppointments } = useAppointment();

  useEffect(() => {
    getAllAppointments();
  }, [getAllAppointments]);

  // Get the earliest 5 appointments and format them like before
  const formattedAppointments = appointments
    .filter(appointment => {
      const appointmentDate = new Date(appointment.appointment_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return appointmentDate >= today; // Only show future appointments
    })
    .sort((a, b) => {
      const dateA = new Date(a.appointment_date).getTime();
      const dateB = new Date(b.appointment_date).getTime();

      if (dateA !== dateB) {
        return dateA - dateB; // Sort by date ascending
      }

      const timeA = new Date(a.appointment_time).getTime();
      const timeB = new Date(b.appointment_time).getTime();

      return timeA - timeB; // Sort by time ascending
    })
    .slice(0, 5) // Get only the first 5
    .map((appointment) => {
      // Format the data to match our previous structure
      const patientName = appointment.patient 
        ? `${appointment.patient.first_name} ${appointment.patient.last_name}`
        : DASHBOARD_APPOINTMENTS.UNKNOWN_PATIENT;
      
      const doctorName = appointment.doctor?.doctor_name || DASHBOARD_APPOINTMENTS.UNKNOWN_DOCTOR;
      const status = appointment.status?.toLowerCase() || 'confirmed';
      
      // Create detail text like before
      const detail = DASHBOARD_APPOINTMENTS.APPOINTMENT_DETAIL
        .replace('{doctor}', doctorName)
        .replace('{status}', status);
      
      // Display the actual appointment date instead of "minutes ago"
      const appointmentDate = new Date(appointment.appointment_date);
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      let dateDisplay = '';
      if (appointmentDate.toDateString() === today.toDateString()) {
        dateDisplay = DASHBOARD_APPOINTMENTS.DATE_DISPLAY.TODAY;
      } else if (appointmentDate.toDateString() === tomorrow.toDateString()) {
        dateDisplay = DASHBOARD_APPOINTMENTS.DATE_DISPLAY.TOMORROW;
      } else {
        dateDisplay = appointmentDate.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        });
      }
      
      return {
        id: appointment.id,
        name: patientName,
        detail,
        timeAgo: dateDisplay,
        status,
        avatar: `/api/placeholder/32/32`
      };
    });

  if (loading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6" style={{ width: '360px', height: '396px' }}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-xl border border-gray-200 shadow-sm p-6"
      style={{ 
        width: '360px', 
        height: '396px',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{DASHBOARD_APPOINTMENTS.TITLE}</h3>
        <div className="flex items-center space-x-2">
          <div className="cursor-pointer p-1 hover:bg-gray-100 rounded-lg transition-colors">
            <MoreVertical className="h-5 w-5 text-gray-500" />
          </div>
        </div>
      </div>
      
      <div className="space-y-2">
        {formattedAppointments.length > 0 ? (
          formattedAppointments.map((appointment) => (
            <div key={appointment.id} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg transition-colors">
              <div className="w-7 h-7 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-medium text-gray-600">
                  {appointment.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {appointment.name}
                  </h4>
                  <span className="text-xs text-gray-500">{appointment.timeAgo}</span>
                </div>
                <p className={`text-xs ${getStatusColor(appointment.status)} truncate`}>
                  {appointment.detail}
                </p>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">{DASHBOARD_APPOINTMENTS.NO_APPOINTMENTS}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardAppointments;
