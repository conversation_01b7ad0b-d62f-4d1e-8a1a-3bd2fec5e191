import 'dotenv/config';
import { app } from './app/server.js';
import { sequelize, testConnection, healthCheck, closeConnection, getConnectionStatus } from './app/config/aws-config.js';
import express from 'express';
import path from 'path';

const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

const gracefulShutdown = async (signal) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  try {
    await closeConnection();
    console.log('✅ Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

const setupProcessHandlers = () => {
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise);
    console.error('Reason:', reason);
    process.exit(1);
  });

  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
};

const setupStaticFiles = () => {
  const __dirname = path.dirname(
    new URL(import.meta.url).pathname.replace(/^\/+([A-Za-z]:)/, '$1')
  );
  app.use(express.static(path.join(__dirname, 'public')));
};

const initializeDatabase = async () => {
  console.log('🔌 Initializing database connection...');
  
  try {
    const connectionResult = await testConnection();
    
    if (connectionResult.success) {
      console.log('✅ Database connection established successfully');
      console.log(`📊 Connection Status:`, getConnectionStatus());
      return true;
    } else {
      console.error('❌ Database connection failed:', connectionResult);
      return false;
    }
  } catch (error) {
    console.error('❌ Database initialization error:', error);
    return false;
  }
};

const startServer = async () => {
  try {
    console.log('🚀 Starting server...');
    console.log(`🌐 Environment: ${NODE_ENV}`);
    
    // Initialize database connection
    const dbConnected = await initializeDatabase();
    
    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Server startup aborted.');
      process.exit(1);
    }

    setupStaticFiles();

    const server = app.listen(PORT, () => {
      console.log(`✅ Server is running on port ${PORT}`);
      console.log(`🌐 Environment: ${NODE_ENV}`);
      console.log(`🔗 Health check available at: http://localhost:${PORT}/health`);
      console.log(`📊 Database status available at: http://localhost:${PORT}/api/health/db`);
      // console.log(`⏰ Cron status available at: http://localhost:${PORT}/api/health/cron`);
      // console.log('🔄 Cron jobs are automatically started with the server');
    });

    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
      } else {
        console.error('❌ Server error:', error);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

setupProcessHandlers();
startServer();
