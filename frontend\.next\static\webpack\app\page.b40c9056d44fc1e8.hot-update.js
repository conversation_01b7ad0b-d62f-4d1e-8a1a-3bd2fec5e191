"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/reactivation-program/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/reactivation-program/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsGrid */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsGrid.tsx\");\n/* harmony import */ var _components_ReactivationProgram_ClinicSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ReactivationProgram/ClinicSelector */ \"(app-pages-browser)/./src/components/ReactivationProgram/ClinicSelector.tsx\");\n/* harmony import */ var _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/Constants/ReactivationProgram */ \"(app-pages-browser)/./src/Constants/ReactivationProgram.ts\");\n/* harmony import */ var _components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CommonComponents/PageSection */ \"(app-pages-browser)/./src/components/CommonComponents/PageSection.tsx\");\n/* harmony import */ var _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAppointment */ \"(app-pages-browser)/./src/hooks/useAppointment.ts\");\n/* harmony import */ var _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAICallLog */ \"(app-pages-browser)/./src/hooks/useAICallLog.ts\");\n/* harmony import */ var _components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ReactivationProgram/AddReactivationForm */ \"(app-pages-browser)/./src/components/ReactivationProgram/AddReactivationForm.tsx\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsTable */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsTable.tsx\");\n/* harmony import */ var _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useReactivationManagement */ \"(app-pages-browser)/./src/hooks/useReactivationManagement.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReactivationProgram = ()=>{\n    _s();\n    const [isAddReactivationOpen, setIsAddReactivationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.CAMPAIGNS_DATA);\n    const [selectedClinicId, setSelectedClinicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // View state\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"campaigns\");\n    const { aiCallLogs, getAllAICallLogs } = (0,_hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_8__.useAICallLog)();\n    const { appointments, getAllAppointments } = (0,_hooks_useAppointment__WEBPACK_IMPORTED_MODULE_7__.useAppointment)();\n    const { reactivations, error, getReactivationSummary, getReactivationsByClinic } = (0,_hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_11__.useReactivationManagement)();\n    const handleAddCampaign = (data)=>{\n        setCampaigns([\n            ...campaigns,\n            data\n        ]);\n    };\n    const handleClinicSelect = async (clinicId)=>{\n        setSelectedClinicId(clinicId);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAppointments();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAppointments\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAICallLogs();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAICallLogs\n    ]);\n    const today = new Date().toISOString().split(\"T\")[0]; // \"2025-09-15\" format\n    const appointmentCount = Array.isArray(appointments) ? appointments.filter((appointment)=>appointment.appointment_date === today).length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.REACTIVATION_PROGRAM_TITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.REACTIVATION_PROGRAM_SUBTITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"main\",\n                        onClick: ()=>setIsAddReactivationOpen(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.REACTIVATION_PROGRAM_ADD_NEW\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                appointments: appointmentCount,\n                calls: (aiCallLogs === null || aiCallLogs === void 0 ? void 0 : aiCallLogs.length) || 0,\n                successRate: \"0%\",\n                newBookings: 0\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"campaigns\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"campaigns\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Campaigns\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"patients\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"patients\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Patient Management\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"stats\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"stats\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Call Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"campaigns\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.REACTIVATION_CAMPAIGNS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.COMING_SOON_TITLE\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.COMING_SOON_DESCRIPTION\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"patients\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.PATIENT_MANAGEMENT_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: \"Patient Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: 'Patient management functionality has been moved to the Add Reactivation Program form. Click the \"Add New Program\" button to access patient management features.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 161,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"stats\" && // <></>\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_5__.STATISTICS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: \"Call Statistics\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: 'Call statistics functionality has been moved to the Add Reactivation Program form. Click the \"Add New Program\" button to access call statistics and patient management features.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ClinicSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedClinicId: selectedClinicId,\n                onClinicSelect: handleClinicSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                reactivations: reactivations || [],\n                loading: loading\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: isAddReactivationOpen,\n                onClose: ()=>setIsAddReactivationOpen(false),\n                onSubmit: handleAddCampaign\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: '<elevenlabs-convai agent-id=\"agent_01jybn5qtwfnd8twmvjffcb0h3\"></elevenlabs-convai>'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReactivationProgram, \"cGgVgeWXEmINW3sOREAMcJyFMNs=\", false, function() {\n    return [\n        _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_8__.useAICallLog,\n        _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_7__.useAppointment,\n        _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_11__.useReactivationManagement\n    ];\n});\n_c = ReactivationProgram;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactivationProgram);\nvar _c;\n$RefreshReg$(_c, \"ReactivationProgram\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reactivation-program/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useReactivationManagement.ts":
/*!************************************************!*\
  !*** ./src/hooks/useReactivationManagement.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useReactivationManagement: () => (/* binding */ useReactivationManagement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/reactivationService */ \"(app-pages-browser)/./src/services/reactivationService.ts\");\n/* harmony import */ var _utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/commonFunctions */ \"(app-pages-browser)/./src/utils/commonFunctions.ts\");\n\n\n\nfunction useReactivationManagement() {\n    const [reactivations, setReactivations] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [summary, setSummary] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Fetch reactivations for a clinic\n    const getReactivationsByClinic = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationsByClinic]\": async (clinicId)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const data = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.getReactivationsByClinic(clinicId);\n                setReactivations(data);\n                return {\n                    success: true,\n                    data\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to fetch reactivations');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationsByClinic]\"], []);\n    // Fetch reactivation statistics for a clinic\n    const getReactivationStats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationStats]\": async (clinicId, dateRange)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const data = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.getReactivationStats(clinicId, dateRange);\n                setStats(data);\n                return {\n                    success: true,\n                    data\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to fetch reactivation statistics');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationStats]\"], []);\n    // Get reactivation summary for dashboard\n    const getReactivationSummary = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationSummary]\": async (clinicId)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const data = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.getReactivationSummary(clinicId);\n                setSummary(data);\n                return {\n                    success: true,\n                    data\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to fetch reactivation summary');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationSummary]\"], []);\n    // Create a new reactivation record\n    const createReactivation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[createReactivation]\": async (data)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const newReactivation = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.createReactivation(data);\n                setReactivations({\n                    \"useReactivationManagement.useCallback[createReactivation]\": (prev)=>[\n                            newReactivation,\n                            ...prev\n                        ]\n                }[\"useReactivationManagement.useCallback[createReactivation]\"]);\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactSuccessMessage)('Reactivation campaign created successfully');\n                return {\n                    success: true,\n                    data: newReactivation\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to create reactivation campaign');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[createReactivation]\"], []);\n    // Update reactivation status\n    const updateReactivationStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[updateReactivationStatus]\": async (id, status, additionalData)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const updatedReactivation = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.updateReactivationStatus(id, status, additionalData);\n                setReactivations({\n                    \"useReactivationManagement.useCallback[updateReactivationStatus]\": (prev)=>prev.map({\n                            \"useReactivationManagement.useCallback[updateReactivationStatus]\": (r)=>r.id === id ? updatedReactivation : r\n                        }[\"useReactivationManagement.useCallback[updateReactivationStatus]\"])\n                }[\"useReactivationManagement.useCallback[updateReactivationStatus]\"]);\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactSuccessMessage)(\"Reactivation status updated to \".concat(status));\n                return {\n                    success: true,\n                    data: updatedReactivation\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to update reactivation status');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[updateReactivationStatus]\"], []);\n    // Delete a reactivation record\n    const deleteReactivation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[deleteReactivation]\": async (id)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.deleteReactivation(id);\n                setReactivations({\n                    \"useReactivationManagement.useCallback[deleteReactivation]\": (prev)=>prev.filter({\n                            \"useReactivationManagement.useCallback[deleteReactivation]\": (r)=>r.id !== id\n                        }[\"useReactivationManagement.useCallback[deleteReactivation]\"])\n                }[\"useReactivationManagement.useCallback[deleteReactivation]\"]);\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactSuccessMessage)('Reactivation campaign deleted successfully');\n                return {\n                    success: true\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to delete reactivation campaign');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[deleteReactivation]\"], []);\n    // Get all reactivations with filters\n    const getAllReactivations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getAllReactivations]\": async (filters)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const data = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.getReactivations(filters);\n                setReactivations(data);\n                return {\n                    success: true,\n                    data\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to fetch reactivations');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[getAllReactivations]\"], []);\n    // Clear error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[clearError]\": ()=>{\n            setError(null);\n        }\n    }[\"useReactivationManagement.useCallback[clearError]\"], []);\n    // Clear all data\n    const clearData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[clearData]\": ()=>{\n            setReactivations([]);\n            setStats([]);\n            setSummary(null);\n            setError(null);\n        }\n    }[\"useReactivationManagement.useCallback[clearData]\"], []);\n    // Get reactivation by ID\n    const getReactivationById = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationById]\": async (id)=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const data = await _services_reactivationService__WEBPACK_IMPORTED_MODULE_1__.reactivationService.getReactivationById(id);\n                return {\n                    success: true,\n                    data\n                };\n            } catch (error) {\n                const errorMsg = (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.extractErrorMessage)(error, 'Failed to fetch reactivation');\n                (0,_utils_commonFunctions__WEBPACK_IMPORTED_MODULE_2__.compactErrorMessage)(errorMsg);\n                setError(errorMsg);\n                return {\n                    success: false,\n                    error: errorMsg\n                };\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationById]\"], []);\n    // Filter reactivations by status\n    const getReactivationsByStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationsByStatus]\": (status)=>{\n            return reactivations.filter({\n                \"useReactivationManagement.useCallback[getReactivationsByStatus]\": (r)=>r.status === status\n            }[\"useReactivationManagement.useCallback[getReactivationsByStatus]\"]);\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationsByStatus]\"], [\n        reactivations\n    ]);\n    // Get recent reactivations (last 7 days)\n    const getRecentReactivations = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getRecentReactivations]\": ()=>{\n            const sevenDaysAgo = new Date();\n            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n            return reactivations.filter({\n                \"useReactivationManagement.useCallback[getRecentReactivations]\": (r)=>new Date(r.created_at) >= sevenDaysAgo\n            }[\"useReactivationManagement.useCallback[getRecentReactivations]\"]);\n        }\n    }[\"useReactivationManagement.useCallback[getRecentReactivations]\"], [\n        reactivations\n    ]);\n    // Get reactivation count by status\n    const getReactivationCountByStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useReactivationManagement.useCallback[getReactivationCountByStatus]\": ()=>{\n            const counts = {\n                pending: 0,\n                in_progress: 0,\n                completed: 0,\n                failed: 0,\n                cancelled: 0\n            };\n            reactivations.forEach({\n                \"useReactivationManagement.useCallback[getReactivationCountByStatus]\": (r)=>{\n                    if (counts.hasOwnProperty(r.status)) {\n                        counts[r.status]++;\n                    }\n                }\n            }[\"useReactivationManagement.useCallback[getReactivationCountByStatus]\"]);\n            return counts;\n        }\n    }[\"useReactivationManagement.useCallback[getReactivationCountByStatus]\"], [\n        reactivations\n    ]);\n    return {\n        // State\n        reactivations,\n        stats,\n        summary,\n        loading,\n        error,\n        // Actions\n        getReactivationsByClinic,\n        getReactivationStats,\n        getReactivationSummary,\n        createReactivation,\n        updateReactivationStatus,\n        deleteReactivation,\n        getAllReactivations,\n        getReactivationById,\n        // Utility functions\n        clearError,\n        clearData,\n        getReactivationsByStatus,\n        getRecentReactivations,\n        getReactivationCountByStatus\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useReactivationManagement.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/reactivationService.ts":
/*!*********************************************!*\
  !*** ./src/services/reactivationService.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   reactivationService: () => (/* binding */ reactivationService)\n/* harmony export */ });\n/* harmony import */ var _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/axios.utils */ \"(app-pages-browser)/./src/utils/axios.utils.ts\");\n\nclass ReactivationService {\n    /**\r\n   * Get reactivation records for a specific clinic\r\n   */ async getReactivationsByClinic(clinicId) {\n        try {\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.get(\"/patient/reactivations/\".concat(clinicId));\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to fetch reactivations for clinic');\n            }\n        } catch (error) {\n            console.error('Error fetching reactivations by clinic:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get all reactivations with optional filters\r\n   */ async getReactivations(filters) {\n        try {\n            const queryParams = new URLSearchParams();\n            if (filters === null || filters === void 0 ? void 0 : filters.clinic_id) queryParams.append('clinic_id', filters.clinic_id.toString());\n            if (filters === null || filters === void 0 ? void 0 : filters.status) queryParams.append('status', filters.status);\n            if (filters === null || filters === void 0 ? void 0 : filters.start_date) queryParams.append('start_date', filters.start_date);\n            if (filters === null || filters === void 0 ? void 0 : filters.end_date) queryParams.append('end_date', filters.end_date);\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.get(\"/reactivations?\".concat(queryParams.toString()));\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to fetch reactivations');\n            }\n        } catch (error) {\n            console.error('Error fetching reactivations:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get reactivation statistics for a clinic\r\n   */ async getReactivationStats(clinicId, dateRange) {\n        try {\n            const queryParams = new URLSearchParams();\n            if (dateRange === null || dateRange === void 0 ? void 0 : dateRange.start_date) queryParams.append('start_date', dateRange.start_date);\n            if (dateRange === null || dateRange === void 0 ? void 0 : dateRange.end_date) queryParams.append('end_date', dateRange.end_date);\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.get(\"/patient/reactivations/\".concat(clinicId, \"/stats?\").concat(queryParams.toString()));\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to fetch reactivation statistics');\n            }\n        } catch (error) {\n            console.error('Error fetching reactivation stats:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get a single reactivation record by ID\r\n   */ async getReactivationById(id) {\n        try {\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.get(\"/reactivation/\".concat(id));\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to fetch reactivation');\n            }\n        } catch (error) {\n            console.error('Error fetching reactivation by ID:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Create a new reactivation record\r\n   */ async createReactivation(data) {\n        try {\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.post('/reactivation', data);\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to create reactivation');\n            }\n        } catch (error) {\n            console.error('Error creating reactivation:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update a reactivation record\r\n   */ async updateReactivation(id, data) {\n        try {\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.put(\"/reactivation/\".concat(id), data);\n            if (response && response.status) {\n                return response.data;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to update reactivation');\n            }\n        } catch (error) {\n            console.error('Error updating reactivation:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Update reactivation status with automatic timestamp handling\r\n   */ async updateReactivationStatus(id, status) {\n        let additionalData = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const updateData = {\n            status,\n            ...additionalData\n        };\n        // Set timestamps based on status\n        if (status === 'in_progress') {\n            updateData.executed_time = new Date().toISOString();\n        } else if ([\n            'completed',\n            'failed'\n        ].includes(status)) {\n            updateData.completed_time = new Date().toISOString();\n        }\n        return this.updateReactivation(id, updateData);\n    }\n    /**\r\n   * Delete a reactivation record (soft delete)\r\n   */ async deleteReactivation(id) {\n        try {\n            const response = await _utils_axios_utils__WEBPACK_IMPORTED_MODULE_0__.apiRequest.delete(\"/reactivation/\".concat(id));\n            if (response && response.status) {\n                return true;\n            } else {\n                throw new Error((response === null || response === void 0 ? void 0 : response.message) || 'Failed to delete reactivation');\n            }\n        } catch (error) {\n            console.error('Error deleting reactivation:', error);\n            throw error;\n        }\n    }\n    /**\r\n   * Get reactivation summary for dashboard\r\n   */ async getReactivationSummary(clinicId) {\n        try {\n            if (!clinicId) {\n                // If no clinic ID provided, return empty summary\n                return {\n                    total_campaigns: 0,\n                    active_campaigns: 0,\n                    completed_campaigns: 0,\n                    failed_campaigns: 0,\n                    total_patients_contacted: 0,\n                    success_rate: 0\n                };\n            }\n            const reactivations = await this.getReactivationsByClinic(clinicId);\n            const total_campaigns = reactivations.length;\n            const active_campaigns = reactivations.filter((r)=>r.status === 'in_progress').length;\n            const completed_campaigns = reactivations.filter((r)=>r.status === 'completed').length;\n            const failed_campaigns = reactivations.filter((r)=>r.status === 'failed').length;\n            const total_patients_contacted = reactivations.reduce((sum, r)=>sum + r.patient_count, 0);\n            const successful_campaigns = reactivations.filter((r)=>r.status === 'completed');\n            const success_rate = total_campaigns > 0 ? successful_campaigns.length / total_campaigns * 100 : 0;\n            return {\n                total_campaigns,\n                active_campaigns,\n                completed_campaigns,\n                failed_campaigns,\n                total_patients_contacted,\n                success_rate: Math.round(success_rate * 100) / 100\n            };\n        } catch (error) {\n            console.error('Error getting reactivation summary:', error);\n            throw error;\n        }\n    }\n}\nconst reactivationService = new ReactivationService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (reactivationService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/reactivationService.ts\n"));

/***/ })

});