import { User, Role } from '../models/index.js';
import bcrypt from 'bcryptjs';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';
import dotenv from 'dotenv';
import { Op } from 'sequelize';

dotenv.config();

export const createUser = async (userData) => {
  try {
    const newUser = await User.create(userData);
    return newUser;
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_USER, error);
    throw new Error(error.message);
  }
};

export const checkUserExists = async ({ email, user_phonenumber }) => {
  try {    
    const orConditions = [];
    if (email) orConditions.push({ email });
    if (user_phonenumber) orConditions.push({ user_phonenumber });
    if (orConditions.length === 0) return null;
    return await User.findOne({
      where: {
        [Op.and]: [{ is_deleted: false }, { [Op.or]: orConditions }],
      },
    });
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_USER_BY_EMAIL_OR_PHONE, error);
    throw new Error(error.message);
  }
};

export const findRoleByName = async (roleName) => {
  try {
    const role = await Role.findOne({
      where: { roleName },
    });
    return role;
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_ROLE_BY_NAME, error);
    throw new Error(error.message || constants.INTERNAL_SERVER_ERROR);
  }
};

export const validatePassword = async (enteredPassword, storedPassword) => {
  try {
    return await bcrypt.compare(enteredPassword, storedPassword);
  } catch (error) {
    logger.error(loggerMessages.ERROR_VALIDATING_PASSWORD, error);
    throw new Error(error.message);
  }
};

export const updateUserPassword = async (userId, newPassword) => {
  try {
    await User.update(
      { password_hash: newPassword, updated_at: new Date() },
      { where: { id: userId } }
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_USER_PASSWORD, error);
    throw new Error(error.message);
  }
};
