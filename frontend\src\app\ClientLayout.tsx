"use client";
// ...existing code...
import Layout from "../components/Layout/Layout";
import { usePathname } from "next/navigation";
import { TAB_TO_ROUTE } from "@/Constants/Layout";

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isAuthPage =
    pathname === "/login" ||
    pathname === "/register" ||
    pathname === "/forgot-password" ||
    pathname.startsWith("/reset-password");

  // Redirect root path to login if not authenticated, else dashboard
  if (pathname === "/") {
    if (typeof window !== "undefined") {
      // For session-based auth, we'll let the server handle the redirect
      // The session will be checked by the API calls
      window.location.replace("/login");
    }
    return null;
  }

  // Determine active tab from route using shared TAB_TO_ROUTE
  const activeTab =
    Object.keys(TAB_TO_ROUTE).find(
      (key) => TAB_TO_ROUTE[key as keyof typeof TAB_TO_ROUTE] === pathname
    ) || "dashboard";

  if (isAuthPage) {
    return <>{children}</>;
  }

  // Don't render layout for root path as it will redirect
  if (pathname === "/") {
    return <>{children}</>;
  }

  return <Layout activeTab={activeTab}>{children}</Layout>;
}
