/* Change Password Page - Light Theme with Enhanced shadcn/ui Theming */

/* Custom animations for enhanced UX */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced card hover effects */
.changeCard {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
     0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation: fadeIn 0.6s ease-out;
  max-width: 600px;
  margin: 0;
}

.changeCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(59, 130, 246, 0.1),
     0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

/* Header section */
.changeHeader {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
  margin-top: 0.5rem;
  position: relative;
  animation: fadeIn 0.6s ease-out;
}

/* Title styling */
.changeTitle {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(90deg, #1e40af, #22c55e, #14b8a6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: -0.02em;
  position: relative;
  z-index: 1;
  margin: 0;
}

.changeSubtitle {
  color: #6b7280;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
  margin: 0;
}

/* Form section animations */
.changeForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0;
  animation: fadeIn 0.6s ease-out 0.2s both;
}

/* Form field styling */
.formField {
  animation: scaleIn 0.6s ease-out;
  transition: all 0.3s ease;
}

.formField:hover {
  transform: translateY(-1px);
}

.formField:nth-child(1) {
  animation-delay: 0.1s;
}
.formField:nth-child(2) {
  animation-delay: 0.2s;
}
.formField:nth-child(3) {
  animation-delay: 0.3s;
}

/* Input styling */
.changeInput {
  padding-left: 2.5rem;
  min-height: 44px;
  font-size: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  color: #374151;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  box-sizing: border-box;
}

.changeInput:focus {
  border-color: #2563eb;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.changeInput:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Icon styling */
.changeIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  transition: color 0.3s ease;
}

.changeIconLeft {
  left: 0.75rem;
  pointer-events: none;
}

.changeIconRight {
  right: 0.75rem;
  pointer-events: auto;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

/* Password toggle button */
.changePasswordToggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  outline: none;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: color 0.3s ease;
}

.changePasswordToggle:hover {
  color: #2563eb;
}

/* Button styling */
.changeButton {
  color: #ffffff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.changeButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.changeButton:hover::before {
  left: 100%;
}

.changeButton:focus,
.changeButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.changeButton:disabled {
  background: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
}

.changeButton:disabled::before {
  display: none;
}

/* Error styling */
.changeError {
  font-size: 0.875rem;
  color: #ef4444;
  margin-top: 0.25rem;
  display: block;
  padding-left: 0.2rem;
  animation: fadeIn 0.3s ease-out;
}

/* Success styling */
.changeSuccess {
  color: #22c55e;
  font-weight: 500;
  margin-bottom: 12px;
  display: block;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

/* Password strength indicator */
.passwordStrength {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  backdrop-filter: blur(10px);
  animation: scaleIn 0.4s ease-out;
}

.passwordStrengthTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.passwordChecks {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.passwordCheck {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.3s ease;
}

.passwordCheck.valid {
  color: #059669;
}

.passwordCheck.invalid {
  color: #dc2626;
}

.checkIcon {
  font-weight: bold;
  font-size: 0.875rem;
  min-width: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.passwordCheck.valid .checkIcon {
  color: #059669;
  transform: scale(1.1);
}

.passwordCheck.invalid .checkIcon {
  color: #dc2626;
}

/* Label styling */
.formLabel {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.formLabel:hover {
  color: #2563eb;
}

/* Responsive design */
@media (max-width: 640px) {
  .changeCard {
    border-radius: 0.75rem;
    margin: 0 0.5rem;
    max-width: none;
  }

  .changeTitle {
    font-size: 1.25rem;
  }

  .changeForm {
    gap: 1rem;
    margin-top: 1rem;
  }

  .changeButton {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
  }
}

/* Focus styles for accessibility */
.changeInput:focus-visible,
.changeButton:focus-visible,
.changePasswordToggle:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}
