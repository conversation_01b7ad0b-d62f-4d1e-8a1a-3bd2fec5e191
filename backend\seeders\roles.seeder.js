const roles = [
  {
    roleName: 'super_admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    roleName: 'clinic_admin',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    roleName: 'front_desk',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    roleName: 'agent',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

export async function up(queryInterface, Sequelize) {
  await queryInterface.bulkInsert('roles', roles);
}

export async function down(queryInterface, Sequelize) {
  await queryInterface.bulkDelete('roles', null);
} 