"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Loader2 } from "lucide-react";
import { useAppointment, Appointment } from "@/hooks/useAppointment";

// Define types locally since we're not using supabaseService
interface Clinic {
  id: number;
  name?: string;
  clinic_name?: string;
  address?: string;
  location?: string;
  email?: string;
  clinic_email?: string;
  contact_number?: string;
  clinic_phonenumber?: string;
  working_hours?: string;
  workingHours?: string;
  working_days?: string;
  workingDays?: string;
  timezone?: string;
  is_active?: boolean;
  sms_service?: boolean;
  email_service?: boolean;
  is_deleted?: boolean;
}

interface Doctor {
  id: number;
  doctor_name?: string;
  name?: string;
  specialization?: string;
  doctor_email?: string;
  email?: string;
  is_deleted?: boolean;
}
import SearchBar from "@/components/CommonComponents/SearchBar";
import DetailedClinicCard from "@/components/ClinicMaster/DetailedClinicCard";
import ClinicStatsGrid from "@/components/ClinicMaster/ClinicStatsGrid";
import {
  CLINIC_MASTER_TITLE,
  CLINIC_MASTER_SUBTITLE,
  CLINIC_MASTER_ADD_NEW,
  CLINIC_MASTER_SEARCH_PLACEHOLDER,
  CLINIC_MASTER_FILTER_ALL,
  CLINIC_MASTER_FILTER_ACTIVE,
  CLINIC_MASTER_FILTER_CLOSED,
  CLINIC_MASTER_LOADING,
  CLINIC_MASTER_RETRY,
  CLINIC_FETCH_DETAILS_FAILED,
  CLINIC_DELETE_FAILED,
  CLINIC_DETAILS_TITLE,
  PATIENT_UPLOAD_SUCCESS,
  PATIENT_UPLOAD_FAILED,
  PATIENT_IMPORT_CONFIRM_TITLE,
  PATIENT_IMPORT_CONFIRM_MESSAGE,
  PATIENT_IMPORT_CONFIRM_DESCRIPTION,
  PATIENT_IMPORT_CONFIRM_BUTTON,
  PATIENT_IMPORT_CANCEL_BUTTON,
} from "@/Constants/ClinicMaster";
import PageSection from "@/components/CommonComponents/PageSection";
import { useClinic, AddClinicPayload } from "@/hooks/useClinic";
import { useDoctor } from "@/hooks/useDoctor";
import ClinicMasterTable from "@/components/ClinicMaster/ClinicMasterTable";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { AddClinicForm } from "@/components/forms/AddClinicForm";
import { usePatients } from "@/hooks/usePatients";
import { errorMessage, successMessage, formatPhoneNumber } from "@/utils/commonFunctions";
import ReactivationScheduleModal from "@/components/ClinicMaster/ReactivationScheduleModal";
import ConfirmAlertDialog from "@/components/CommonComponents/ConfirmAlertDialog";

const ClinicMaster = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isAddClinicOpen, setIsAddClinicOpen] = useState(false);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedClinic, setSelectedClinic] = useState<Clinic | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  // const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  // const [clinicToDelete, setClinicToDelete] = useState<Clinic | null>(null);
  const [editFormInitial, setEditFormInitial] = useState<Record<string, string | boolean> | undefined>(undefined);
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isReactivationModalOpen, setIsReactivationModalOpen] = useState(false);
  const [selectedClinicForReactivation, setSelectedClinicForReactivation] = useState<Clinic | null>(null);
  const [isImportConfirmOpen, setIsImportConfirmOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const {
    createClinic,
    getClinics,
    getClinicById,
    updateClinic,
    deleteClinic,
  } = useClinic();
  const { getDoctors } = useDoctor();
  const { uploadPatientList } = usePatients();
  const { getAllAppointments } = useAppointment();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const clinicsResult = await getClinics();
        if (clinicsResult.success && clinicsResult.data) {
          setClinics(clinicsResult.data as Clinic[]);
        } else {
          setError(clinicsResult.error || CLINIC_FETCH_DETAILS_FAILED);
        }
        // Fetch doctors using API (not supabase)
        const doctorsResult = await getDoctors();
        if (doctorsResult.success && doctorsResult.data) {
          setDoctors(
            (doctorsResult.data as Doctor[]).filter((doc: Doctor) => !(doc as unknown as Record<string, unknown>).is_deleted)
          );
        } else {
          setDoctors([]);
        }
        // Fetch appointments using the hook
        const appointmentsResult = await getAllAppointments();
        if (appointmentsResult.success && appointmentsResult.data) {
          setAppointments(appointmentsResult.data);
        } else {
          setAppointments([]);
        }
        setError(null);
      } catch {
        setError(CLINIC_FETCH_DETAILS_FAILED);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddClinic = async (formData: Record<string, unknown>) => {
    try {
      const payload: AddClinicPayload = {
        clinic_name: String(formData.name),
        location: String(formData.address),
        timezone: String(formData.timezone),
        clinic_email: String(formData.email),
        clinic_phonenumber: String(formData.phone),
        working_hours: JSON.stringify({
          monday: formData.workingHours,
          tuesday: formData.workingHours,
          wednesday: formData.workingHours,
          thursday: formData.workingHours,
          friday: formData.workingHours,
          saturday: formData.workingHours,
        }),
        working_days: JSON.stringify(
          String(formData.workingDays)
            .split(",")
            .map((d: string) => d.trim().toLowerCase())
        ),
        sms_service: Boolean(formData.sms_service),
        email_service: Boolean(formData.email_service),
      };
      const result = await createClinic(payload);
      if (result.success) {
        const clinicsResult = await getClinics();
        if (clinicsResult.success && clinicsResult.data) {
          setClinics(clinicsResult.data as Clinic[]);
        }
        setIsAddClinicOpen(false);
      }
      // error toast is handled in the hook
    } catch {
      // error toast is handled in the hook
    }
  };

  // API: Delete clinic
  const handleDeleteClinic = async (clinicId: number) => {
    try {
      setLoading(true);
      const result = await deleteClinic(clinicId);
      if (result.success) {
        const clinicsResult = await getClinics();
        if (clinicsResult.success && clinicsResult.data) {
          setClinics(clinicsResult.data as Clinic[]);
        }
        // setIsDeleteConfirmOpen(false);
        // setClinicToDelete(null);
      } else {
        setError(result.error || CLINIC_DELETE_FAILED);
      }
    } catch {
      setError(CLINIC_DELETE_FAILED);
    } finally {
      setLoading(false);
    }
  };

  // API: Edit clinic
  const handleEditClinic = async (formData: Record<string, unknown>) => {
    if (!selectedClinic) return;
    try {
      setLoading(true);
      // Map form fields to API keys
      const payload = {
        clinic_name: String(formData.name),
        location: String(formData.address),
        clinic_email: String(formData.email),
        clinic_phonenumber: String(formData.phone),
        working_hours: JSON.stringify(
          (() => {
            // Try to parse as JSON, else treat as string
            try {
              const parsed = JSON.parse(String(formData.workingHours));
              if (typeof parsed === "object") return parsed;
            } catch {
              // If user entered as '9:00 AM - 6:00 PM', use for all days
              const days = [
                "monday",
                "tuesday",
                "wednesday",
                "thursday",
                "friday",
                "saturday",
              ];
              const wh: Record<string, string> = {};
              days.forEach((day) => {
                wh[day] = String(formData.workingHours);
              });
              return wh;
            }
          })()
        ),
        working_days: JSON.stringify(
          (() => {
            // Try to parse as JSON, else treat as comma-separated string
            try {
              const parsed = JSON.parse(String(formData.workingDays));
              if (Array.isArray(parsed))
                return parsed.map((d: string) => d.trim().toLowerCase());
            } catch {
              // If user entered as 'Monday - Friday', split and lower
              return String(formData.workingDays)
                .split(",")
                .map((d: string) => d.trim().toLowerCase());
            }
          })()
        ),
        timezone: String(formData.timezone),
        is_active: String(formData.isActive) === "true",
        sms_service: Boolean(formData.sms_service),
        email_service: Boolean(formData.email_service),
      };
      const result = await updateClinic(Number(selectedClinic.id), payload);
      if (result.success) {
        const clinicsResult = await getClinics();
        if (clinicsResult.success && clinicsResult.data) {
          setClinics(clinicsResult.data as Clinic[]);
        }
        setIsEditModalOpen(false);
        setSelectedClinic(null);
      } else {
        setError(result.error || "");
      }
    } catch {
      setError("");
    } finally {
      setLoading(false);
    }
  };

  // Helper to get clinic status (already defined)
  const getClinicStatus = (clinic: Clinic) => {
    if ((clinic as unknown as Record<string, unknown>).working_hours?.toString().toLowerCase().includes("closed"))
      return "Temporarily Closed";
    return (clinic as unknown as Record<string, unknown>).is_active ? "Active" : "Inactive";
  };

  const filteredClinics = clinics.filter((clinic: Clinic) => {
    const clinicRecord = clinic as unknown as Record<string, unknown>;
    const fullAddress = `${clinicRecord.location || ""}`;
    const matchesSearch =
      (clinicRecord.clinic_name?.toString().toLowerCase() || "").includes(
        searchTerm.toLowerCase()
      ) || fullAddress.toLowerCase().includes(searchTerm.toLowerCase());
    // Status and filter logic can be adjusted if needed
    return matchesSearch;
  });

  // Pagination logic
  const paginatedClinics = filteredClinics.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  // Handlers for ClinicMasterTable
  const handleViewClinic = (clinic: Clinic) => {
    setLoading(true);
    getClinicById(Number(clinic.id)).then((result) => {
      setLoading(false);
      if (result.success && result.data) {
        setSelectedClinic(result.data as Clinic);
        setIsViewModalOpen(true);
      } else {
        setError(result.error || CLINIC_FETCH_DETAILS_FAILED);
      }
    }).catch(() => {
      setLoading(false);
      setError(CLINIC_FETCH_DETAILS_FAILED);
    });
  };

  const handleEditClinicWrapper = (clinic: Clinic) => {
    setSelectedClinic(clinic);
    // Parse workingHours and workingDays for user-friendly prefill
    let workingHours = "";
    let workingDays = "";
    try {
             const whRaw =
         String((clinic as unknown as Record<string, unknown>).working_hours || (clinic as unknown as Record<string, unknown>).workingHours || "");
      if (whRaw) {
        const wh = typeof whRaw === "string" ? JSON.parse(whRaw) : whRaw;
        if (wh && typeof wh === "object") {
          const hoursArr = Object.values(wh);
          const allSame = hoursArr.every((h) => h === hoursArr[0]);
          if (allSame) {
            workingHours = String(hoursArr[0]);
          } else {
            workingHours = Object.entries(wh)
              .map(
                ([day, hours]) =>
                  `${day.charAt(0).toUpperCase() + day.slice(1)}: ${hours}`
              )
              .join(", ");
          }
        } else {
          workingHours = whRaw;
        }
      }
         } catch {
       workingHours =
         String((clinic as unknown as Record<string, unknown>).working_hours || (clinic as unknown as Record<string, unknown>).workingHours || "");
     }
    workingDays =
      String((clinic as unknown as Record<string, unknown>).working_days || (clinic as unknown as Record<string, unknown>).workingDays || "");
    try {
      if (workingDays) {
        let days = workingDays;
        if (typeof days === "string") {
          try {
            const parsed = JSON.parse(days);
            if (Array.isArray(parsed)) {
              days = parsed.join(", ");
            } else {
              days = String(parsed);
            }
          } catch {
            days = String(days);
          }
        }
        days = String(days).replace(/\[|\]|"/g, "");
        days = days.replace(
          /(^| - )(\w)/g,
          (match: string, p1: string, p2: string) => p1 + p2.toUpperCase()
        );
        workingDays = days;
      }
    } catch {
      workingDays =
        String((clinic as unknown as Record<string, unknown>).working_days || (clinic as unknown as Record<string, unknown>).workingDays || "");
    }
    setEditFormInitial({
      name: String((clinic as unknown as Record<string, unknown>).clinic_name || clinic.name || ""),
      address: String((clinic as unknown as Record<string, unknown>).location || clinic.address || ""),
      email: String((clinic as unknown as Record<string, unknown>).clinic_email || clinic.email || ""),
      phone: formatPhoneNumber(String((clinic as unknown as Record<string, unknown>).clinic_phonenumber || clinic.contact_number || "")),
      workingHours,
      workingDays,
      timezone: String((clinic as unknown as Record<string, unknown>).timezone || ""),
      isActive:
        (clinic as unknown as Record<string, unknown>).is_active !== undefined
          ? String((clinic as unknown as Record<string, unknown>).is_active)
          : "true",
      sms_service: Boolean((clinic as unknown as Record<string, unknown>).sms_service) || false,
      email_service: Boolean((clinic as unknown as Record<string, unknown>).email_service) || false,
    });
    setIsEditModalOpen(true);
  };

  const handleDeleteClinicWrapper = (clinic: Clinic) => {
    handleDeleteClinic(Number(clinic.id));
  };

  const handleReactivationSchedule = (clinic: Clinic) => {
    setSelectedClinicForReactivation(clinic);
    setIsReactivationModalOpen(true);
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = "/data.csv";
    link.download = "patient_import_template.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // File input ref for import
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [importing, setImporting] = useState(false);
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // reset so same file can be selected again
      fileInputRef.current.click();
    }
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Store the file and show confirmation dialog
    setSelectedFile(file);
    setIsImportConfirmOpen(true);
  };

  const handleConfirmImport = async () => {
    if (!selectedFile || !selectedClinic) return;
    
    setImporting(true);
    setIsImportConfirmOpen(false);
    
    try {
      const result = await uploadPatientList(
        String((selectedClinic as Clinic).id),
        selectedFile
      );
      if (result.success) {
        successMessage(PATIENT_UPLOAD_SUCCESS);
      } else {
        errorMessage(result.error || PATIENT_UPLOAD_FAILED);
      }
    } catch {
      errorMessage(PATIENT_UPLOAD_FAILED);
    } finally {
      setImporting(false);
      setSelectedFile(null);
    }
  };

  if (loading) {
    return (
      <PageSection>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">
              {CLINIC_MASTER_TITLE}
            </h2>
            <p className="text-gray-600">{CLINIC_MASTER_SUBTITLE}</p>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600">{CLINIC_MASTER_LOADING}</span>
          </div>
        </div>
      </PageSection>
    );
  }

  if (error) {
    return (
      <PageSection>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">
              {CLINIC_MASTER_TITLE}
            </h2>
            <p className="text-gray-600">{CLINIC_MASTER_SUBTITLE}</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                {CLINIC_MASTER_RETRY}
              </Button>
            </div>
          </CardContent>
        </Card>
      </PageSection>
    );
  }

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            {CLINIC_MASTER_TITLE}
          </h2>
          <p className="text-gray-600">{CLINIC_MASTER_SUBTITLE}</p>
        </div>
        <Button variant="main" onClick={() => setIsAddClinicOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {CLINIC_MASTER_ADD_NEW}
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <SearchBar
              placeholder={CLINIC_MASTER_SEARCH_PLACEHOLDER}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {CLINIC_MASTER_FILTER_ALL}
                  </SelectItem>
                  <SelectItem value="active">
                    {CLINIC_MASTER_FILTER_ACTIVE}
                  </SelectItem>
                  <SelectItem value="closed">
                    {CLINIC_MASTER_FILTER_CLOSED}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <div className="mb-6">
        <ClinicStatsGrid
          clinics={clinics}
          doctors={doctors}
          appointments={appointments}
          getClinicStatus={getClinicStatus}
        />
      </div>

      {/* Clinic Table with actions */}
      <ClinicMasterTable
        clinics={paginatedClinics}
        page={page}
        pageSize={pageSize}
        total={filteredClinics.length}
        onPageChange={setPage}
        onViewClinic={handleViewClinic}
        onEditClinic={handleEditClinicWrapper}
        onDeleteClinic={handleDeleteClinicWrapper}
        onReactivationSchedule={handleReactivationSchedule}
      />

      {/* Add Clinic Modal */}
      <AddClinicForm
        isOpen={isAddClinicOpen}
        onClose={() => setIsAddClinicOpen(false)}
        onSubmit={handleAddClinic}
      />
      {/* Edit Clinic Modal (reuse AddClinicForm in edit mode) */}
      {isEditModalOpen && selectedClinic && (
        <AddClinicForm
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedClinic(null);
          }}
          onSubmit={handleEditClinic}
          initialData={editFormInitial}
          submitButtonText="Update Clinic"
          title="Edit Clinic"
        />
      )}
      {/* View Clinic Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogTitle className="sr-only">{CLINIC_DETAILS_TITLE}</DialogTitle>
          {selectedClinic && (
            <DetailedClinicCard
              clinic_name={
                String((selectedClinic as unknown as Record<string, unknown>).clinic_name ||
                (selectedClinic as unknown as Record<string, unknown>).name ||
                "")
              }
              location={
                String((selectedClinic as unknown as Record<string, unknown>).location ||
                (selectedClinic as unknown as Record<string, unknown>).address ||
                "")
              }
              clinic_email={
                String((selectedClinic as unknown as Record<string, unknown>).clinic_email ||
                (selectedClinic as unknown as Record<string, unknown>).email ||
                "")
              }
              clinic_phonenumber={
                formatPhoneNumber(String((selectedClinic as unknown as Record<string, unknown>).clinic_phonenumber ||
                (selectedClinic as unknown as Record<string, unknown>).contact_number ||
                ""))
              }
              working_hours={String((selectedClinic as unknown as Record<string, unknown>).working_hours || "")}
              working_days={String((selectedClinic as unknown as Record<string, unknown>).working_days || "")}
              statusBadge={<Badge>{getClinicStatus(selectedClinic)}</Badge>}
              hideActions={true}
            />
          )}
          {/* Import/Export Buttons - Moved to bottom */}
          <div className="flex justify-end gap-2 mt-4 pt-4">
            <input
              type="file"
              ref={fileInputRef}
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              style={{ display: "none" }}
              onChange={handleFileChange}
              disabled={importing}
            />
            <Button
              className="border-2 text-black hover:bg-black hover:text-white"
              onClick={handleFileUpload}
              disabled={importing}
            >
              {importing ? "Importing..." : "Import Data"}
            </Button>
            <Button className="border-2 text-black hover:bg-black hover:text-white" onClick={handleDownload}>
              Download Template
            </Button>
          </div>
        </DialogContent>
      </Dialog>

             {/* Reactivation Schedule Modal */}
       {selectedClinicForReactivation && (
         <ReactivationScheduleModal
           isOpen={isReactivationModalOpen}
           onClose={() => {
             setIsReactivationModalOpen(false);
             setSelectedClinicForReactivation(null);
           }}
           clinicName={String((selectedClinicForReactivation as unknown as Record<string, unknown>).clinic_name || selectedClinicForReactivation.name || "")}
           clinicId={Number(selectedClinicForReactivation.id)}
         />
       )}

       {/* Import Confirmation Dialog */}
       <ConfirmAlertDialog
         open={isImportConfirmOpen}
         onOpenChange={setIsImportConfirmOpen}
         title={PATIENT_IMPORT_CONFIRM_TITLE}
         description={`${PATIENT_IMPORT_CONFIRM_MESSAGE} "${selectedFile?.name}"? ${PATIENT_IMPORT_CONFIRM_DESCRIPTION}`}
         confirmText={PATIENT_IMPORT_CONFIRM_BUTTON}
         cancelText={PATIENT_IMPORT_CANCEL_BUTTON}
         variant="info"
         onConfirm={handleConfirmImport}
       />
     </PageSection>
   );
 };

export default ClinicMaster;
