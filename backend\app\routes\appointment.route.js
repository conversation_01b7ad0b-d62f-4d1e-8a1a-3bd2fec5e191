import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createAppointmentSchema, updateAppointmentSchema, createElevenLabAppointmentSchema } from '../validators/appointment.validator.js';
import * as appointmentController from '../controllers/appointment.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Check time slot availability (no validation needed, just query params)
router.get(
  '/check-availability',
  verifyToken,
  appointmentController.checkTimeSlotAvailability
);

// Get available time slots for a doctor on a specific date
router.get(
  '/available-slots',
  // verifyToken,
  appointmentController.getAvailableTimeSlots
);

// Create a new appointment
// POST /v1/appointment/create
router.post(
  '',
  verifyToken,
  validate(createAppointmentSchema),
  appointmentController.createAppointment
);

router.post(
  '/elevenlab',
  validate(createElevenLabAppointmentSchema),
  appointmentController.createAppointment
);

// Get all appointments
// GET /v1/appointment/list
router.get(
  '',
  verifyToken,
  appointmentController.getAllAppointments
);

// Get appointment by ID
// GET /v1/appointment/:id
router.get(
  '/:id',
  verifyToken,
  appointmentController.getAppointmentById
);

// Update appointment by ID
// PUT /v1/appointment/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateAppointmentSchema),
  appointmentController.updateAppointment
);

// Soft delete appointment by ID
// DELETE /v1/appointment/:id
router.delete(
  '/:id',
  verifyToken,
  appointmentController.deleteAppointment
);

export default router; 