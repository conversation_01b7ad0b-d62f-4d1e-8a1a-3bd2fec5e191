/**
 * SMS Service: Handles sending SMS messages using Twilio
 */
import twilio from 'twilio';
import dotenv from 'dotenv';
import * as constants from '../utils/constants.utils.js';
import { convertUTCToIndianTime } from '../utils/timezone.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';

dotenv.config();

// Initialize Twilio client
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER;

/**
 * Send SMS message using Twilio
 * @param {string} to - Recipient phone number (with country code)
 * @param {string} message - SMS message content
 * @returns {Promise<string>} Success message
 */
export const sendSMS = async (to, message) => {
  if (!TWILIO_PHONE_NUMBER) {
    throw new Error('TWILIO_PHONE_NUMBER environment variable is not set');
  }

  if (!to) {
    throw new Error('Recipient phone number is required');
  }

  if (!message) {
    throw new Error('SMS message content is required');
  }

  try {
    logger.info(loggerMessages.SENDING_SMS_MESSAGE);
    const result = await twilioClient.messages.create({
      body: message,
      from: TWILIO_PHONE_NUMBER,
      to: to
    });

    logger.info(`${loggerMessages.SMS_SENT_SUCCESSFULLY} to ${to}. SID: ${result.sid}`);
    return constants.SMS_SENT_SUCCESS;
  } catch (error) {
    logger.error(`${loggerMessages.SMS_SEND_FAILED} to ${to}:`, error);
    throw new Error(constants.SMS_SEND_FAILED);
  }
};

/**
 * Send appointment confirmation SMS
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} doctorData - Doctor data
 * @param {Object} clinicData - Clinic data
 * @returns {Promise<string>} Success message
 */
export const sendAppointmentConfirmationSMS = async (appointmentData, patientData, doctorData, clinicData) => {
  
  if (!patientData.phone_number) {
    logger.warn(loggerMessages.PATIENT_NO_PHONE_NUMBER);
    return false;
  }

  // Convert UTC time from database to Indian time for display
  const appointmentDate = convertUTCToIndianTime(appointmentData.appointment_time);
  const formattedDate = appointmentDate.toLocaleDateString('en-IN', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  const formattedTime = appointmentDate.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const message = `Hi ${patientData.first_name}! Your appointment with ${doctorData.doctor_name} is confirmed for ${formattedDate} at ${formattedTime}. Please arrive 15 min early. ${clinicData.clinic_name}. Reply STOP to unsubscribe.`;

  try {
    await sendSMS(patientData.phone_number, message);
    
    logger.info(constants.APPOINTMENT_CONFIRMATION_SMS_SENT(patientData, appointmentData));
    return true;
  } catch (error) {
    logger.error(constants.APPOINTMENT_CONFIRMATION_SMS_FAILED(appointmentData), error);
    return false;
  }
};

/**
 * Send appointment cancellation SMS
 * @param {Object} appointmentData - Appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} clinicData - Clinic data
 * @returns {Promise<string>} Success message
 */
export const sendAppointmentCancellationSMS = async (appointmentData, patientData, clinicData) => {
  if (!patientData.phone_number) {
    logger.warn(loggerMessages.PATIENT_NO_PHONE_NUMBER);
    return false;
  }

  // Convert UTC time from database to Indian time for display
  const appointmentDate = convertUTCToIndianTime(appointmentData.appointment_time);
  const formattedDate = appointmentDate.toLocaleDateString('en-IN', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  const formattedTime = appointmentDate.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const message = `Hi ${patientData.first_name}, your appointment on ${formattedDate} at ${formattedTime} has been cancelled. Call ${clinicData.clinic_phonenumber} to reschedule. ${clinicData.clinic_name}. Reply STOP to unsubscribe.`;

  try {
    await sendSMS(patientData.phone_number, message);
    logger.info(constants.APPOINTMENT_CANCELLATION_SMS_SENT(patientData, appointmentData));
    return true;
  } catch (error) {
    logger.error(constants.APPOINTMENT_CANCELLATION_SMS_FAILED(appointmentData), error);
    return false;
  }
};

/**
 * Send appointment update/reschedule SMS
 * @param {Object} appointmentData - Updated appointment data
 * @param {Object} patientData - Patient data
 * @param {Object} doctorData - Doctor data
 * @param {Object} clinicData - Clinic data
 * @returns {Promise<string>} Success message
 */
export const sendAppointmentUpdateSMS = async (appointmentData, patientData, doctorData, clinicData) => {
  if (!patientData.phone_number) {
    logger.warn(loggerMessages.PATIENT_NO_PHONE_NUMBER);
    return false;
  }

  // Convert UTC time from database to Indian time for display
  const appointmentDate = convertUTCToIndianTime(appointmentData.appointment_time);
  const formattedDate = appointmentDate.toLocaleDateString('en-IN', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
  const formattedTime = appointmentDate.toLocaleTimeString('en-IN', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const message = `Hi ${patientData.first_name}! Your appointment with ${doctorData.doctor_name} has been rescheduled to ${formattedDate} at ${formattedTime}. Please arrive 15 min early. ${clinicData.clinic_name}. Reply STOP to unsubscribe.`;

  try {
    await sendSMS(patientData.phone_number, message);
    logger.info(constants.APPOINTMENT_UPDATE_SMS_SENT(patientData, appointmentData));
    return true;
  } catch (error) {
    logger.error(constants.APPOINTMENT_UPDATE_SMS_FAILED(appointmentData), error);
    return false;
  }
};

