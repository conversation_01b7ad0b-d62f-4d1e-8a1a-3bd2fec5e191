// ActiveCampaignsList.tsx
// Renders a list of active campaigns for the Reactivation Program, showing campaign details and actions.
// Used to manage and view campaign progress.

import React from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play } from "lucide-react";
import { REACTIVATION_PROGRAM_STAT_ACTIVE_APPOINTMENTS } from "@/Constants/ReactivationProgram";

/**
 * Represents a single campaign for the active campaigns list.
 * @property id - Unique identifier for the campaign
 * @property name - Campaign name
 * @property status - Campaign status (Active, Completed, Scheduled)
 * @property startDate - Campaign start date
 * @property endDate - Campaign end date
 * @property targetPatients - Number of target patients
 * @property contactedPatients - Number of contacted patients
 * @property successfulBookings - Number of successful bookings
 * @property successRate - Success rate percentage
 * @property lastRun - Last run date
 */
interface Campaign {
  id: number;
  name: string;
  status: string;
  startDate: string;
  endDate: string;
  targetPatients: number;
  contactedPatients: number;
  successfulBookings: number;
  successRate: string;
  lastRun: string;
}

/**
 * Props for the ActiveCampaignsList component
 * @property campaigns - Array of campaign objects
 */
interface ActiveCampaignsListProps {
  campaigns: Campaign[];
}

/**
 * Renders a list of active campaigns for the Reactivation Program.
 * Shows campaign details and actions.
 */
const ActiveCampaignsList: React.FC<ActiveCampaignsListProps> = ({
  campaigns,
}) => (
  <Card>
    <CardHeader>
      <CardTitle>{REACTIVATION_PROGRAM_STAT_ACTIVE_APPOINTMENTS}</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-4">
        {campaigns.map((campaign) => (
          <div
            key={campaign.id}
            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
          >
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <h3 className="font-semibold text-gray-900">{campaign.name}</h3>
                <Badge
                  className={
                    campaign.status === "Active"
                      ? "bg-black text-white border-transparent"
                      : campaign.status === "Completed"
                      ? "bg-gray-100 text-gray-800 border-transparent"
                      : campaign.status === "Scheduled"
                      ? "text-gray-800 border border-gray-200"
                      : ""
                  }
                >
                  {campaign.status}
                </Badge>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm">
                <div>
                  <span className="text-gray-600">Period: </span>
                  <span className="font-medium">
                    {campaign.startDate} to {campaign.endDate}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Progress: </span>
                  <span className="font-medium">
                    {campaign.contactedPatients}/{campaign.targetPatients}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Bookings: </span>
                  <span className="font-medium text-green-600">
                    {campaign.successfulBookings}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Success Rate: </span>
                  <span className="font-medium text-blue-600">
                    {campaign.successRate}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              {campaign.status === "Active" && (
                <Button className="border border-gray-200 text-black" size="sm">
                  <Play className="h-4 w-4" />
                </Button>
              )}
              {campaign.status === "Scheduled" && (
                <Button className="border border-gray-200 text-black" size="sm">
                  <Play className="h-4 w-4" />
                </Button>
              )}
              <Button variant="ghost" size="sm">
                View Details
              </Button>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

export default ActiveCampaignsList;
