import { DataTypes } from 'sequelize';
import * as constants from '../utils/constants.utils.js';

const AICallLogModel = (sequelize) => {
  return sequelize.define('ai_call_logs', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    direction: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        isIn: [constants.AI_CALL_LOG_DIRECTIONS],
      },
    },
    call_time_date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    call_duration: {
      type: DataTypes.INTEGER, // Duration in seconds
      allowNull: false,
    },
    call_status: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        isIn: [constants.AI_CALL_LOG_STATUSES],
      },
    },
    conversation_summary: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    transcript: {
      type: DataTypes.ARRAY(DataTypes.TEXT),
      allowNull: true,
    },
    conversation_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    call_summary_title: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    clinic_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'clinics',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    patient_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'patients',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  }, {
    tableName: 'ai_call_logs',
    timestamps: false,
    underscored: true,
  });
};

export default AICallLogModel; 