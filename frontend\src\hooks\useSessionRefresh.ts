// utils/sessionManager.ts

import { apiRequest } from "../utils/axios.utils";

let activityTimeout: NodeJS.Timeout | null = null;
let intervalId: NodeJS.Timeout | null = null;
const REFRESH_INTERVAL = 55 * 60 * 1000; // 55 minutes (refresh before 1 hour expiry)
const CHECK_INTERVAL = 30 * 60 * 1000; // 30 minutes - background checks
const ACTIVITY_TIMEOUT = 5 * 60 * 1000; // 5 minutes of inactivity before refresh

// Accepts a callback to update accessToken in state if needed
async function checkSession(logout: () => void, onRefresh?: (accessToken: string) => void) {
  try {
    const data = await apiRequest.post("/auth/refresh", {}, { withCredentials: true }) as { status: boolean; data?: { accessToken?: string } };
    
    if (data.status && data.data?.accessToken) {
      if (typeof onRefresh === 'function') {
        onRefresh(data.data.accessToken);
      }
      return true;
    } else {
      logout();
      return false;
    }
  } catch {
    logout();
    return false;
  }
}

function startSessionTimer(logout: () => void, onRefresh?: (accessToken: string) => void) {
  if (activityTimeout) clearTimeout(activityTimeout);
  activityTimeout = setTimeout(async () => {
    const success = await checkSession(logout, onRefresh);
    if (success) {
      startSessionTimer(logout, onRefresh); // Only restart timer if refresh was successful
    }
  }, REFRESH_INTERVAL);
}





// Accepts an optional callback to update accessToken in state
export function setupSessionManager(
  logout: () => void,
  onRefresh?: (accessToken: string) => void
) {
  let lastActivityTime = Date.now();
  let isUserActive = true;

  const activityHandler = async () => {
    const now = Date.now();
    const wasInactive = !isUserActive;
    lastActivityTime = now;
    isUserActive = true;
    
    if (wasInactive) {
      // Always refresh when user becomes active to ensure session is valid
      await checkSession(logout, onRefresh);
    }
  };

  // Throttled activity handler to prevent excessive calls
  let throttledHandler: NodeJS.Timeout | null = null;
  const throttledActivityHandler = () => {
    if (throttledHandler) return;
    throttledHandler = setTimeout(() => {
      activityHandler();
      throttledHandler = null;
    }, 1000); // Throttle to once per second
  };

  window.addEventListener("mousemove", throttledActivityHandler);
  window.addEventListener("keydown", throttledActivityHandler);
  window.addEventListener("click", throttledActivityHandler);
  window.addEventListener("scroll", throttledActivityHandler);
  
  // Start the main session timer (runs every 55 minutes regardless of activity)
  startSessionTimer(logout, onRefresh);

  // Check for user inactivity every minute
  const inactivityInterval = setInterval(() => {
    if (Date.now() - lastActivityTime > ACTIVITY_TIMEOUT) {
      if (isUserActive) {
        isUserActive = false;
      }
    }
  }, 60000); // Check every minute

  // Periodic check for session validity (every 30 minutes)
  intervalId = setInterval(async () => {
    // Only refresh if user is active
    if (isUserActive) {
      await checkSession(logout, onRefresh);
    }
  }, CHECK_INTERVAL);

  return () => {
    window.removeEventListener("mousemove", throttledActivityHandler);
    window.removeEventListener("keydown", throttledActivityHandler);
    window.removeEventListener("click", throttledActivityHandler);
    window.removeEventListener("scroll", throttledActivityHandler);
    if (activityTimeout) clearTimeout(activityTimeout);
    if (intervalId) clearInterval(intervalId);
    if (inactivityInterval) clearInterval(inactivityInterval);
    if (throttledHandler) clearTimeout(throttledHandler);
  };
}
