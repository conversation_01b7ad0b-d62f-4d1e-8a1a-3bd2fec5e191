import express from 'express';
import cors from 'cors';
import mainRoutes from './routes/index.js';
import logger from './config/logger.config.js';
import * as constants from './utils/constants.utils.js';
import * as status from './utils/status_code.utils.js';
import {
  healthCheck,
  getConnectionStatus,
  testConnection,
} from './config/aws-config.js';
import './models/index.js';
import cookieParser from 'cookie-parser';
import {
  handleSequelizeError,
  handleJWTError,
  formatErrorResponse,
} from './utils/error.util.js';
// import { initializeCronJobs, getCronJobStatus } from './services/cron.service.js';

const app = express();

// Initialize cron jobs for automated outbound calls
// const initializeCronJobsWithRetry = () => {
//   try {
//     logger.info('🔄 Initializing cron jobs...');
//     initializeCronJobs();
//     logger.info('✅ Cron jobs initialized successfully');
    
//     // Log cron job status
//     const cronStatus = getCronJobStatus();
//     logger.info('📅 Cron job status:', cronStatus);
    
//     return true;
//   } catch (error) {
//     logger.error('❌ Failed to initialize cron jobs:', error);
//     return false;
//   }
// };

// Initialize cron jobs
// const cronInitialized = initializeCronJobsWithRetry();

// if (!cronInitialized) {
//   logger.warn('⚠️ Cron jobs failed to initialize, but server will continue running');
// }

app.use(
  cors({
    origin: process.env.FRONTEND_BASE_URL,
    credentials: true,
  })
);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url} - ${req.ip}`);
  next();
});

app.use('/api', mainRoutes);

app.get('/', (req, res) => {
  res.status(status.STATUS_CODE_SUCCESS).send(constants.HELLO_WORLD);
});

app.get('/health', async (req, res) => {
  try {
    const healthResult = await healthCheck();
    // const cronStatus = getCronJobStatus();

    if (healthResult.database === 'connected') {
      res.status(status.STATUS_CODE_SUCCESS).json({
        success: true,
        message: 'Server is healthy',
        timestamp: healthResult.timestamp,
        database: healthResult.database,
        status: healthResult.status,
        // cron: {
        //   initialized: cronInitialized,
        //   status: cronStatus.status,
        //   schedules: cronStatus.schedules
        // }
      });
    } else {
      res.status(status.STATUS_CODE_SERVICE_UNAVAILABLE).json({
        success: false,
        message: 'Server health check failed - database disconnected',
        timestamp: healthResult.timestamp,
        database: healthResult.database,
        error: healthResult.connection.error,
        status: healthResult.status,
        //   cron: {
        //     initialized: cronInitialized,
        //     status: cronStatus.status
        // }
      });
    }
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Server health check failed',
      timestamp: new Date().toISOString(),
      database: 'error',
      error: error.message,
      // cron: {
      //   initialized: cronInitialized,
      //   status: 'unknown'
      // }
    });
  }
});

// Dedicated database health endpoint
app.get('/api/health/db', async (req, res) => {
  try {
    const connectionStatus = getConnectionStatus();
    const connectionTest = await testConnection();

    const response = {
      success: connectionTest.success,
      timestamp: new Date().toISOString(),
      database: {
        connected: connectionStatus.connected,
        lastCheck: connectionStatus.lastCheck,
        retryCount: connectionStatus.retryCount,
        type: connectionStatus.databaseType,
        host: connectionStatus.host,
        environment: connectionStatus.isProduction
          ? 'production'
          : 'development',
      },
      connection: connectionTest,
    };

    if (connectionTest.success) {
      res.status(status.STATUS_CODE_SUCCESS).json(response);
    } else {
      res.status(status.STATUS_CODE_SERVICE_UNAVAILABLE).json(response);
    }
  } catch (error) {
    logger.error('Database health check failed:', error);
    res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Database health check failed',
      timestamp: new Date().toISOString(),
      error: error.message,
    });
  }
});

// Dedicated cron status endpoint
// app.get('/api/health/cron', async (req, res) => {
//   try {
//     // const cronStatus = getCronJobStatus();
    
//     const response = {
//       success: cronInitialized,
//       timestamp: new Date().toISOString(),
//       cron: {
//         initialized: cronInitialized,
//         status: cronStatus.status,
//         schedules: cronStatus.schedules,
//         configuration: cronStatus.configuration,
//         description: cronStatus.description
//       }
//     };

//     if (cronInitialized) {
//       res.status(status.STATUS_CODE_SUCCESS).json(response);
//     } else {
//       res.status(status.STATUS_CODE_SERVICE_UNAVAILABLE).json(response);
//     }
//   } catch (error) {
//     logger.error('Cron health check failed:', error);
//     res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json({
//       success: false,
//       message: 'Cron health check failed',
//       timestamp: new Date().toISOString(),
//       error: error.message,
//       cron: {
//         initialized: cronInitialized,
//         status: 'error'
//       }
//     });
//   }
// });

app.use('*', (req, res) => {
  logger.warn(`Route not found: ${req.method} ${req.originalUrl}`);
  res.status(status.STATUS_CODE_NOT_FOUND).json({
    success: false,
    message: 'Route not found',
    error: 'NOT_FOUND',
    path: req.originalUrl,
  });
});

app.use((error, req, res, next) => {
  logger.error('Global error handler:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
  });

  let processedError = error;

  if (error.name && error.name.startsWith('Sequelize')) {
    processedError = handleSequelizeError(error);
  }

  if (
    error.name &&
    (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError')
  ) {
    processedError = handleJWTError(error);
  }

  const errorResponse = formatErrorResponse(processedError, req);
  const statusCode =
    processedError.statusCode || status.STATUS_CODE_INTERNAL_SERVER_STATUS;
  res.status(statusCode).json(errorResponse);
});

export { app };
