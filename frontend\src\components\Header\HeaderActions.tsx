import React from "react";
// import { Settings } from "lucide-react";
// import { Button } from "@/components/ui/button";
// import { useRouter } from "next/navigation";
import SearchBar from "./SearchBar";
// import NotificationButton from "./NotificationButton";
import UserProfileDropdown from "./UserProfileDropdown";
import { CSS_CLASSES } from "@/Constants/Header";

interface HeaderActionsProps {
  className?: string;
}

const HeaderActions: React.FC<HeaderActionsProps> = ({ className = "" }) => {
  // const router = useRouter();

  // const handleSettings = () => {
  //   router.push(ROUTES.SETTINGS);
  // };

  return (
    <div className={`${CSS_CLASSES.HEADER_ACTIONS_CONTAINER} ${className}`}>
      {/* Search and notification icons */}
      <div className={CSS_CLASSES.SEARCH_NOTIFICATION_CONTAINER}>
        <SearchBar />
        {/* <NotificationButton /> */}
      </div>

      {/* Divider */}
      {/* <div className={CSS_CLASSES.DIVIDER} /> */}

      {/* Settings and profile dropdown */}
      {/* <Button
        variant="ghost"
        size="sm"
        title={ACCESSIBILITY.SETTINGS_BUTTON}
        onClick={handleSettings}
        className={CSS_CLASSES.SETTINGS_BUTTON}
      >
        <Settings className={ICONS.SETTINGS} />
      </Button> */}

      <UserProfileDropdown />
    </div>
  );
};

export default HeaderActions;
