import * as React from "react";

import type { ToastActionElement, ToastProps } from "@/components/ui/toast";
// CONSTANTS
const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 3000;
// TYPES
type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
};

type ToastType =
  | "success"
  | "error"
  | "info"
  | "warning"
  | "default"
  | "custom"
  | "loading"
  | "destructive"
  | "security"
  | "premium"
  | "love"
  | "power"
  | "time";

type Action =
  | {
      type: "ADD_TOAST";
      toast: ToasterToast;
    }
  | {
      type: "UPDATE_TOAST";
      toast: Partial<ToasterToast>;
    }
  | {
      type: "DISMISS_TOAST";
      toastId?: ToasterToast["id"];
    }
  | {
      type: "REMOVE_TOAST";
      toastId?: ToasterToast["id"];
    };

interface State {
  toasts: ToasterToast[];
}

interface ToastConfig {
  variant: ToastProps["variant"];
  defaultTitle: string;
}

interface ToastMessageOptions {
  message: string;
  title?: string;
  type?: ToastType;
  action?: ToastActionElement;
}
// CONFIGURATION
const TOAST_TYPE_CONFIG: Record<ToastType, ToastConfig> = {
  success: { variant: "success", defaultTitle: "Success" },
  error: { variant: "error", defaultTitle: "Error" },
  info: { variant: "info", defaultTitle: "Info" },
  warning: { variant: "warning", defaultTitle: "Warning" },
  default: { variant: "default", defaultTitle: "" },
  custom: { variant: "default", defaultTitle: "" },
  loading: { variant: "loading", defaultTitle: "Loading" },
  destructive: { variant: "destructive", defaultTitle: "Error" },
  security: { variant: "security", defaultTitle: "Security" },
  premium: { variant: "premium", defaultTitle: "Premium" },
  love: { variant: "love", defaultTitle: "Love" },
  power: { variant: "power", defaultTitle: "Power" },
  time: { variant: "time", defaultTitle: "Time" },
} as const;
// UTILITIES
let count = 0;

function generateId(): string {
  count = (count + 1) % Number.MAX_SAFE_INTEGER;
  return count.toString();
}
// STATE MANAGEMENT
const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();
const listeners: Array<(state: State) => void> = [];
let memoryState: State = { toasts: [] };
// TOAST QUEUE MANAGEMENT
const addToRemoveQueue = (toastId: string): void => {
  if (toastTimeouts.has(toastId)) {
    return;
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId);
    dispatch({
      type: "REMOVE_TOAST",
      toastId,
    });
  }, TOAST_REMOVE_DELAY);

  toastTimeouts.set(toastId, timeout);
};

const clearToastTimeout = (toastId: string): void => {
  const timeout = toastTimeouts.get(toastId);
  if (timeout) {
    clearTimeout(timeout);
    toastTimeouts.delete(toastId);
  }
};
// REDUCER
export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST": {
      const newToasts = [action.toast, ...state.toasts].slice(0, TOAST_LIMIT);
      return {
        ...state,
        toasts: newToasts,
      };
    }

    case "UPDATE_TOAST": {
      return {
        ...state,
        toasts: state.toasts.map((toast) =>
          toast.id === action.toast.id ? { ...toast, ...action.toast } : toast
        ),
      };
    }

    case "DISMISS_TOAST": {
      const { toastId } = action;

      // Handle side effects for toast dismissal
      if (toastId) {
        addToRemoveQueue(toastId);
      } else {
        // Dismiss all toasts
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id);
        });
      }

      return {
        ...state,
        toasts: state.toasts.map((toast) =>
          toast.id === toastId || toastId === undefined
            ? { ...toast, open: false }
            : toast
        ),
      };
    }

    case "REMOVE_TOAST": {
      const { toastId } = action;

      if (toastId) {
        clearToastTimeout(toastId);
      } else {
        // Clear all timeouts when removing all toasts
        toastTimeouts.forEach((timeout) => clearTimeout(timeout));
        toastTimeouts.clear();
      }

      if (toastId === undefined) {
        return {
          ...state,
          toasts: [],
        };
      }

      return {
        ...state,
        toasts: state.toasts.filter((toast) => toast.id !== toastId),
      };
    }

    default: {
      return state;
    }
  }
};
// DISPATCH
function dispatch(action: Action): void {
  memoryState = reducer(memoryState, action);
  listeners.forEach((listener) => {
    listener(memoryState);
  });
}
// CORE TOAST FUNCTION
type Toast = Omit<ToasterToast, "id">;

function toast(props: Toast) {
  const id = generateId();

  const update = (updateProps: ToasterToast): void => {
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...updateProps, id },
    });
  };

  const dismiss = (): void => {
    dispatch({ type: "DISMISS_TOAST", toastId: id });
  };

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open: boolean) => {
        if (!open) dismiss();
      },
    },
  });

  return {
    id,
    dismiss,
    update,
  };
}
// HOOK
function useToast() {
  const [state, setState] = React.useState<State>(memoryState);

  React.useEffect(() => {
    listeners.push(setState);
    return () => {
      const index = listeners.indexOf(setState);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }, [state]);

  return {
    ...state,
    toast,
    dismiss: (toastId?: string): void => {
      dispatch({ type: "DISMISS_TOAST", toastId });
    },
  };
}
// CONVENIENCE FUNCTIONS
function toastMessage({
  message,
  title,
  type = "default",
  action,
}: ToastMessageOptions): void {
  const config = TOAST_TYPE_CONFIG[type] || TOAST_TYPE_CONFIG.default;

  toast({
    title: title ?? config.defaultTitle,
    description: message,
    variant: config.variant,
    ...(action && { action }),
  });
}

function toastSuccess(message: string, title?: string): void {
  toastMessage({ message, title, type: "success" });
}

function toastError(message: string, title?: string): void {
  toastMessage({ message, title, type: "error" });
}

function toastInfo(message: string, title?: string): void {
  toastMessage({ message, title, type: "info" });
}

function toastWarning(message: string, title?: string): void {
  toastMessage({ message, title, type: "warning" });
}

function toastLoading(message: string, title?: string): void {
  toastMessage({ message, title, type: "loading" });
}

function toastDestructive(message: string, title?: string): void {
  toastMessage({ message, title, type: "destructive" });
}

function toastSecurity(message: string, title?: string): void {
  toastMessage({ message, title, type: "security" });
}

function toastPremium(message: string, title?: string): void {
  toastMessage({ message, title, type: "premium" });
}

function toastLove(message: string, title?: string): void {
  toastMessage({ message, title, type: "love" });
}

function toastPower(message: string, title?: string): void {
  toastMessage({ message, title, type: "power" });
}

function toastTime(message: string, title?: string): void {
  toastMessage({ message, title, type: "time" });
}
// EXPORTS
export {
  useToast,
  toast,
  toastMessage,
  toastSuccess,
  toastError,
  toastInfo,
  toastWarning,
  toastLoading,
  toastDestructive,
  toastSecurity,
  toastPremium,
  toastLove,
  toastPower,
  toastTime,
};
