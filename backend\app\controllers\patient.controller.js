import * as patientService from '../services/patient.service.js';
import { logPatientAudit } from '../services/patientAuditLog.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { AuditActions, PatientAuditDescriptions } from '../utils/auditlog_messages.utils.js';
import { submitElevenLabsBatchCall } from '../services/elevenlabs.service.js';
import { 
  triggerOutboundCallsForClinic, 
  getCronJobStatus,
  processClinicWithSpecificDays,
  processAllPatientsForClinic,
  callOutboundAPIManually
} from '../services/cron.service.js';
import { Clinic } from '../models/index.js';
import * as reactivationService from '../services/reactivation.service.js';

/**
 * Create a new patient
 * @route POST /v1/patient/create
 */
export const createPatient = async (req, res) => {
  logger.info(loggerMessages.CREATING_PATIENT);
  try {
    const patientData = req.body;
    patientData.email = patientData.email.toLowerCase()
    if (req.user && req.user.id) {
      patientData.created_by = req.user.id;
      patientData.updated_by = req.user.id;
    }
    // Check for existing patient by email or phone number
    const existingPatient = await patientService.findPatientByEmailOrPhone({
      email: patientData.email,
      phone_number: patientData.phone_number,
    });
    if (existingPatient) {
      return res.status(status.STATUS_CODE_CONFLICT).json(
        errorResponse(constants.PATIENT_ALREADY_EXISTS_ERROR)
      );
    }
    // Create the patient
    const newPatient = await patientService.createPatient(patientData);
    await logPatientAudit({
      action: AuditActions.CREATE,
      record_id: newPatient.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newPatient),
      description: PatientAuditDescriptions.PATIENT_CREATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_CREATED_SUCCESSFULLY, newPatient)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_PATIENT);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all patients
 * @route GET /v1/patient/list
 */
export const getAllPatients = async (req, res) => {
  logger.info(loggerMessages.FETCHING_PATIENTS);
  try {
    const filters = {
      is_active: req.query.is_active,
      is_deleted: req.query.is_deleted,
      clinic_id: req.query.clinic_id
    };
    const patients = await patientService.getAllPatients(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENTS_FETCHED_SUCCESSFULLY, patients)
    );
  } catch (error) {
    logger.info(loggerMessages.ERROR_FETCHING_PATIENTS);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Get a single patient by ID
 * @route GET /v1/patient/:id
 */
export const getPatientById = async (req, res) => {
  logger.info(loggerMessages.FETCHING_PATIENT_BY_ID);
  try {
    const patient = await patientService.getPatientById(req.params.id);
    if (!patient) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.PATIENT_NOT_FOUND)
      );
    }
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_FETCHED_SUCCESSFULLY, patient)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_PATIENT_BY_ID);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Update a patient by ID
 * @route PUT /v1/patient/:id
 */
export const updatePatient = async (req, res) => {
  logger.info(loggerMessages.UPDATING_PATIENT);
  try {
    const patientData = req.body;
    const { id } = req.params;
    if (req.user && req.user.id) {
      patientData.updated_by = req.user.id;
      patientData.updated_at = Date.now();
    }
    
    const oldPatient = await patientService.getPatientById(id)
    const updatedPatient = await patientService.updatePatient(
      req.params.id,
      patientData
    );
    if (!updatedPatient) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.PATIENT_NOT_FOUND)
      );
    }
    await logPatientAudit({
      action: AuditActions.UPDATE,
      record_id: updatedPatient.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldPatient),
      new_value: JSON.stringify(updatedPatient),
      description: PatientAuditDescriptions.PATIENT_UPDATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_UPDATED_SUCCESSFULLY, updatedPatient)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_PATIENT);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Soft delete a patient by ID
 * @route DELETE /v1/patient/:id
 */
export const deletePatient = async (req, res) => {
  logger.info(loggerMessages.DELETING_PATIENT);
  try {
    const deletedPatient = await patientService.deletePatient(req.params.id);
    if (!deletedPatient) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.PATIENT_NOT_FOUND)
      );
    }
    await logPatientAudit({
      action: AuditActions.DELETE,
      record_id: deletedPatient.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(deletedPatient),
      description: PatientAuditDescriptions.PATIENT_DELETED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_DELETED_SUCCESSFULLY, deletedPatient)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_PATIENT);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};


/**
 * Upload patient list from CSV or Excel file
 * @route POST /v1/patient/upload
 */
export const uploadPatientList = async (req, res) => {
  logger.info(loggerMessages.UPLOADING_PATIENT_LIST);
  try {
    if (!req.file) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.PATIENT_FILE_REQUIRED)
      );
    }

    // Use service helpers for parsing and normalization
    let patients;
    try {
      patients = patientService.parsePatientFile(req.file);
    } catch (err) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(err.message || constants.PATIENT_FILE_TYPE_INVALID)
      );
    }
    patients = patientService.normalizePatients(patients, req);
    patients = patientService.filterValidPatients(patients);
    if (patients.length === 0) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.NO_VALID_PATIENT_RECORDS)
      );
    }
    // Bulk insert
    const insertedPatients = await patientService.bulkCreatePatients(patients);
    logger.info(loggerMessages.PATIENT_LIST_UPLOADED);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_LIST_UPLOADED_SUCCESSFULLY, insertedPatients)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPLOADING_PATIENT_LIST, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Upload patient list from CSV or Excel file
 * @route POST /v1/patient/upload
 */
export const getPatientByPhone = async (req, res) => {
  logger.info(loggerMessages.GETTING_PATIENT_BY_PHONE);
  try {
    const { phone } = req.query
    const patient = await patientService.findPatientByEmailOrPhone({phone_number:phone});
    if (!patient) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.PATIENT_NOT_FOUND)
      );
    }
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENT_FETCHED_SUCCESSFULLY, patient)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_PATIENT_BY_PHONE, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Outbound call for inactive patients (similar to original API)
 * @route POST /v1/patient/outbound
 */
export const outboundCall = async (req, res) => {
  logger.info(loggerMessages.GETTING_INACTIVE_PATIENTS);
  try {
    const { clinic_id } = req.body;
    
    if (!clinic_id) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.CLINIC_ID_REQUIRED)
      );
    }
    
    // Get clinic to use its reactivation_days setting
    const clinic = await Clinic.findByPk(clinic_id);
    if (!clinic) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.CLINIC_NOT_FOUND_PATIENT_CONTROLLER)
      );
    }

    // Convert clinic batch_call_time to Unix timestamp for current day
    let timeUnix = null;
    if (clinic.batch_call_time) {
      const today = new Date();
      const [hours, minutes, seconds] = clinic.batch_call_time.split(':').map(Number);
      today.setHours(hours, minutes, seconds, 0);
      timeUnix = Math.floor(today.getTime() / 1000);
    }
    
    const days = clinic.reactivation_days; 
    const result = await processClinicWithSpecificDays(clinic_id, days, timeUnix);
    
    // Create reactivation record for tracking
    try {
      const reactivationRecord = await reactivationService.createReactivation({
        clinic_id,
        batch_name: `reactivation-${new Date().toISOString().split('T')[0]}-batch-1`,
        status: 'pending',
        patient_count: result.patient_count || 0,
        scheduled_time_unix: timeUnix,
        scheduled_time: timeUnix ? new Date(timeUnix * 1000) : null,
        reactivation_days: days,
        created_by: req.user?.id || null,
        updated_by: req.user?.id || null,
      });
      

      
      result.reactivation_id = reactivationRecord.id;
    } catch (error) {
      logger.error('Error creating reactivation record:', error);
      // Continue even if tracking fails
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.BATCH_CALL_JOB_SUBMITTED_SUCCESSFULLY, result)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_INACTIVE_PATIENTS, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Manually trigger outbound calls for a specific clinic (for testing)
 * @route POST /v1/patient/trigger-outbound/:clinicId
 */
export const triggerOutboundCalls = async (req, res) => {
  logger.info(loggerMessages.TRIGGERING_MANUAL_OUTBOUND_CALLS);
  try {
    const { clinicId } = req.params;
    
    const result = await triggerOutboundCallsForClinic(parseInt(clinicId));
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.OUTBOUND_CALLS_TRIGGERED_SUCCESSFULLY, result)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_TRIGGERING_OUTBOUND_CALLS_CONTROLLER(error));
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Get cron job status
 * @route GET /v1/patient/cron-status
 */
export const getCronStatus = async (req, res) => {
  logger.info(loggerMessages.GETTING_CRON_JOB_STATUS);
  try {
    const cronStatus = getCronJobStatus();
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CRON_JOB_STATUS_RETRIEVED_SUCCESSFULLY, cronStatus)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_CRON_JOB_STATUS(error));
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Outbound call for all patients in a clinic
 * @route POST /v1/patient/outbound/all
 */
export const allOutboundCall = async (req, res) => {
  logger.info(loggerMessages.GETTING_INACTIVE_PATIENTS);
  try {
    const { clinic_id, time } = req.body;
    
    if (!clinic_id) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse('Clinic ID is required')
      );
    }
    
    const filters = {
      clinic_id: parseInt(clinic_id),
      is_active: true,
      is_deleted: false
    };
    
    const patients = await patientService.getAllPatients(filters);
    
    if (patients.length === 0) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse('No patients found for the specified clinic')
      );
    }

    const reactivationRecord = await submitElevenLabsBatchCall(patients, time, {
      clinic_id: parseInt(clinic_id),
      user_id: req.user?.id || null,
    });

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.BATCH_CALL_JOB_SUBMITTED_SUCCESSFULLY, {
        patients: patients,
        total_patients: patients.length,
        clinic_id: parseInt(clinic_id),
        scheduled_time: time,
        reactivation_id: reactivationRecord?.id || null
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_INACTIVE_PATIENTS, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Manually trigger API call cron job for a specific clinic
 * @route POST /v1/patient/call-api/:clinicId
 */
export const callOutboundAPI = async (req, res) => {
  logger.info(loggerMessages.GETTING_INACTIVE_PATIENTS);
  try {
    const { clinic_id, time } = req.body
    const patients = await patientService.getAllPatients(clinic_id)
    
    const reactivationRecord = await submitElevenLabsBatchCall(patients, time, {
      clinic_id: parseInt(req.params.clinicId),
      user_id: req.user?.id || null,
    });

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.BATCH_CALL_JOB_SUBMITTED_SUCCESSFULLY, {
        patients,
        reactivation_id: reactivationRecord?.id || null
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_INACTIVE_PATIENTS, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Get patients by clinic ID
 * @route GET /v1/patient/clinic/:clinicId
 */
export const getPatientsByClinic = async (req, res) => {
  logger.info(loggerMessages.FETCHING_PATIENTS_BY_CLINIC);
  try {
    const { clinicId } = req.params;
    const filters = {
      clinic_id: parseInt(clinicId),
      is_active: true,
      is_deleted: false
    };
    
    const patients = await patientService.getAllPatients(filters);
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.PATIENTS_FETCHED_SUCCESSFULLY, patients)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_PATIENTS_BY_CLINIC, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Submit batch call with selected patients
 * @route POST /v1/patient/batch-call
 */
export const submitBatchCall = async (req, res) => {
  logger.info(loggerMessages.SUBMITTING_BATCH_CALL);
  try {
    const { clinic_id, patient_ids, time } = req.body;
    
    if (!patient_ids || !Array.isArray(patient_ids) || patient_ids.length === 0) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse('Patient IDs array is required and must not be empty')
      );
    }
    
    // Get selected patients with user details
    const selectedPatients = await patientService.getPatientsByIds(patient_ids);
    
    if (selectedPatients.length === 0) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse('No patients found with the provided IDs')
      );
    }
    
    // Submit batch call
    const reactivationRecord = await submitElevenLabsBatchCall(selectedPatients, time, {
      clinic_id,
      user_id: req.user?.id || null,
    });
    
    // Return patients with user details
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.BATCH_CALL_JOB_SUBMITTED_SUCCESSFULLY, {
        patients: selectedPatients,
        total_selected: selectedPatients.length,
        clinic_id,
        scheduled_time: time,
        reactivation_id: reactivationRecord?.id || null
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_SUBMITTING_BATCH_CALL, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * Get reactivation records for a clinic
 * @route GET /v1/patient/reactivations/:clinicId
 */
export const getReactivationsByClinic = async (req, res) => {
  logger.info(loggerMessages.FETCHING_REACTIVATIONS_BY_CLINIC);
  try {
    const { clinicId } = req.params;
    
    if (!clinicId || isNaN(parseInt(clinicId))) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse('Invalid clinic ID provided')
      );
    }
    
    const reactivations = await reactivationService.getReactivationsByClinic(parseInt(clinicId));
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.REACTIVATIONS_FETCHED_SUCCESSFULLY, reactivations)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_REACTIVATIONS_BY_CLINIC, error);
    
    // Provide more specific error messages
    let errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    let statusCode = status.STATUS_CODE_INTERNAL_SERVER_STATUS;
    
    if (error.message && error.message.includes('relation') && error.message.includes('does not exist')) {
      errorMessage = 'Reactivation table does not exist. Please run database migrations first.';
      statusCode = status.STATUS_CODE_SERVICE_UNAVAILABLE;
    } else if (error.message && error.message.includes('column') && error.message.includes('does not exist')) {
      errorMessage = 'Database schema mismatch. Please run database migrations first.';
      statusCode = status.STATUS_CODE_SERVICE_UNAVAILABLE;
    }
    
    return res.status(statusCode).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get reactivation statistics for a clinic
 * @route GET /v1/patient/reactivations/:clinicId/stats
 */
export const getReactivationStats = async (req, res) => {
  logger.info(loggerMessages.FETCHING_REACTIVATION_STATS);
  try {
    const { clinicId } = req.params;
    const { start_date, end_date } = req.query;
    
    const dateRange = {};
    if (start_date) dateRange.startDate = new Date(start_date);
    if (end_date) dateRange.endDate = new Date(end_date);
    
    const stats = await reactivationService.getReactivationStats(parseInt(clinicId), dateRange);
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.REACTIVATION_STATS_FETCHED_SUCCESSFULLY, stats)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_REACTIVATION_STATS, error);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message || constants.INTERNAL_SERVER_ERROR)
    );
  }
};