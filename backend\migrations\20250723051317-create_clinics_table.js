import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('clinics', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    clinic_name: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    clinic_email: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    clinic_phonenumber: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    location: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    timezone: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    working_hours: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    working_days: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('clinics');
}; 