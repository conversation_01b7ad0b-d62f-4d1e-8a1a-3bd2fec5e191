
.changeContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0fdf4 100%);
  padding: 0 1rem;
}

.changeMotion {
  width: 100%;
  max-width: 480px;
}

.changeCard {
  border-radius: 1.5rem;
  box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 1.5px 8px rgba(34,197,94,0.07);
  background: rgba(255,255,255,0.95);
  border: 1px solid #e5e7eb;
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
}

.changeHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: center;
  margin-top: 0.5rem;
}

.changeTitle {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(90deg, #1e40af, #22c55e, #14b8a6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: -0.02em;
}

.changeSubtitle {
  color: var(--color-muted-foreground, #6b7280);
  margin-top: 0.25rem;
  font-size: 1rem;
}

.changeForm {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
  padding: 0 0.5rem;
}

.changeInput {
  padding-left: 2.5rem;
  min-height: 44px;
  font-size: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  transition: border-color 0.2s;
  width: 100%;
  box-sizing: border-box;
}
.changeInput:focus {
  border-color: #2563eb;
  background: #fff;
}

.changeError {
  font-size: 0.92rem;
  color: #ef4444;
  margin-top: 0.3rem;
  display: block;
  padding-left: 0.2rem;
}

.changeButton {
  width: 100%;
  color: #fff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 0;
  margin-top: 0.5rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}
.changeButton:focus,
.changeButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}
.changeButton:disabled {
  background: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
}

.changeSuccess {
  color: #22c55e;
  font-weight: 500;
  margin-bottom: 12px;
  display: block;
  text-align: center;
}

.changeIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-muted-foreground, #6b7280);
}

.changeIconLeft {
  left: 0.75rem;
  pointer-events: none;
}

.changeIconRight {
  right: 0.75rem;
  pointer-events: auto;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.changePasswordToggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-muted-foreground, #6b7280);
  outline: none;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.changeLogo {
  margin: 0 auto 0.5rem auto;
}

.changeBackToLogin {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-muted-foreground, #6b7280);
}

.changeBackToLoginLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: underline;
  transition: color 0.15s;
}

.changeLabelRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 0.2rem;
  position: relative;
}

.passwordStrength {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.passwordStrengthTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.passwordChecks {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.passwordCheck {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #6b7280;
  transition: color 0.2s;
}

.passwordCheck.valid {
  color: #059669;
}

.passwordCheck.invalid {
  color: #dc2626;
}

.checkIcon {
  font-weight: bold;
  font-size: 0.875rem;
  min-width: 1rem;
  text-align: center;
}

.passwordCheck.valid .checkIcon {
  color: #059669;
}

.passwordCheck.invalid .checkIcon {
  color: #dc2626;
}

