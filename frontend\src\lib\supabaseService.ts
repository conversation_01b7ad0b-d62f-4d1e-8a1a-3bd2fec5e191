import { supabase } from './utils';

// Types for our data
export interface Patient {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  dob?: string;
  address?: string;
  preferred_slot?: string;
  created_at?: string;
}

export interface Doctor {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  specialization?: string;
  clinic_id?: number;
  created_at?: string;
}

export interface Clinic {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  contact_number?: string;
  working_hours?: string;
  email?: string;
  created_at?: string;
}

export interface AppointmentSchedule {
  id: number;
  patient_id: number;
  doctor_id: number;
  clinic_id?: number;
  date: string;
  time_slot: string;
  status?: string;
  booked_via?: string;
  notes?: string;
  created_at?: string;

   // Add the expanded relationships
   patient?: Patient;
   doctor?: Doctor;
   clinic?: Clinic;
}

// Data fetching functions
export const supabaseService = {
  // Patients
  async getPatients() {
    const { data, error } = await supabase
      .from('patient')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as Patient[];
  },

  async getPatientById(id: number) {
    const { data, error } = await supabase
      .from('patient')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data as Patient;
  },

  // Doctors
  async getDoctors() {
    const { data, error } = await supabase
      .from('doctor')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as Doctor[];
  },

  async getDoctorById(id: number) {
    const { data, error } = await supabase
      .from('doctor')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data as Doctor;
  },

  // Clinics
  async getClinics() {
    const { data, error } = await supabase
      .from('clinic')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as Clinic[];
  },

  async getClinicById(id: number) {
    const { data, error } = await supabase
      .from('clinic')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data as Clinic;
  },

  // Appointments
  async getAppointments() {
    const { data, error } = await supabase
      .from('appointment_schedule')
      .select(`
        *,
        patient:patient_id(*),
        doctor:doctor_id(*),
        clinic:clinic_id(*)
      `)
      .order('date', { ascending: true });
    
    if (error) throw error;
    return data as AppointmentSchedule[];
  },

  async getAppointmentsByDate(date: string) {
    const { data, error } = await supabase
      .from('appointment_schedule')
      .select(`
        *,
        patient:patient_id(*),
        doctor:doctor_id(*),
        clinic:clinic_id(*)
      `)
      .eq('date', date)
      .order('time_slot', { ascending: true });
    
    if (error) throw error;
    return data as AppointmentSchedule[];
  },

  // Dashboard stats
  async getDashboardStats() {
    const [patients, doctors, clinics, appointments] = await Promise.all([
      supabase.from('patient').select('id', { count: 'exact' }),
      supabase.from('doctor').select('id', { count: 'exact' }),
      supabase.from('clinic').select('id', { count: 'exact' }),
      supabase.from('appointment_schedule').select('id', { count: 'exact' })
    ]);

    return {
      totalPatients: patients.count || 0,
      totalDoctors: doctors.count || 0,
      totalClinics: clinics.count || 0,
      totalAppointments: appointments.count || 0
    };
  }
}; 