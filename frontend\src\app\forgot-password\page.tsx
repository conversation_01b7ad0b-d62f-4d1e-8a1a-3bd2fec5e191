"use client";
import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Mail } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import styles from "./ForgotPassword.module.css";
import { FORGOT_PASSWORD_CONSTANTS } from "@/Constants/ForgotPassword";
import { ERROR_MESSAGES } from "@/Constants/HooksAPI";
import { useAuth } from "@/hooks/useAuth";

const schema = z.object({
  email: z.string().email({ message: FORGOT_PASSWORD_CONSTANTS.EMAIL_ERROR }),
});

type FormData = z.infer<typeof schema>;

export default function ForgotPasswordPage() {
  const [success, setSuccess] = useState(false);
  const [localError, setLocalError] = useState("");
  const { forgotPassword, loading, error: hookError } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({ resolver: zodResolver(schema) });

  const onSubmit = async (data: FormData) => {
    setLocalError("");
    setSuccess(false);
    const emailLower = data.email.toLowerCase();
    const result = await forgotPassword(emailLower);
    if (result.success) {
      setSuccess(true);
    } else {
      setLocalError(result.error || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
    }
  };

  return (
    <div className={styles.forgotContainer}>
      <motion.div
        initial={{ opacity: 0, scale: 0.96 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className={styles.forgotMotion}
        style={{ maxWidth: "480px", width: "100%" }}
      >
        <Card
          className={styles.forgotCard}
          style={{ maxWidth: "480px", width: "100%" }}
        >
          <CardHeader className={styles.forgotHeader}>
            <Image
              src="/Densy%20AI.svg"
              alt="Densy AI Logo"
              width={150}
              height={40}
              className={styles.forgotLogo}
              priority
            />
            <h1 className={styles.forgotTitle}>
              {FORGOT_PASSWORD_CONSTANTS.TITLE}
            </h1>
            <p className={styles.forgotSubtitle}>
              {FORGOT_PASSWORD_CONSTANTS.SUBTITLE}
            </p>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className={styles.forgotForm}
              autoComplete="off"
            >
              <div>
                <Label htmlFor="email">
                  {FORGOT_PASSWORD_CONSTANTS.EMAIL_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.forgotIcon}>
                    <Mail size={18} />
                  </span>
                  <Input
                    id="email"
                    type="email"
                    aria-label="Email address"
                    placeholder={FORGOT_PASSWORD_CONSTANTS.EMAIL_PLACEHOLDER}
                    autoComplete="email"
                    {...register("email")}
                    className={styles.forgotInput}
                    required
                  />
                </div>
                {errors.email && (
                  <span className={styles.forgotError}>
                    {errors.email.message}
                  </span>
                )}
              </div>
              {(localError || hookError) && (
                <span className={styles.forgotError}>
                  {localError || hookError}
                </span>
              )}
              {success && (
                <span className={styles.forgotSuccess}>
                  {FORGOT_PASSWORD_CONSTANTS.SUCCESS}
                </span>
              )}
              <Button
                type="submit"
                className={success ? styles.forgotButtonSuccess : styles.forgotButton}
                disabled={loading || success}
                aria-label="Send reset link"
              >
                {loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Mail className="animate-spin" size={18} /> Sending...
                  </span>
                ) : success ? (
                  "Sent!"
                ) : (
                  FORGOT_PASSWORD_CONSTANTS.BUTTON
                )}
              </Button>
            </form>
            <div className={styles.forgotLogin}>
              <span>
                <Link href="/login" className={styles.forgotLoginLink}>
                  &larr; Back to Login
                </Link>
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
