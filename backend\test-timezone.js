/**
 * Test script for timezone conversion functionality
 * Run with: node test-timezone.js
 */

import { 
  convertIndianTimeToUTC, 
  convertUTCToIndianTime, 
  formatIndianTime, 
  getCurrentIndianTime, 
  getCurrentUTCTime,
  getTimezoneInfo 
} from './app/utils/timezone.util.js';

function testTimezoneConversions() {
  console.log('🧪 Testing Timezone Conversion Functionality...\n');

  try {
    // Test 1: Get timezone information
    console.log('1. Timezone Information:');
    const timezoneInfo = getTimezoneInfo();
    console.log(`   IST Offset: ${timezoneInfo.IST_OFFSET_HOURS} hours (${timezoneInfo.IST_OFFSET_MINUTES} minutes)`);
    console.log(`   Timezone: ${timezoneInfo.TIMEZONE_NAME}`);
    console.log(`   Description: ${timezoneInfo.DESCRIPTION}\n`);

    // Test 2: Current times
    console.log('2. Current Times:');
    const currentUTC = getCurrentUTCTime();
    const currentIndian = getCurrentIndianTime();
    console.log(`   Current UTC: ${currentUTC.toISOString()}`);
    console.log(`   Current Indian: ${currentIndian.toISOString()}`);
    console.log(`   Difference: ${(currentIndian.getTime() - currentUTC.getTime()) / (1000 * 60 * 60)} hours\n`);

    // Test 3: Indian time to UTC conversion
    console.log('3. Indian Time to UTC Conversion:');
    const testIndianTime = '2024-01-15T14:30:00'; // 2:30 PM Indian time
    const convertedToUTC = convertIndianTimeToUTC(testIndianTime);
    console.log(`   Indian Time: ${testIndianTime}`);
    console.log(`   Converted to UTC: ${convertedToUTC.toISOString()}`);
    console.log(`   Expected UTC: 2024-01-15T09:00:00.000Z (2:30 PM IST = 9:00 AM UTC)\n`);

    // Test 4: UTC to Indian time conversion
    console.log('4. UTC to Indian Time Conversion:');
    const testUTCTime = '2024-01-15T09:00:00.000Z'; // 9:00 AM UTC
    const convertedToIndian = convertUTCToIndianTime(testUTCTime);
    console.log(`   UTC Time: ${testUTCTime}`);
    console.log(`   Converted to Indian: ${convertedToIndian.toISOString()}`);
    console.log(`   Expected Indian: 2024-01-15T14:30:00.000Z (9:00 AM UTC = 2:30 PM IST)\n`);

    // Test 5: Format Indian time for display
    console.log('5. Format Indian Time for Display:');
    const appointmentTime = '2024-01-15T09:00:00.000Z'; // UTC from database
    const formattedIndianTime = formatIndianTime(appointmentTime);
    console.log(`   UTC from DB: ${appointmentTime}`);
    console.log(`   Formatted Indian: ${formattedIndianTime}\n`);

    // Test 6: Round-trip conversion
    console.log('6. Round-trip Conversion Test:');
    const originalIndianTime = '2024-01-15T14:30:00';
    const toUTC = convertIndianTimeToUTC(originalIndianTime);
    const backToIndian = convertUTCToIndianTime(toUTC);
    console.log(`   Original Indian: ${originalIndianTime}`);
    console.log(`   To UTC: ${toUTC.toISOString()}`);
    console.log(`   Back to Indian: ${backToIndian.toISOString()}`);
    console.log(`   Round-trip successful: ${originalIndianTime === backToIndian.toISOString().slice(0, 19)}\n`);

    // Test 7: Edge cases
    console.log('7. Edge Cases:');
    
    // Test with different date formats
    const differentFormats = [
      '2024-01-15T14:30:00',
      '2024-01-15 14:30:00',
      '2024-01-15T14:30:00.000Z',
      new Date('2024-01-15T14:30:00')
    ];

    differentFormats.forEach((format, index) => {
      try {
        const converted = convertIndianTimeToUTC(format);
        console.log(`   Format ${index + 1}: ${format} → UTC: ${converted.toISOString()}`);
      } catch (error) {
        console.log(`   Format ${index + 1}: ${format} → Error: ${error.message}`);
      }
    });

    console.log('\n🎉 All timezone conversion tests completed successfully!');
    console.log('\n📝 Key Points:');
    console.log('   - Indian time is UTC+5:30 (5.5 hours ahead)');
    console.log('   - When saving: Convert Indian time to UTC before database insert');
    console.log('   - When displaying: Convert UTC from database to Indian time');
    console.log('   - All conversions are handled automatically in the services');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    process.exit(1);
  }
}

// Run the test
testTimezoneConversions();
