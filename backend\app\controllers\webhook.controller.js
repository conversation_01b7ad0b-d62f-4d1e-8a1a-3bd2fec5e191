/**
 * Webhook Controller: Handles HTTP requests for webhook operations.
 * Uses webhookService and returns consistent API responses.
 * <AUTHOR>
 */
import * as webhookService from '../services/webhook.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as status from '../utils/status_code.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';
import dotenv from 'dotenv';

dotenv.config();

const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

/**
 * Handle ElevenLabs webhook
 * @route POST /v1/webhook/elevenlabs
 * <AUTHOR>
 */
export const handleElevenLabsWebhook = async (req, res) => {
  const requestId = Date.now().toString();
  logger.info(`[${requestId}] ${constants.WEBHOOK_PROCESSING_WEBHOOK} - ${new Date().toISOString()}`);
  
  try {
    // Get raw body for signature verification
    let rawBody;
    
    // Check if body is already parsed (Express middleware might have parsed it)
    if (typeof req.body === 'string') {
      rawBody = req.body;
    } else if (req.body && typeof req.body === 'object') {
      // Body was already parsed by middleware, we need to get the raw body
      // For signature verification, we'll use the parsed body as string
      rawBody = JSON.stringify(req.body);
    } else {
      rawBody = req.body?.toString() || '';
    }
    
    // Log essential request info
    logger.info(`[${requestId}] ${constants.WEBHOOK_RECEIVED.replace('{length}', rawBody.length)}`);
    
    const headers = req.headers;
    logger.info(`[${requestId}] ${constants.WEBHOOK_HEADERS}`, {
      'content-type': headers['content-type'],
      'user-agent': headers['user-agent'],
      'content-length': headers['content-length']
    });

    // Verify webhook signature
    const signature = headers['x-elevenlabs-signature'] || headers['x-webhook-signature'];
    
    if (WEBHOOK_SECRET && signature) {
      logger.info(`[${requestId}] ${constants.WEBHOOK_ATTEMPTING_SIGNATURE_VERIFICATION}`);
      const isValidSignature = webhookService.verifyWebhookSignature(
        rawBody,
        signature,
        WEBHOOK_SECRET
      );

      if (!isValidSignature) {
        logger.warn(`[${requestId}] ${constants.WEBHOOK_SIGNATURE_VERIFICATION_FAILED_LOG}`);
        return res.status(status.STATUS_CODE_UNAUTHORIZED).json(
          errorResponse(constants.WEBHOOK_SIGNATURE_VERIFICATION_FAILED)
        );
      }
      logger.info(`[${requestId}] ${constants.WEBHOOK_SIGNATURE_VERIFICATION_SUCCESSFUL}`);
    } else if (WEBHOOK_SECRET && !signature) {
      logger.warn(`[${requestId}] ${constants.WEBHOOK_SECRET_CONFIGURED_NO_SIGNATURE}`);
    } else {
      logger.warn(`[${requestId}] ${constants.WEBHOOK_SECRET_NOT_CONFIGURED}`);
    }

    // Parse JSON after verifying signature
    let parsedBody;
    try {
      if (typeof req.body === 'object' && req.body !== null) {
        // Body is already parsed
        parsedBody = req.body;
        logger.info(`[${requestId}] ${constants.WEBHOOK_BODY_ALREADY_PARSED}`);
      } else {
        // Parse raw body
        parsedBody = JSON.parse(rawBody);
        logger.info(`[${requestId}] ${constants.WEBHOOK_JSON_PARSED_SUCCESSFULLY}`);
      }
    } catch (parseError) {
      logger.error(`[${requestId}] ${constants.WEBHOOK_JSON_PARSING_FAILED.replace('{error}', parseError.message)}`);
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.WEBHOOK_INVALID_JSON_PAYLOAD)
      );
    }

    // Log the parsed structure
    logger.info(`[${requestId}] ${constants.WEBHOOK_PAYLOAD_STRUCTURE}`, {
      hasData: !!parsedBody.data,
      conversationId: parsedBody.data?.conversation_id || constants.WEBHOOK_MISSING_CONVERSATION_ID,
      topLevelKeys: Object.keys(parsedBody)
    });

    // Validate payload structure
    if (!webhookService.validateWebhookPayload(parsedBody)) {
      logger.warn(`[${requestId}] ${constants.WEBHOOK_PAYLOAD_VALIDATION_FAILED}`);
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.WEBHOOK_INVALID_PAYLOAD)
      );
    }

    logger.info(`[${requestId}] ${constants.WEBHOOK_PAYLOAD_VALIDATION_SUCCESSFUL}`);

    // Process webhook and fetch transcript
    logger.info(`[${requestId}] ${constants.WEBHOOK_STARTING_TRANSCRIPT_FETCH.replace('{id}', parsedBody.data.conversation_id)}`);
    const result = await webhookService.processElevenLabsWebhook(
      parsedBody,
      ELEVENLABS_API_KEY
    );

    // Log comprehensive transcript result
    logger.info(`[${requestId}] ${constants.WEBHOOK_TRANSCRIPT_FETCH_COMPLETED}`, {
      success: result.success,
      conversationId: result.conversationId,
      transcriptLength: result.transcript ? result.transcript.length : 0,
      message: result.message,
      metadata: result.metadata
    });

    // Log detailed transcript content
    if (result.transcript && result.transcript.length > 0) {
      logger.info(`[${requestId}] ${constants.WEBHOOK_TRANSCRIPT_CONTENT}`, {
        totalEntries: result.transcript.length,
        entries: result.transcript.map((entry, index) => ({
          index: index + 1,
          role: entry.role,
          messageLength: entry.message ? entry.message.length : 0,
          messagePreview: entry.message ? 
            entry.message.substring(0, 100) + (entry.message.length > 100 ? '...' : '') : constants.WEBHOOK_NO_MESSAGE
        }))
      });
    } else {
      logger.warn(`[${requestId}] ${constants.WEBHOOK_TRANSCRIPT_EMPTY_WARNING}`);
    }

    logger.info(`[${requestId}] ${logMessages.WEBHOOK_PROCESSED_SUCCESSFULLY}`);

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.WEBHOOK_PROCESSED_SUCCESSFULLY, result)
    );

  } catch (error) {
    logger.error(`[${requestId}] ${logMessages.ERROR_PROCESSING_WEBHOOK}: ${error.message}`);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Health check endpoint for webhook
 * @route GET /v1/webhook/health
 * <AUTHOR>
 */
export const webhookHealthCheck = async (req, res) => {
  try {
    logger.info(constants.WEBHOOK_HEALTH_CHECK_REQUESTED);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.WEBHOOK_SERVICE_HEALTHY, { 
        status: constants.WEBHOOK_STATUS_OK,
        timestamp: new Date().toISOString(),
        environment: {
          hasWebhookSecret: !!WEBHOOK_SECRET,
          hasElevenLabsKey: !!ELEVENLABS_API_KEY
        }
      })
    );
  } catch (error) {
    logger.error(constants.WEBHOOK_HEALTH_CHECK_ERROR.replace('{error}', error.message));
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(constants.INTERNAL_SERVER_ERROR)
    );
  }
};
