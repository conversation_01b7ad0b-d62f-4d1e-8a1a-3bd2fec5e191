// AddTemplateForm.tsx
// Renders a modal form for adding a new template using CommonForm and template field config.

import React from "react";
import CommonForm from "@/components/CommonComponents/CommonForm";
import { formConfigs } from "@/components/CommonComponents/formConfigs";

/**
 * Props for the AddTemplateForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (template data)
 */
interface AddTemplateFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (template: Record<string, unknown>) => void;
  initialData?: Record<string, string>;
  submitButtonText?: string;
  title?: string;
}

/**
 * Renders a modal form for adding a new template.
 * Uses CommonForm with template field configuration.
 */
const AddTemplateForm: React.FC<AddTemplateFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData = {},
  submitButtonText,
  title,
}) => {
  return (
    <CommonForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      formType="template"
      fields={formConfigs.template}
      initialData={initialData}
      submitButtonText={submitButtonText}
      title={title}
    />
  );
};

export default AddTemplateForm; 