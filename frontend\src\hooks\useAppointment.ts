import { useState, useCallback } from "react";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { apiRequest } from "@/utils/axios.utils";
import * as AppointmentConstants from "@/Constants/Appointment";

export interface Clinic {
  id: number;
  clinic_name: string;
}

export interface Doctor {
  id: number;
  doctor_name: string;
  specialization: string;
  doctor_email: string;
}

export interface Patient {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
}

export interface Appointment {
  id: number;
  clinic_id: number;
  patient_id: number | null;
  doctor_id: number;
  appointment_date: string;
  appointment_time: string;
  status: string;
  source: string;
  created_at: string;
  updated_at: string;
  created_by: number | null;
  updated_by: number | null;
  is_deleted: boolean;
  is_active: boolean;
  clinic: Clinic;
  patient: Patient | null;
  doctor: Doctor;
}

export interface AppointmentResponse {
  status: boolean;
  message: string;
  data: Appointment[];
}

export function useAppointment() {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = "/appointment";

  const getAllAppointments = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(API_BASE)) as AppointmentResponse;
      if (response && response.status) {
        setAppointments(response.data);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const getAppointmentById = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`${API_BASE}/${id}`)) as { status: boolean; message?: string; data?: Appointment };
      if (response && response.status) {
        setAppointment(response.data as Appointment);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const createAppointment = useCallback(async (data: Omit<Appointment, "id" | "created_at" | "updated_at" | "clinic" | "patient" | "doctor">) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.post(API_BASE, data)) as { status: boolean; message?: string; data?: Appointment };
      if (response && response.status) {
        setAppointments((prev) => [...prev, response.data as Appointment]);
        compactSuccessMessage(response.message || AppointmentConstants.MESSAGES.APPOINTMENT_CREATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_CREATE_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_CREATE_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAppointment = useCallback(
    async (data: Partial<Omit<Appointment, "id" | "created_at" | "updated_at" | "clinic" | "patient" | "doctor">>, id: string) => {
      setLoading(true);
      setError(null);
      try {
        const response = (await apiRequest.put(`${API_BASE}/${id}`, data)) as { status: boolean; message?: string; data?: Appointment };
        if (response && response.status) {
          setAppointments((prev) =>
            prev.map((a) => (a.id === parseInt(id) ? response.data as Appointment : a))
          );
          compactSuccessMessage(response.message || AppointmentConstants.MESSAGES.APPOINTMENT_UPDATE_SUCCESS);
          return { success: true, data: response.data };
        } else {
          compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_UPDATE_FAILED);
          return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_UPDATE_FAILED };
        }
      } catch (error: unknown) {
        const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_UPDATE_FAILED);
        compactErrorMessage(errorMsg);
        return { success: false, error: errorMsg };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const deleteAppointment = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.delete(`${API_BASE}/${id}`)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setAppointments((prev) => prev.filter((a) => a.id !== parseInt(id)));
        compactSuccessMessage(response.message || AppointmentConstants.MESSAGES.APPOINTMENT_DELETE_SUCCESS);
        return { success: true };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_DELETE_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_DELETE_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const getAppointmentsByDate = useCallback(async (date: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`${API_BASE}?date=${date}`)) as AppointmentResponse;
      if (response && response.status) {
        setAppointments(response.data);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const getAppointmentsByStatus = useCallback(async (status: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`${API_BASE}?status=${status}`)) as AppointmentResponse;
      if (response && response.status) {
        setAppointments(response.data);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
        return { success: false, error: response?.message || AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AppointmentConstants.MESSAGES.APPOINTMENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    appointments,
    appointment,
    loading,
    error,
    setLoading,
    setError,
    getAllAppointments,
    getAppointmentById,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    getAppointmentsByDate,
    getAppointmentsByStatus,
  };
} 