// DashboardRecentAIActivity.tsx
// Renders a card showing doctor details and patient counts dynamically from the useDoctor hook.
// Displays doctor information with their patient counts in a clean layout.

import React, { useEffect, useState } from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Users, Stethoscope, User } from "lucide-react";
import { DASHBOARD_RECENT_AI_ACTIVITY_TITLE } from "@/Constants/Dashboard";
import { useDoctor } from "@/hooks/useDoctor";
import { useAppointment } from "@/hooks/useAppointment";
import { usePatients } from "@/hooks/usePatients";

interface Doctor {
  id: number;
  doctor_name: string;
  specialization?: string;
  qualification?: string;
  clinic_id: number;
  doctor_phonenumber?: string;
  doctor_email?: string;
  experience_years?: number;
  is_deleted?: boolean;
  created_at?: string | number;
  [key: string]: unknown;
}

/**
 * Renders a card with doctor details and patient counts for the dashboard.
 */
const DashboardRecentAIActivity: React.FC = () => {
  const { getDoctors } = useDoctor();
  const { appointments } = useAppointment();
  const { patients, getAllPatients } = usePatients();
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const [doctorsResult] = await Promise.all([
        getDoctors(),
        getAllPatients(), // Fetch patients as well
      ]);
      
      if (doctorsResult.success && Array.isArray(doctorsResult.data)) {
        // Filter out deleted doctors
        const activeDoctors = doctorsResult.data.filter((doctor: Doctor) => !doctor.is_deleted);
        setDoctors(activeDoctors);
      }
      setLoading(false);
    };

    fetchData();
  }, [getDoctors, getAllPatients]);

  // Calculate patient count for each doctor from appointments
  const getAppointmentPatientCount = (doctorId: number) => {
    return appointments.filter(appointment => appointment.doctor_id === doctorId).length;
  };

  // Calculate patient count for each doctor from patients data
  const getPatientCount = (doctorId: number) => {
    return patients.filter(patient => 
      patient.doctors && patient.doctors.includes(doctorId)
    ).length;
  };

  // Get top 5 doctors by combined patient count
  const topDoctors = doctors
    .map(doctor => {
      const appointmentPatients = getAppointmentPatientCount(doctor.id);
      const assignedPatients = getPatientCount(doctor.id);
      const totalPatients = appointmentPatients + assignedPatients;
      
      return {
        ...doctor,
        totalPatients
      };
    })
    .sort((a, b) => b.totalPatients - a.totalPatients)
    .slice(0, 5);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2 text-green-600" />
            {DASHBOARD_RECENT_AI_ACTIVITY_TITLE}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, idx) => (
              <div key={idx} className="animate-pulse">
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="w-12 h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="h-5 w-5 mr-2 text-green-600" />
          {DASHBOARD_RECENT_AI_ACTIVITY_TITLE}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {topDoctors.length > 0 ? (
            topDoctors.map((doctor) => (
              <div
                key={doctor.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                {/* Left: Doctor avatar and info */}
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                    <Stethoscope className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {doctor.doctor_name || "Unknown Doctor"}
                    </p>
                    <div className="flex items-center space-x-3 text-sm text-gray-600">
                      {/* Specialization */}
                      <span className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {doctor.specialization || "General"}
                      </span>
                      {/* Experience */}
                      {doctor.experience_years && (
                        <span>{doctor.experience_years} years exp.</span>
                      )}
                      {/* Email */}
                      {doctor.doctor_email && (
                        <span className="text-xs">{doctor.doctor_email}</span>
                      )}
                    </div>
                  </div>
                </div>
                {/* Right: Total Patient count */}
                <div className="flex items-center">
                  <div className="text-right">
                    <p className="text-lg font-semibold text-green-600">
                      {doctor.totalPatients}
                    </p>
                    <p className="text-xs text-gray-500">Total Patients</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>No doctors available</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardRecentAIActivity;
