/**
 * Clinic Routes: Express router for clinic CRUD endpoints.
 * Applies validation and authentication middleware.
 */
import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createClinicSchema, updateClinicSchema, updateClinicReactivitationSchema } from '../validators/clinic.validator.js';
import * as clinicController from '../controllers/clinic.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new clinic
// POST /v1/clinic/create
router.post(
  '/create',
  verifyToken,
  validate(createClinicSchema),
  clinicController.createClinic
);

// Get all clinics
// GET /v1/clinic/list
router.get(
  '/list',
  verifyToken,
  clinicController.getAllClinics
);

// Get clinic by ID
// GET /v1/clinic/:id
router.get(
  '/:id',
  verifyToken,
  clinicController.getClinicById
);

// Update clinic by ID
// PUT /v1/clinic/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateClinicSchema),
  clinicController.updateClinic
);

// Update clinic by ID
// PUT /v1/clinic/:id
router.put(
  '/reactivation-days/:id',
  verifyToken,
  validate(updateClinicReactivitationSchema),
  clinicController.updateReactivationDaysClinic
);

// Soft delete clinic by ID
// DELETE /v1/clinic/:id
router.delete(
  '/:id',
  verifyToken,
  clinicController.deleteClinic
);

export default router; 