"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Loader2, X } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import AddDoctorForm from "@/components/forms/AddDoctorForm";
import SearchBar from "@/components/CommonComponents/SearchBar";
import DoctorDirectoryTable from "@/components/DoctorDirectory/DoctorDirectoryTable";
import DetailedDoctorCard from "@/components/DoctorDirectory/DetailedDoctorCard";
import DoctorStatsGrid from "@/components/DoctorDirectory/DoctorStatsGrid";
import PageSection from "@/components/CommonComponents/PageSection";
import {
  DOCTOR_DIRECTORY_TITLE,
  DOCTOR_DIRECTORY_SUBTITLE,
  DOCTOR_DIRECTORY_ADD_NEW,
  DOCTOR_DIRECTORY_SEARCH_PLACEHOLDER,
  DOCTOR_DIRECTORY_FILTER_ALL,
  DOCTOR_DIRECTORY_LOADING,
  DOCTOR_DIRECTORY_RETRY,
} from "@/Constants/DoctorDirectory";
import { useDoctor } from "@/hooks/useDoctor";
import { useClinic } from "@/hooks/useClinic";
import { formatPhoneNumber } from "@/utils/commonFunctions";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";

interface Doctor {
  id: number;
  doctor_name: string;
  specialization?: string;
  qualification?: string;
  clinic_id: number;
  doctor_phonenumber?: string;
  doctor_email?: string;
  experience_years?: number;
  is_deleted?: boolean;
  created_at?: string | number; // Added created_at
  consultationHours?: string; // Added consultationHours
  working_days?: string; // Added working_days
  patients?: number; // Added patients
  [key: string]: unknown;
}

interface Clinic {
  id: number;
  clinic_name: string;
}

const DoctorDirectory = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterSpecialty, setFilterSpecialty] = useState("all");
  const [isAddDoctorOpen, setIsAddDoctorOpen] = useState(false);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getDoctors, createDoctor, updateDoctor, deleteDoctor } = useDoctor();
  const { getClinics } = useClinic();
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [isEditDoctorOpen, setIsEditDoctorOpen] = useState(false);
  const [editDoctorInitialData, setEditDoctorInitialData] = useState<Record<string, unknown> | undefined>(undefined);

  // Fetch doctors and clinics from backend API
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      const [doctorsResult, clinicsResult] = await Promise.all([
        getDoctors(),
        getClinics(),
      ]);
      if (doctorsResult.success) {
        const doctorsData = Array.isArray(doctorsResult.data) ? doctorsResult.data : [];
        setDoctors(doctorsData.filter((doc: Doctor) => !doc.is_deleted));
      } else {
        setError(doctorsResult.error || "Failed to load doctors");
      }
      if (clinicsResult.success) {
        const clinicsData = Array.isArray(clinicsResult.data) ? clinicsResult.data : [];
        setClinics(clinicsData);
      }
      setLoading(false);
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Helper to get clinic name from id
  const getClinicNameById = (id: number) => {
    const clinic = clinics.find((c) => c.id === id);
    return clinic ? clinic.clinic_name : "N/A";
  };

  // Map form data to backend API field names
  const mapDoctorFormDataToApi = (formData: Record<string, unknown>) => {
    return {
      doctor_name: formData.name,
      clinic_id: formData.clinic ? parseInt(formData.clinic as string, 10) : 1,
      specialization: formData.specialization,
      doctor_email: formData.email,
      doctor_phonenumber: formData.phone,
      experience_years: parseInt(formData.experience as string) || 0,
      qualification: formData.qualification,
    };
  };

  // Handle doctor creation
  const handleAddDoctor = async (formData: Record<string, unknown>) => {
    const apiData = mapDoctorFormDataToApi(formData);
    const result = await createDoctor(apiData);
    if (result.success) {
      // Refresh the doctor list after adding
      const doctorsResult = await getDoctors();
      if (doctorsResult.success) {
        const doctorsData = Array.isArray(doctorsResult.data) ? doctorsResult.data : [];
        setDoctors(doctorsData.filter((doc: Doctor) => !doc.is_deleted));
      }
      setIsAddDoctorOpen(false);
    }
    // Error messages are handled in the hook
  };

  // Handle doctor deletion
  const handleDeleteDoctor = async (doctor: Doctor) => {
    const result = await deleteDoctor(doctor.id);
    if (result.success) {
      // Refresh the doctor list after deleting
      const doctorsResult = await getDoctors();
      if (doctorsResult.success) {
        const doctorsData = Array.isArray(doctorsResult.data) ? doctorsResult.data : [];
        setDoctors(doctorsData.filter((doc: Doctor) => !doc.is_deleted));
      }
    }
    // Error messages are handled in the hook
  };

  // Handle edit doctor
  const handleEditDoctor = (doctor: Doctor) => {
    setSelectedDoctor(null); // Close detail modal if open
    setIsAddDoctorOpen(false);
    setEditDoctorInitialData({
      id: String(doctor.id), // Store the doctor ID for proper identification during update
      name: String(doctor.doctor_name || ''),
      specialization: String(doctor.specialization || ''),
      qualification: String(doctor.qualification || ''),
      clinic: String(doctor.clinic_id ?? ''), // must match form field name
      email: String(doctor.doctor_email || ''),
      phone: formatPhoneNumber(String(doctor.doctor_phonenumber || '')),
      experience: doctor.experience_years !== undefined ? String(doctor.experience_years) : '',
    });
    setIsEditDoctorOpen(true);
  };

  // Handle doctor update
  const handleUpdateDoctor = async (formData: Record<string, unknown>) => {
    if (!editDoctorInitialData) return;
    
    // Use the stored doctor ID from editDoctorInitialData for proper identification
    const doctorId = editDoctorInitialData.id;
    
    if (!doctorId) {
      console.error('Doctor ID not found for update');
      return;
    }
    
    const apiData = mapDoctorFormDataToApi(formData);
    
    // Call the update API using the stored doctor ID (convert string back to number)
    const result = await updateDoctor(Number(doctorId), apiData);
    
    if (result.success) {
      // Refresh the doctor list after updating
      const doctorsResult = await getDoctors();
      if (doctorsResult.success) {
        const doctorsData = Array.isArray(doctorsResult.data) ? doctorsResult.data : [];
        setDoctors(doctorsData.filter((doc: Doctor) => !doc.is_deleted));
      }
      setIsEditDoctorOpen(false);
      setEditDoctorInitialData(undefined);
    }
    // Error messages are handled in the hook
  };

  // Get unique specialties from doctors data
  const specialties = [
    "all",
    ...Array.from(
      new Set(
        doctors
          .map((d) => d.specialization)
          .filter((spec): spec is string => Boolean(spec))
      )
    ),
  ];

  // Determine doctor status (you can customize this logic)
  const getDoctorStatus = (doctor: Doctor) => {
    // For now, we'll use a simple logic based on creation date
    // You can enhance this based on your business logic
    if (!doctor.created_at || (typeof doctor.created_at !== 'string' && typeof doctor.created_at !== 'number')) return "Available";
    const createdDate = new Date(doctor.created_at);
    const now = new Date();
    const diffInHours =
      (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) return "Available";
    if (diffInHours < 72) return "Busy";
    return "On Leave";
  };

  // Generate next available time (placeholder logic)

  const filteredDoctors = doctors.filter((doctor) => {
    const matchesSearch =
      (doctor.doctor_name ? doctor.doctor_name.toLowerCase() : "").includes(searchTerm.toLowerCase()) ||
      ((doctor.specialization || "").toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesSpecialty =
      filterSpecialty === "all" || doctor.specialization === filterSpecialty;
    return matchesSearch && matchesSpecialty;
  });

  // Pagination logic
  const paginatedDoctors = filteredDoctors.slice((page - 1) * pageSize, page * pageSize);

  // Handle view doctor
  const handleViewDoctor = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
  };

  if (loading) {
    return (
      <PageSection>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">
              {DOCTOR_DIRECTORY_TITLE}
            </h2>
            <p className="text-gray-600">{DOCTOR_DIRECTORY_SUBTITLE}</p>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600">{DOCTOR_DIRECTORY_LOADING}</span>
          </div>
        </div>
      </PageSection>
    );
  }

  if (error) {
    return (
      <PageSection>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">
              {DOCTOR_DIRECTORY_TITLE}
            </h2>
            <p className="text-gray-600">{DOCTOR_DIRECTORY_SUBTITLE}</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                {DOCTOR_DIRECTORY_RETRY}
              </Button>
            </div>
          </CardContent>
        </Card>
      </PageSection>
    );
  }

  return (
    <PageSection>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            {DOCTOR_DIRECTORY_TITLE}
          </h2>
          <p className="text-gray-600">{DOCTOR_DIRECTORY_SUBTITLE}</p>
        </div>
        <Button variant="main" onClick={() => setIsAddDoctorOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {DOCTOR_DIRECTORY_ADD_NEW}
        </Button>
      </div>

      {/* Search and Filter */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <SearchBar
              placeholder={DOCTOR_DIRECTORY_SEARCH_PLACEHOLDER}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 min-w-[220px]"
            />
            <div className="flex gap-2 flex-wrap justify-center md:justify-end w-full md:w-auto">
              <Select
                value={filterSpecialty}
                onValueChange={(value) => setFilterSpecialty(value)}
              >
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select Specialty" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{DOCTOR_DIRECTORY_FILTER_ALL}</SelectItem>
                  {specialties
                    .filter((specialty) => specialty !== "all")
                    .map((specialty) => (
                      <SelectItem key={specialty} value={specialty}>
                        {specialty}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="mb-6">
        <DoctorStatsGrid
          doctors={doctors}
          specialties={specialties}
          getDoctorStatus={getDoctorStatus}
        />
      </div>

      {/* Table View */}
      <DoctorDirectoryTable
        doctors={paginatedDoctors}
        clinics={clinics}
        page={page}
        pageSize={pageSize}
        total={filteredDoctors.length}
        onPageChange={setPage}
        onViewDoctor={handleViewDoctor}
        onEditDoctor={handleEditDoctor}
        onDeleteDoctor={handleDeleteDoctor}
      />

      {/* Centered Doctor Detail Card as Modal */}
      {selectedDoctor && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
          <div className="relative w-full max-w-xl">
            <button
              className="absolute right-2 top-2 z-10 flex items-center justify-center w-8 h-8 rounded-full text-gray-500 hover:bg-gray-100 transition-colors"
              onClick={() => setSelectedDoctor(null)}
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </button>
            <DetailedDoctorCard
              name={selectedDoctor.doctor_name || "N/A"}
              specialization={selectedDoctor.specialization || "General Medicine"}
              statusBadge={null}
              phone={formatPhoneNumber(selectedDoctor.doctor_phonenumber || "N/A")}
              email={selectedDoctor.doctor_email || "N/A"}
              clinic={getClinicNameById(selectedDoctor.clinic_id)}
              consultation={selectedDoctor.consultationHours || "-"}
              workingDays={selectedDoctor.working_days || "-"}
              experience={selectedDoctor.experience_years !== undefined ? `${selectedDoctor.experience_years} years` : "-"}
              patients={typeof selectedDoctor.patients === 'number' ? selectedDoctor.patients : 0}
              nextAvailable={null}
              qualification={selectedDoctor.qualification || undefined}
            />
          </div>
        </div>
      )}

      {/* Add Doctor Form Modal */}
      <AddDoctorForm
        isOpen={isAddDoctorOpen}
        onClose={() => setIsAddDoctorOpen(false)}
        onSubmit={handleAddDoctor}
        clinics={clinics}
      />

      {/* Edit Doctor Form Modal */}
      {isEditDoctorOpen && (
        <AddDoctorForm
          isOpen={isEditDoctorOpen}
          onClose={() => { setIsEditDoctorOpen(false); setEditDoctorInitialData(undefined); }}
          onSubmit={handleUpdateDoctor}
          initialData={editDoctorInitialData}
          submitButtonText="Update Doctor"
          title="Edit Doctor"
          clinics={clinics}
        />
      )}
    </PageSection>
  );
};

export default DoctorDirectory;
