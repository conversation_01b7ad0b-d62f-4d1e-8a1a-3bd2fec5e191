/**
 * Escalation Validators: express-validator schemas for escalation create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating an escalation
export const createEscalationSchema = [
  body('clinic_id')
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID)
    .notEmpty()
    .withMessage(constants.CLINIC_ID_INVALID),
  body('patient_id')
    .isInt()
    .withMessage(constants.PATIENT_ID_INVALID)
    .notEmpty()
    .withMessage(constants.PATIENT_ID_REQUIRED),
  body('summary')
    .notEmpty()
    .withMessage(constants.SUMMARY_REQUIRED)
    .isString()
    .withMessage(constants.SUMMARY_STRING),
  body('transcript')
    .optional()
    .isArray()
    .withMessage(constants.TRANSCRIPT_ARRAY),
  body('status')
    .isIn(['open', 'assigned', 'in_progress', 'resolved'])
    .withMessage(constants.STATUS_INVALID)
    .notEmpty()
    .withMessage(constants.STATUS_REQUIRED),
  body('tags')
    .isIn(['ai_rejection', 'busy'])
    .withMessage(constants.TAGS_INVALID)
    .notEmpty()
    .withMessage(constants.TAGS_REQUIRED),
  body('assignee_id')
    .optional()
    .isInt()
    .withMessage(constants.ASSIGNEE_ID_INVALID),
];

// Validation schema for creating an escalation with transcript from ElevenLabs
export const createEscalationWithTranscriptSchema = [
  body('clinic_id')
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID)
    .notEmpty()
    .withMessage(constants.CLINIC_ID_INVALID),
  body('phone_number')
    .isString()
    .withMessage(constants.PATIENT_ID_INVALID)
    .notEmpty()
    .withMessage(constants.PATIENT_ID_REQUIRED),
  body('summary')
    .notEmpty()
    .withMessage(constants.SUMMARY_REQUIRED)
    .isString()
    .withMessage(constants.SUMMARY_STRING),
  body('status')
    .optional()
    .isIn(['open', 'assigned', 'in_progress', 'resolved'])
    .withMessage(constants.STATUS_INVALID),
  body('tags')
    .isIn(['ai_rejection', 'busy'])
    .withMessage(constants.TAGS_INVALID)
    .notEmpty()
    .withMessage(constants.TAGS_REQUIRED),
  body('assignee_id')
    .optional()
    .isInt()
    .withMessage(constants.ASSIGNEE_ID_INVALID),
  body('conversation_id')
    .notEmpty()
    .withMessage(constants.CONVERSATION_ID_REQUIRED)
    .isString()
    .withMessage(constants.CONVERSATION_ID_STRING),
  // body('api_key')
  //   .notEmpty()
  //   .withMessage(constants.API_KEY_REQUIRED)
  //   .isString()
  //   .withMessage(constants.API_KEY_STRING),
];

// Validation schema for updating an escalation
export const updateEscalationSchema = [
  body('clinic_id')
    .optional()
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID),
  body('patient_id')
    .optional()
    .isInt()
    .withMessage(constants.PATIENT_ID_INVALID),
  body('summary')
    .optional()
    .isString()
    .withMessage(constants.SUMMARY_STRING),
  body('transcript')
    .optional()
    .isString()
    .withMessage(constants.TRANSCRIPT_STRING),
  body('status')
    .optional()
    .isIn(['open', 'assigned', 'in_progress', 'resolved'])
    .withMessage(constants.STATUS_INVALID),
  body('tags')
    .optional()
    .isIn(['ai_rejection', 'busy'])
    .withMessage(constants.TAGS_INVALID),
  body('assignee_id')
    .optional()
    .isInt()
    .withMessage(constants.ASSIGNEE_ID_INVALID),
]; 