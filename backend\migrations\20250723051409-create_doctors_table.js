import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable({ tableName: 'doctor'}, {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    clinic_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: { tableName: 'clinics' },
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    doctor_name: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    specialization: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    qualification: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    experience_years: {
      type: Sequelize.INTEGER,
      allowNull: true,
    },
    doctor_email: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    doctor_phonenumber: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
    },
    updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable({ tableName: 'doctor' });
}; 