// AUTHENTICATION & LOGIN LOG MESSAGES
export const SIGNING_USER = 'Signing up a new user';
export const ERROR_SIGNING_UP_USER = 'Error signing up user';
export const LOGGING_USER = 'Logging user';
export const ERROR_LOGGING_IN_USER = 'Error in logging user';
export const ERROR_LOGGING_OUT_USER = 'Error during logout';
export const ERROR_LOGGING_ACTIVITY = 'Error in logging activity';
export const INVALID_CREDENTIALS = 'Invalid Credentials';
export const PASSWORD_INCORRECT = 'Password is incorrect.';
export const USER_DOES_NOT_EXIST = 'User does not exist.';
export const USER_ALREADY_EXISTS_LOG_MESSAGE =
  'User with the same email or phone number already exists.';
export const ERROR_FETCHING_USER_BY_EMAIL_OR_PHONE =
  'Error fetching user by email or phone';
export const ERROR_VALIDATING_PASSWORD = 'Error validating password.';

// TOKEN & AUTHORIZATION LOG MESSAGES
export const TOKEN_CREATED = (userId) => `Token created for user ID: ${userId}`;
export const AUTH_HEADER_MISSING = 'Authorization header is missing';
export const TOKEN_MISSING = 'Token is missing from authorization header';
export const TOKEN_VERIFIED = (userId) =>
  `Token verified successfully for user ID: ${userId}`;
export const TOKEN_VERIFICATION_FAILED = (errorMessage) =>
  `Token verification failed: ${errorMessage}`;
export const ERROR_CREATING_TOKEN = 'Error creating token';
export const ERROR_REFRESHING_TOKEN = 'Error refreshing token';

// PASSWORD RESET LOG MESSAGES
export const LOGGING_FORGOT_PASSWORD = 'Logging forgot password';
export const LOGGING_RESET_PASSWORD = 'Logging reset password';
export const PASSWORD_RESET_EMAIL_SENT =
  'Password reset instructions sent to your email.';
export const EMAIL_SENT_SUCCESS = 'Email sent successfully';
export const EMAIL_SEND_FAILED = 'Email sent failed';
export const PASSWORD_RESET_SUCCESSFULLY = 'Password reset successfully';
export const PASSWORD_CONFIRM_PASSWORD_MISMATCH =
  'New password and confirm password do not match.';
export const ERROR_INVALID_RESET_TOKEN = 'Invalid reset token';
export const RESET_PASSWORD_TOKEN_EXPIRED = 'Reset password token has expired';
export const ERROR_PASSWORD_FORGOT_EMAIL_SENT =
  'Error in password forgot email sent';
export const ERROR_PASSWORD_RESET_FAILED = 'Error in password reset failed';
export const ERROR_GENERATING_RESET_PASSWORD_TOKEN =
  'Error generating reset password token';
export const RESET_PASSWORD_TOKEN_CREATED = (userId) =>
  `Reset password token created for user: ${userId}`;
export const ERROR_UPDATING_USER_PASSWORD = 'Error updating user password';
export const ERROR_VERIFYING_RESET_PASSWORD_TOKEN =
  'Error in verifying the reset token';
export const RESET_PASSWORD_TOKEN_VERIFIED = (userId) =>
  `Reset password token verified for user: ${userId}`;
export const ERROR_FETCHING_USER_BY_RESET_PASSWORD_TOKEN = (error) =>
  `Error fetching user by reset password token: ${error.message}`;
export const RESET_PASSWORD_TOKEN_EXPIRY = '1h';

// PASSWORD HASHING LOG MESSAGES
export const ERROR_HASHING_PASSWORD = 'Error hashing password';
export const ERROR_COMPARING_PASSWORDS = 'Error comparing passwords';
export const PASSWORD_HASHED_SUCCESSFULLY = 'Password hashed successfully';
export const PASSWORD_COMPARISON_SUCCESSFUL = 'Password comparison successful';

// USER MANAGEMENT LOG MESSAGES
// User CRUD operations
export const CREATING_USER = 'Creating a new user';
export const FETCHING_USERS = 'Fetching all users';
export const FETCHING_USER_BY_ID = 'Fetching user by ID';
export const UPDATING_USER = 'Updating user';
export const DELETING_USER = 'Deleting user';
export const USER_DELETED = 'User deleted successfully';
export const USER_NOT_FOUND = 'User not found';

// User CRUD error messages
export const ERROR_CREATING_USER = 'Error creating user';
export const ERROR_FETCHING_USERS = 'Error fetching users';
export const ERROR_FETCHING_USER_BY_ID = 'Error fetching user by ID';
export const ERROR_UPDATING_USER = 'Error updating user';
export const ERROR_DELETING_USER = 'Error deleting user';

// User validation messages
export const USER_EXISTS_EMAIL = 'Attempt to create user with existing email';
export const USER_EMAIL_EMAIL = 'User email already exist'
export const USER_EXISTS_PHONE =
  'Attempt to create user with existing phone number';

// User search operations
export const FETCHING_USER_BY_EMAIL = 'Fetching user by email';
export const USER_FETCHED_EMAIL = 'Successfully fetched user with email';
export const USER_NOT_FOUND_EMAIL = 'User with email not found';
export const ERROR_FETCHING_USER_BY_EMAIL = 'Error fetching user by email';
export const FETCHING_USER_BY_PHONE = 'Fetching user by phone number';
export const ERROR_FETCHING_USER_BY_PHONE =
  'Error fetching user by phone number';
export const USER_FETCHED_PHONE = 'Successfully fetched user with phone number';
export const USER_NOT_FOUND_PHONE = 'User with phone number not found';

// PROFILE MANAGEMENT LOG MESSAGES
export const FETCHING_PROFILE_BY_ID = 'Fetching profile by ID';
export const ERROR_FETCHING_PROFILE_BY_ID = 'Error fetching profile by id';
export const UPDATING_PROFILE = 'Updating profile';
export const ERROR_UPDATING_PROFILE = 'Error updating profile.';

// ROLE MANAGEMENT LOG MESSAGES
// Role CRUD operations
export const CREATING_ROLE = 'Creating a new role';
export const FETCHING_ROLE_BY_ID = 'Fetching role by id';

// Role error messages
export const ERROR_CREATING_ROLE = 'Error creating role';
export const ERROR_FETCHING_ROLE_BY_ID = 'Error fetching role by id';
export const ERROR_CHECKING_ROLE = 'Error checking user role:';
export const ERROR_FETCHING_ROLE_BY_NAME = 'Error fetching role by id';

// Role validation messages
export const ACCESS_DENIED =
  'Access denied. User does not have sufficient permissions.';
export const INVALID_ROLE =
  'Invalid role provided. Role must be either "admin" or "employee".';

// PATIENT MANAGEMENT LOG MESSAGES
// Patient CRUD operations
export const CREATING_PATIENT = 'Creating a new patient';
export const FETCHING_PATIENTS = 'Fetching all patients';
export const FETCHING_PATIENT_BY_ID = 'Fetching patient by ID';
export const UPDATING_PATIENT = 'Updating patient';
export const DELETING_PATIENT = 'Deleting patient';
export const PATIENT_DELETED = 'Patient deleted successfully';
export const GETTING_INACTIVE_PATIENTS = 'Getting inactive patients';

// Patient CRUD error messages
export const ERROR_CREATING_PATIENT = 'Error creating patient';
export const ERROR_FETCHING_PATIENTS = 'Error fetching patients';
export const ERROR_FETCHING_PATIENT_BY_ID = 'Error fetching patient by ID';
export const ERROR_UPDATING_PATIENT = 'Error updating patient';
export const ERROR_DELETING_PATIENT = 'Error deleting patient';
export const ERROR_GETTING_INACTIVE_PATIENTS = 'Error getting ';

// Patient bulk operations
export const UPLOADING_PATIENT_LIST = 'Uploading patient list from file';
export const PATIENT_LIST_UPLOADED =
  'Patient list uploaded and inserted successfully';
export const ERROR_UPLOADING_PATIENT_LIST = 'Error uploading patient list';
export const GETTING_PATIENT_BY_PHONE = 'Getting patient by phone number'
export const ERROR_GETTING_PATIENT_BY_PHONE = 'Error getting patient by phone number';

// Patient clinic operations
export const FETCHING_PATIENTS_BY_CLINIC = 'Fetching patients by clinic ID';
export const ERROR_FETCHING_PATIENTS_BY_CLINIC = 'Error fetching patients by clinic ID';
export const SUBMITTING_BATCH_CALL = 'Submitting batch call with selected patients';
export const ERROR_SUBMITTING_BATCH_CALL = 'Error submitting batch call';

// Reactivation-related log messages
export const FETCHING_REACTIVATIONS_BY_CLINIC = 'Fetching reactivations by clinic';
export const ERROR_FETCHING_REACTIVATIONS_BY_CLINIC = 'Error fetching reactivations by clinic';
export const FETCHING_REACTIVATION_STATS = 'Fetching reactivation statistics';
export const ERROR_FETCHING_REACTIVATION_STATS = 'Error fetching reactivation statistics';

// CLINIC MANAGEMENT LOG MESSAGES
// Clinic CRUD operations
export const CREATING_CLINIC = 'Creating clinic';
export const FETCHING_CLINICS = 'Fetching clinics';
export const FETCHING_CLINIC_BY_ID = 'Fetching clinic by ID';
export const UPDATING_CLINIC = 'Updating clinic';
export const DELETING_CLINIC = 'Deleting clinic';

// Clinic CRUD error messages
export const ERROR_CREATING_CLINIC = 'Error creating clinic';
export const ERROR_FETCHING_CLINICS = 'Error fetching clinics';
export const ERROR_FETCHING_CLINIC_BY_ID = 'Error fetching clinic by ID';
export const ERROR_UPDATING_CLINIC = 'Error updating clinic';
export const ERROR_DELETING_CLINIC = 'Error deleting clinic';

// Clinic validation messages
export const CLINIC_NOT_FOUND = 'Clinic not found';
export const CLINIC_ALREADY_EXISTS =
  'Clinic with the same email or phone number already exists.';

// DOCTOR MANAGEMENT LOG MESSAGES
// Doctor CRUD operations
export const CREATING_DOCTOR = 'Creating doctor';
export const FETCHING_DOCTORS = 'Fetching doctors';
export const FETCHING_DOCTOR_BY_ID = 'Fetching doctor by ID';
export const UPDATING_DOCTOR = 'Updating doctor';
export const DELETING_DOCTOR = 'Deleting doctor';

// Doctor CRUD error messages
export const ERROR_CREATING_DOCTOR = 'Error creating doctor';
export const ERROR_FETCHING_DOCTORS = 'Error fetching doctors';
export const ERROR_FETCHING_DOCTOR_BY_ID = 'Error fetching doctor by ID';
export const ERROR_UPDATING_DOCTOR = 'Error updating doctor';
export const ERROR_DELETING_DOCTOR = 'Error deleting doctor';

// TEMPLATE MANAGEMENT LOG MESSAGES
// Template CRUD operations 
export const CREATING_TEMPLATE = 'Creating a new template';
export const FETCHING_TEMPLATES = 'Fetching all templates';
export const FETCHING_TEMPLATE_BY_ID = 'Fetching template by ID';
export const UPDATING_TEMPLATE = 'Updating template';
export const DELETING_TEMPLATE = 'Deleting template';
export const TEMPLATE_DELETED = 'Template deleted successfully';

// Template CRUD error messages
export const ERROR_CREATING_TEMPLATE = 'Error creating template';
export const ERROR_FETCHING_TEMPLATES = 'Error fetching templates';
export const ERROR_FETCHING_TEMPLATE_BY_ID = 'Error fetching template by ID';
export const ERROR_UPDATING_TEMPLATE = 'Error updating template';
export const ERROR_DELETING_TEMPLATE = 'Error deleting template';

// CAMPAIGN MANAGEMENT LOG MESSAGES
// Campaign CRUD operations
export const CREATING_CAMPAIGN = 'Creating a new campaign';
export const FETCHING_CAMPAIGNS = 'Fetching all campaigns';
export const FETCHING_CAMPAIGN_BY_ID = 'Fetching campaign by ID';
export const UPDATING_CAMPAIGN = 'Updating campaign';
export const DELETING_CAMPAIGN = 'Deleting campaign';
export const CAMPAIGN_DELETED = 'Campaign deleted successfully';

// Campaign CRUD error messages
export const ERROR_CREATING_CAMPAIGN = 'Error creating campaign';
export const ERROR_FETCHING_CAMPAIGNS = 'Error fetching campaigns';
export const ERROR_FETCHING_CAMPAIGN_BY_ID = 'Error fetching campaign by ID';
export const ERROR_UPDATING_CAMPAIGN = 'Error updating campaign';
export const ERROR_DELETING_CAMPAIGN = 'Error deleting campaign';

// ESCALATION MANAGEMENT LOG MESSAGES
// Escalation CRUD operations
export const CREATING_ESCALATION = 'Creating escalation';
export const CREATING_ESCALATION_WITH_TRANSCRIPT = 'Creating escalation with transcript from ElevenLabs';
export const FETCHING_ESCALATIONS = 'Fetching escalations';
export const FETCHING_ESCALATION_BY_ID = (id) => `Fetching escalation by ID: ${id}`;
export const UPDATING_ESCALATION = (id) => `Updating escalation: ${id}`;
export const DELETING_ESCALATION = (id) => `Deleting escalation: ${id}`;

// Escalation CRUD error messages
export const ERROR_CREATING_ESCALATION = 'Error creating escalation';
export const ERROR_CREATING_ESCALATION_WITH_TRANSCRIPT = 'Error creating escalation with transcript';
export const ERROR_FETCHING_ESCALATIONS = 'Error fetching escalations';
export const ERROR_FETCHING_ESCALATION_BY_ID = 'Error fetching escalation by ID';
export const ERROR_UPDATING_ESCALATION = 'Error updating escalation';
export const ERROR_DELETING_ESCALATION = 'Error deleting escalation';

// ElevenLabs integration messages
export const ERROR_FETCHING_TRANSCRIPT_ELEVENLABS = 'Error fetching transcript from ElevenLabs';

// APPOINTMENT MANAGEMENT LOG MESSAGES
// Appointment CRUD operations
export const CREATING_APPOINTMENT = 'Creating a new appointment';
export const FETCHING_APPOINTMENTS = 'Fetching all appointments';
export const FETCHING_APPOINTMENT_BY_ID = 'Fetching appointment by ID';
export const UPDATING_APPOINTMENT = 'Updating appointment';
export const DELETING_APPOINTMENT = 'Deleting appointment';
export const APPOINTMENT_DELETED = 'Appointment deleted successfully';

// Appointment CRUD error messages
export const ERROR_CREATING_APPOINTMENT = 'Error creating appointment';
export const ERROR_FETCHING_APPOINTMENTS = 'Error fetching appointments';
export const ERROR_FETCHING_APPOINTMENT_BY_ID = 'Error fetching appointment by ID';
export const ERROR_UPDATING_APPOINTMENT = 'Error updating appointment';
export const ERROR_DELETING_APPOINTMENT = 'Error deleting appointment';

// Time slot availability messages
export const CHECKING_TIME_SLOT_AVAILABILITY = 'Checking time slot availability';
export const ERROR_CHECKING_TIME_SLOT_AVAILABILITY = 'Error checking time slot availability';

// Appointment audit log messages
export const ERROR_LOGGING_APPOINTMENT_AUDIT = 'Error logging appointment audit';
export const ERROR_FETCHING_APPOINTMENT_AUDIT_LOGS = 'Error fetching appointment audit logs';
export const ERROR_FETCHING_ALL_APPOINTMENT_AUDIT_LOGS = 'Error fetching all appointment audit logs';

// Available time slots messages
export const GETTING_AVAILABLE_TIME_SLOTS = 'Getting available time slots for doctor';
export const ERROR_GETTING_AVAILABLE_TIME_SLOTS = 'Error getting available time slots';

// AI CALL LOG MANAGEMENT LOG MESSAGES
// AI Call Log CRUD operations
export const CREATING_AI_CALL_LOG = 'Creating AI call log';
export const FETCHING_AI_CALL_LOGS = 'Fetching AI call logs';

// AI Call Log CRUD error messages
export const ERROR_CREATING_AI_CALL_LOG = 'Error creating AI call log';
export const ERROR_FETCHING_AI_CALL_LOGS = 'Error fetching AI call logs';

// CRON SERVICE LOG MESSAGES
export const INITIALIZING_CRON_JOBS = 'Initializing cron jobs for automated outbound calls...';
export const CRON_JOBS_INITIALIZED = 'Cron jobs initialized successfully with schedules:';
export const STARTING_CRON_JOB = (jobName) => `Starting ${jobName.toLowerCase()} cron job`;
export const CRON_JOB_COMPLETED = (jobName) => `${jobName} cron job completed successfully`;
export const ERROR_IN_CRON_JOB = (jobName, error) => `Error in ${jobName.toLowerCase()} cron job: ${error}`;
export const PROCESSING_ALL_CLINICS = 'Processing all clinics for automated outbound calls';
export const FOUND_CLINICS_WITH_REACTIVATION = (count) => `Found ${count} clinics with reactivation settings`;
export const COMPLETED_PROCESSING_ALL_CLINICS = 'Completed processing all clinics for automated outbound calls';
export const PROCESSING_CONTINUOUS_CALLS = 'Processing continuous outbound calls for all clinics';
export const FOUND_ACTIVE_CLINICS = (count) => `Found ${count} active clinics for continuous processing`;
export const COMPLETED_CONTINUOUS_CALLS = 'Completed processing continuous outbound calls for all clinics';
export const CALLING_OUTBOUND_API = 'Calling /outbound API for all active clinics';
export const FOUND_CLINICS_FOR_API_CALLS = (count) => `Found ${count} active clinics for API calls`;
export const COMPLETED_API_CALLS = 'Completed calling /outbound API for all clinics';
export const PROCESSING_CLINIC = (clinicId, clinicName) => `Processing clinic ${clinicId} (${clinicName}) for outbound calls`;
export const SKIPPING_CLINIC_TIME = (clinicId) => `Skipping clinic ${clinicId} - not time for calls yet`;
export const SUCCESSFULLY_SUBMITTED_BATCH = (clinicId, count) => `Successfully submitted batch call for clinic ${clinicId} with ${count} patients`;
export const NO_INACTIVE_PATIENTS = (clinicId) => `No inactive patients found for clinic ${clinicId}`;
export const ERROR_PROCESSING_CLINIC = (clinicId, error) => `Error processing clinic ${clinicId} for outbound calls: ${error}`;
export const CALLING_OUTBOUND_API_FOR_CLINIC = (clinicId, clinicName) => `Calling /outbound API for clinic ${clinicId} (${clinicName})`;
export const SUCCESSFULLY_CALLED_OUTBOUND_API = (clinicId) => `Successfully called /outbound API for clinic ${clinicId}`;
export const ERROR_CALLING_OUTBOUND_API = (clinicId, error) => `Error calling /outbound API for clinic ${clinicId}: ${error}`;
export const MAKING_OUTBOUND_API_CALL = (endpoint, clinicId, payload) => `Making outbound API call to: ${endpoint}`;
export const OUTBOUND_API_CALL_SUCCESSFUL = (clinicId, status) => `Outbound API call successful for clinic ${clinicId}`;
export const PROCESSING_CLINIC_WITH_DAYS = (clinicId, days) => `Processing clinic ${clinicId} with ${days} days for inactive patients`;
export const NO_INACTIVE_PATIENTS_WITH_DAYS = (clinicId, days) => `No inactive patients found for clinic ${clinicId} with ${days} days`;
export const FOUND_INACTIVE_PATIENTS = (clinicId, count) => `Found ${count} inactive patients for clinic ${clinicId}`;
export const ERROR_PROCESSING_CLINIC_WITH_DAYS = (clinicId, error) => `Error processing clinic ${clinicId} with specific days: ${error}`;
export const PROCESSING_ALL_PATIENTS = (clinicId) => `Processing all patients for clinic ${clinicId}`;
export const NO_PATIENTS_FOUND = (clinicId) => `No patients found for clinic ${clinicId}`;
export const FOUND_PATIENTS = (clinicId, count) => `Found ${count} patients for clinic ${clinicId}`;
export const ERROR_PROCESSING_ALL_PATIENTS = (clinicId, error) => `Error processing all patients for clinic ${clinicId}: ${error}`;
export const MANUALLY_CALLING_OUTBOUND_API = (clinicId) => `Manually calling /outbound API for clinic ${clinicId}`;
export const SUCCESSFULLY_CALLED_OUTBOUND_API_MANUALLY = (clinicId) => `Successfully called /outbound API manually for clinic ${clinicId}`;
export const ERROR_MANUALLY_CALLING_OUTBOUND_API = (clinicId, error) => `Error manually calling /outbound API for clinic ${clinicId}: ${error}`;
export const TRIGGERING_OUTBOUND_CALLS = (clinicId) => `Triggering outbound calls for clinic ${clinicId}`;
export const ERROR_TRIGGERING_OUTBOUND_CALLS = (clinicId, error) => `Error triggering outbound calls for clinic ${clinicId}: ${error}`;
export const CLINIC_NOT_FOUND_FOR_TRIGGER = (clinicId) => `Clinic with ID ${clinicId} not found`;
export const CLINIC_NOT_ACTIVE = (clinicId) => `Clinic ${clinicId} is not active`;
export const SUCCESSFULLY_TRIGGERED_OUTBOUND_CALLS = (clinicId) => `Successfully triggered outbound calls for clinic ${clinicId}`;
export const ERROR_IN_PROCESS_ALL_CLINICS = (error) => `Error in processAllClinicsForOutboundCalls: ${error}`;
export const ERROR_IN_PROCESS_CONTINUOUS_CALLS = (error) => `Error in processContinuousOutboundCalls: ${error}`;
export const ERROR_IN_CALL_OUTBOUND_API_ALL_CLINICS = (error) => `Error in callOutboundAPIForAllClinics: ${error}`;

// PATIENT CONTROLLER LOG MESSAGES
export const TRIGGERING_MANUAL_OUTBOUND_CALLS = 'Triggering manual outbound calls for clinic';
export const ERROR_TRIGGERING_OUTBOUND_CALLS_CONTROLLER = (error) => `Error triggering outbound calls: ${error}`;
export const GETTING_CRON_JOB_STATUS = 'Getting cron job status';
export const ERROR_GETTING_CRON_JOB_STATUS = (error) => `Error getting cron job status: ${error}`;
export const MANUALLY_TRIGGERING_API_CALL = 'Manually triggering API call for clinic';
export const ERROR_TRIGGERING_API_CALL = (error) => `Error triggering API call: ${error}`;

// APPOINTMENT NOTIFICATION LOG MESSAGES
export const SENDING_APPOINTMENT_CONFIRMATION_NOTIFICATIONS = 'Sending appointment confirmation notifications';
export const SENDING_APPOINTMENT_UPDATE_NOTIFICATIONS = 'Sending appointment update notifications';
export const SENDING_APPOINTMENT_CANCELLATION_NOTIFICATIONS = 'Sending appointment cancellation notifications';
export const APPOINTMENT_NOTIFICATIONS_SENT = (appointmentData) => `Appointment notifications sent successfully for appointment ${appointmentData.id}`;
export const APPOINTMENT_NOTIFICATIONS_FAILED = (appointmentData) => `Failed to send appointment notifications for appointment ${appointmentData.id}`;

// NOTIFICATION SERVICE LOG MESSAGES
export const FETCHING_NOTIFICATION_DATA = 'Fetching required data for notifications';
export const PATIENT_NOT_FOUND_NOTIFICATION = (appointmentData) => `Patient not found for notification ${appointmentData.id}, skipping notifications`;
export const DOCTOR_NOT_FOUND_NOTIFICATION = (appointmentData) => `Doctor not found for notification ${appointmentData.id}, skipping notifications`;
export const CLINIC_NOT_FOUND_NOTIFICATION = (appointmentData) => `Clinic not found for notification ${appointmentData.id}, skipping notifications`;
export const EMAIL_SERVICE_DISABLED = (clinicData) => `Email service disabled for clinic ${clinicData.id}, skipping email notification`;
export const SMS_SERVICE_DISABLED = (clinicData) => `SMS service disabled for clinic ${clinicData.id}, skipping SMS notification`;
export const FAILED_TO_FETCH_NOTIFICATION_DATA = (appointmentData) => `Failed to fetch required data for notification for appointment ${appointmentData.id}:`;

// SMS SERVICE LOG MESSAGES
export const SENDING_SMS_MESSAGE = 'Sending SMS message';
export const SMS_SENT_SUCCESSFULLY = 'SMS sent successfully';
export const SMS_SEND_FAILED = 'Failed to send SMS';
export const PATIENT_NO_PHONE_NUMBER = 'Patient has no phone number for SMS';
export const APPOINTMENT_CONFIRMATION_SMS_SENT = 'Appointment confirmation SMS sent';
export const APPOINTMENT_UPDATE_SMS_SENT = 'Appointment update SMS sent';
export const APPOINTMENT_CANCELLATION_SMS_SENT = 'Appointment cancellation SMS sent';

// EMAIL SERVICE LOG MESSAGES
export const SENDING_EMAIL_MESSAGE = 'Sending email message';
export const EMAIL_SENT_SUCCESSFULLY = 'Email sent successfully';
export const APPOINTMENT_CONFIRMATION_EMAIL_SENT = (appointmentData) => `Appointment confirmation email sent to ${appointmentData.patient_email} for appointment ${appointmentData.id}`;
export const APPOINTMENT_UPDATE_EMAIL_SENT = (appointmentData) => `Appointment update email sent to ${appointmentData.patient_email} for appointment ${appointmentData.id}`;
export const APPOINTMENT_CANCELLATION_EMAIL_SENT = (appointmentData) => `Appointment cancellation email sent to ${appointmentData.patient_email} for appointment ${appointmentData.id}`;

// TIMEZONE SERVICE LOG MESSAGES
export const CONVERTING_INDIAN_TIME_TO_UTC = 'Converting Indian time to UTC';
export const CONVERTING_UTC_TO_INDIAN_TIME = 'Converting UTC to Indian time';
export const TIMEZONE_CONVERSION_SUCCESSFUL = 'Timezone conversion successful';
export const TIMEZONE_CONVERSION_FAILED = 'Timezone conversion failed';
export const INVALID_DATE_FORMAT_PROVIDED = 'Invalid date format provided';
export const FORMATTING_INDIAN_TIME = 'Formatting Indian time for display';

// WEBHOOK LOG MESSAGES
export const PROCESSING_WEBHOOK = 'Processing webhook request';
export const WEBHOOK_SIGNATURE_VERIFIED = 'Webhook signature verified successfully';
export const WEBHOOK_SIGNATURE_VERIFICATION_FAILED = 'Webhook signature verification failed';
export const WEBHOOK_PROCESSED_SUCCESSFULLY = 'Webhook processed successfully';
export const ERROR_PROCESSING_WEBHOOK = 'Error processing webhook';
export const FETCHING_TRANSCRIPT_FROM_ELEVENLABS = 'Fetching transcript from ElevenLabs';
export const TRANSCRIPT_FETCHED_SUCCESSFULLY = 'Transcript fetched successfully from ElevenLabs';
