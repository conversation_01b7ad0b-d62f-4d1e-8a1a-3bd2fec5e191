import React from "react";
import { Bell } from "lucide-react";
import {
  ACCESSIBILITY,
  CSS_CLASSES,
  ICONS,
} from "@/Constants/Header";

interface NotificationButtonProps {
  className?: string;
  hasNotifications?: boolean;
  onClick?: () => void;
}

const NotificationButton: React.FC<NotificationButtonProps> = ({ 
  className = "",
  hasNotifications = true,
  onClick
}) => {
  return (
    <button
      aria-label={ACCESSIBILITY.NOTIFICATION_BUTTON}
      onClick={onClick}
      className={className}
      style={{
        width: 40,
        height: 40,
        borderRadius: 8,
        padding: 8,
        background: "white",
        border: "none",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        gap: 8,
        opacity: 1,
        boxSizing: "border-box",
        cursor: "pointer",
      }}
    >
      <Bell className={ICONS.BELL} />
      {hasNotifications && (
        <span className={CSS_CLASSES.NOTIFICATION_BUTTON}></span>
      )}
    </button>
  );
};

export default NotificationButton; 