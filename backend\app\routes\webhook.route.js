/**
 * Webhook Routes: Defines API endpoints for webhook operations.
 * <AUTHOR>
 */
import express from 'express';
import { handleElevenLabsWebhook, webhookHealthCheck } from '../controllers/webhook.controller.js';

const router = express.Router();

/**
 * @route POST /v1/webhook/elevenlabs
 * @desc Handle ElevenLabs webhook
 * @access Public
 */
router.post('/elevenlabs', handleElevenLabsWebhook);

/**
 * @route GET /v1/webhook/health
 * @desc Health check for webhook service
 * @access Public
 */
router.get('/health', webhookHealthCheck);

export default router;
