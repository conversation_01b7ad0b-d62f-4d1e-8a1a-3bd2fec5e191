import bcrypt from 'bcryptjs';
import * as userService from '../services/user.service.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { logUserAudit } from '../services/auditLog.service.js';
import { AuditActions, UserAuditDescriptions } from '../utils/auditlog_messages.utils.js';


/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Get all users
 * @api /api/users
 * @method GET
 * <AUTHOR>
 */
export const getAllUsers = async (req, res) => {
  try {
    logger.info(loggerMessages.FETCHING_USERS);

    const users = await userService.getAllUsers();

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.USERS_FETCHED_SUCCESSFULLY, users));
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_USERS, error);

    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Get user by ID
 * @api /api/users/:id
 * @method GET
 * <AUTHOR>
 */
export const getUserById = async (req, res) => {
  try {
    logger.info(loggerMessages.FETCHING_USER_BY_ID);

    const { id } = req.params;
    const user = await userService.getUserById(id);

    if (!user) {
      logger.info(loggerMessages.USER_NOT_FOUND);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.USER_FETCHED_SUCCESSFULLY, user));
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_USER_BY_ID, error);

    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Update user by ID
 * @api /api/users/:id
 * @method PUT
 * <AUTHOR>
 */
export const updateUser = async (req, res) => {
  try {
    logger.info(loggerMessages.UPDATING_USER);

    const { id } = req.params;
    const userData = req.body;

    // Save old value for audit
    const oldUser = await userService.getUserById(id);

    // Check if the request includes a new password
    if (userData.password) {
      // Hash the new password
      const hashedPassword = await bcrypt.hash(userData.password, 8);
      userData.password = hashedPassword;
    }

    // Update the user with the modified data
    const updatedUser = await userService.updateUser(id, userData);
    if (!updatedUser) {
      logger.info(loggerMessages.USER_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }
    // Audit log: user updated
    await logUserAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: id,
      old_value: JSON.stringify(oldUser),
      new_value: JSON.stringify(updatedUser),
      description: UserAuditDescriptions.USER_UPDATED,
    });
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.USER_UPDATED_SUCCESSFULLY, updatedUser));
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_USER, error);
    // Audit log: error on user update
    // Only log error if you have a record_id (not recommended to log with null)

    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Delete user by ID
 * @api /api/users/:id
 * @method DELETE
 * <AUTHOR>
 */
export const deleteUser = async (req, res) => {
  try {
    logger.info(loggerMessages.DELETING_USER);

    const { id } = req.params;

    // 1. Fetch the user
    const oldUser = await userService.getUserById(id);

    // 2. If user doesn't exist, return error (no audit log)
    if (!oldUser) {
      logger.info(loggerMessages.USER_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    // 3. Log audit BEFORE deletion
    await logUserAudit({
      action: AuditActions.DELETE,
      record_id: id,
      user_id: id,
      old_value: JSON.stringify(oldUser),
      description: UserAuditDescriptions.USER_DELETED,
    });

    // 4. Delete the user
    const deletedUser = await userService.deleteUser(id);

    // 5. Return success
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.USER_DELETED_SUCCESSFULLY));
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_USER, error);
    // Audit log: error on user delete
    // Only log error if you have a record_id (not recommended to log with null)

    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};
