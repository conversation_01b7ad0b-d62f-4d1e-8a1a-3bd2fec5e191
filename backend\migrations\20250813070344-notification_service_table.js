/**
 * Migration: Add notification service fields to clinics table
 * Run with: npx sequelize-cli db:migrate
 */

'use strict';

export const up = async (queryInterface, Sequelize) => {
  await queryInterface.addColumn('clinics', 'sms_service', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether SMS notifications are enabled for this clinic'
  });

  await queryInterface.addColumn('clinics', 'email_service', {
    type: Sequelize.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether email notifications are enabled for this clinic'
  });

  // Update existing records to have both services enabled by default
  await queryInterface.sequelize.query(`
    UPDATE clinics 
    SET sms_service = true, email_service = true 
    WHERE sms_service IS NULL OR email_service IS NULL
  `);
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('clinics', 'sms_service');
  await queryInterface.removeColumn('clinics', 'email_service');
};
