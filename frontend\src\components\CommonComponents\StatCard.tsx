// StatCard.tsx
// Renders a card displaying a statistic with an icon, value, and title.
// Used for dashboard/statistics summary views.

import React from 'react';

/**
 * Props for the StatCard component
 * @property title - The title/label for the statistic
 * @property value - The main value to display (number or string)
 * @property icon - Icon to display alongside the title
 */
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
}

/**
 * Renders a statistic card with icon, value, and title.
 */
const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
}) => {
  return (
    <div
      className="bg-white rounded-xl border border-gray-200 shadow-sm p-6 flex flex-col justify-between w-full min-w-0 transition-all duration-200 hover:shadow-lg hover:scale-[1.02] hover:bg-gradient-to-br hover:from-gray-50 hover:to-white cursor-pointer"
      tabIndex={0}
      role="button"
    >
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm font-medium text-gray-600">{title}</div>
        <div className="text-2xl">{icon}</div>
      </div>
      <div className="mb-2">
        <div className="text-3xl font-bold text-gray-900">{value}</div>
      </div>
    </div>
  );
};

export default StatCard; 