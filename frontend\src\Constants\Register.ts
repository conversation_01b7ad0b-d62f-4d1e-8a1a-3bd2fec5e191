export const REGISTER_CONSTANTS = {
  TITLE: "Create your account",
  SUBTITLE: "Join <PERSON><PERSON> AI Clinic",
  NAME_LABEL: "Full Name",
  NAME_PLACEHOLDER: "Your Name",
  NAME_REQUIRED: "Name is required.",
  R<PERSON><PERSON>_LABEL: "Role",
  EMAIL_LABEL: "Email",
  EMAIL_PLACEHOLDER: "<EMAIL>",
  EMAIL_REQUIRED: "Email is required.",
  PASSWORD_LABEL: "Password",
  PASSWORD_PLACEHOLDER: "••••••••",
  PASSWORD_REQUIRED: "Password is required.",
  CONFIRM_PASSWORD_LABEL: "Confirm Password",
  CONFIRM_PASSWORD_PLACEHOLDER: "••••••••",
  CONFIRM_PASSWORD_REQUIRED: "Please confirm your password.",
  PASSWORDS_DO_NOT_MATCH: "Passwords do not match.",
  TERMS_LABEL: "I accept the ",
  TERMS_LINK: "Terms & Conditions",
  TERMS_REQUIRED: "You must accept the Terms & Conditions.",
  REGISTER: "Register",
  ALREADY_HAVE_ACCOUNT: "Already have an account?",
  LOGIN_LINK: "Login",
  REGISTRATION_SUCCESS: "Registration successful!",
  USER_CREATED_SUCCESS: "User created successfully!",
  REGISTRATION_FAILED: "Registration failed",
  PHONE_LABEL: "Phone Number",
  PHONE_PLACEHOLDER: "Enter phone number",
  PHONE_REQUIRED: "Phone number is required.",
  LOADING: "Please wait...",
};

export const ROLE_OPTIONS = [
  { value: "super_admin", label: "Super Admin" },
  { value: "clinic_admin", label: "Clinic Admin" },
  { value: "front_desk", label: "Front Desk" },
  { value: "agent", label: "Agent" },
] as const;

export const VALIDATION_MESSAGES = {
  NAME_MIN_LENGTH: "Name must be at least 2 characters",
  EMAIL_INVALID: "Invalid email address",
  PASSWORD_MIN_LENGTH: "Password must be at least 8 characters",
  PHONE_MIN_LENGTH: "Please enter a valid phone number",
  PASSWORDS_DONT_MATCH: "Passwords don't match",
} as const;

export const TOAST_MESSAGES = {
  REGISTRATION_SUCCESS: "Registration successful! Please check your email to verify your account.",
  REGISTRATION_FAILED: "Internal server error",
  UNEXPECTED_ERROR: "Internal server error",
} as const;

export const FORM_FIELDS = {
  NAME: "name",
  EMAIL: "email",
  PASSWORD: "password",
  CONFIRM_PASSWORD: "confirmPassword",
  PHONE: "user_phonenumber",
  ROLE: "role",
} as const;

export const FORM_IDS = {
  NAME: "name",
  EMAIL: "email",
  PASSWORD: "password",
  CONFIRM_PASSWORD: "confirmPassword",
  PHONE: "user_phonenumber",
  ROLE: "role",
} as const;

export const ROLE_VALUES = {
  SUPER_ADMIN: "super_admin",
  CLINIC_ADMIN: "clinic_admin",
  FRONT_DESK: "front_desk",
  AGENT: "agent",
} as const;

export const DEFAULT_VALUES = {
  ROLE: "front_desk" as const,
  CLINIC_ID: null as null,
} as const;

export const STYLES = {
  CONTAINER_MAX_WIDTH: "480px",
  CONTAINER_WIDTH: "100%",
} as const;

export const ACCESSIBILITY = {
  HIDE_PASSWORD: "Hide password",
  SHOW_PASSWORD: "Show password",
  DENSY_AI_LOGO: "Densy AI Logo",
} as const;

export const ASSETS = {
  LOGO_SRC: "/Densy%20AI.svg",
  LOGO_WIDTH: 150,
  LOGO_HEIGHT: 40,
} as const;

export const ANIMATION = {
  INITIAL_OPACITY: 0,
  INITIAL_SCALE: 0.96,
  FINAL_OPACITY: 1,
  FINAL_SCALE: 1,
  DURATION: 0.5,
} as const;

export const INPUT_TYPES = {
  TEXT: "text",
  EMAIL: "email",
  PASSWORD: "password",
  TEL: "tel",
} as const;

export const BUTTON_TYPES = {
  BUTTON: "button",
  SUBMIT: "submit",
} as const;

export const FORM_ATTRIBUTES = {
  AUTOCOMPLETE: "off",
} as const;
