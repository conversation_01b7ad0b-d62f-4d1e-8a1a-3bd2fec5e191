import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CheckCircle, AlertTriangle, Info } from "lucide-react";

interface ConfirmAlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "default" | "warning" | "info" | "success";
  onConfirm: () => void;
}

const ConfirmAlertDialog: React.FC<ConfirmAlertDialogProps> = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  variant = "default",
  onConfirm,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case "warning":
        return {
          icon: <AlertTriangle className="h-6 w-6 text-white" />,
          titleClass: "text-orange-800",
          descriptionClass: "text-orange-700",
          confirmClass: "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white",
        };
      case "info":
        return {
          icon: <Info className="h-6 w-6 text-white" />,
          titleClass: "text-blue-800",
          descriptionClass: "text-blue-700",
          confirmClass: "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white",
        };
      case "success":
        return {
          icon: <CheckCircle className="h-6 w-6 text-white" />,
          titleClass: "text-green-800",
          descriptionClass: "text-green-700",
          confirmClass: "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white",
        };
      default:
        return {
          icon: <AlertTriangle className="h-6 w-6 text-white" />,
          titleClass: "text-red-800",
          descriptionClass: "text-red-700",
          confirmClass: "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white",
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md border-2 shadow-2xl">
        <AlertDialogHeader className="pb-4">
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 rounded-full" style={{ background: 'linear-gradient(to right, #1e40af, #22c55e)' }}>
              {styles.icon}
            </div>
            <AlertDialogTitle className={`${styles.titleClass} text-lg font-bold`}>
              {title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className={`${styles.descriptionClass} text-base leading-relaxed`}>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-3">
          {cancelText && (
            <AlertDialogCancel className="border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 px-6 py-2">
              {cancelText}
            </AlertDialogCancel>
          )}
          <AlertDialogAction
            onClick={onConfirm}
            className={`${styles.confirmClass} px-6 py-2 font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105`}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmAlertDialog; 