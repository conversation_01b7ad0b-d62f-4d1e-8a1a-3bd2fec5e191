import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Phone, Clock, Users, CheckCircle } from 'lucide-react';
import { PatientWithUserDetails } from '@/hooks/usePatientManagement';

interface BatchCallSubmissionProps {
  selectedPatients: PatientWithUserDetails[];
  onSubmit: (time?: string) => Promise<void>;
  loading: boolean;
}

const BatchCallSubmission: React.FC<BatchCallSubmissionProps> = ({
  selectedPatients,
  onSubmit,
  loading
}) => {
  const [scheduledTime, setScheduledTime] = useState<string>('');

  const handleSubmit = async () => {
    await onSubmit(scheduledTime || undefined);
  };

  const handleSubmitNow = async () => {
    await onSubmit();
  };

  if (selectedPatients.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Batch Call Submission
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            Please select patients to submit a batch call.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Batch Call Submission
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-blue-900">Call Summary</h4>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {selectedPatients.length} patients selected
            </Badge>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-blue-700">Total Patients: {selectedPatients.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-green-700">Ready to Call</span>
            </div>
          </div>
        </div>

        {/* Scheduling Options */}
        <div className="space-y-3">
          <Label htmlFor="scheduledTime" className="text-sm font-medium">
            Schedule for later (optional)
          </Label>
          <div className="flex gap-2">
            <Input
              id="scheduledTime"
              type="datetime-local"
              value={scheduledTime}
              onChange={(e) => setScheduledTime(e.target.value)}
              className="flex-1"
            />
            <Button
              variant="outline"
              onClick={() => setScheduledTime('')}
              disabled={!scheduledTime}
            >
              Clear
            </Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-2">
          <Button
            onClick={handleSubmitNow}
            disabled={loading}
            className="flex-1"
            variant="default"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Submitting...
              </div>
            ) : (
              <>
                <Clock className="h-4 w-4 mr-2" />
                Submit Now
              </>
            )}
          </Button>
          
          {scheduledTime && (
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1"
              variant="outline"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                  Scheduling...
                </div>
              ) : (
                <>
                  <Clock className="h-4 w-4 mr-2" />
                  Schedule Call
                </>
              )}
            </Button>
          )}
        </div>

        {/* Selected Patients Preview */}
        <div className="mt-4">
          <Label className="text-sm font-medium mb-2 block">
            Selected Patients Preview
          </Label>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {selectedPatients.slice(0, 5).map((patient) => (
              <div key={patient.id} className="text-sm text-gray-600 flex items-center gap-2">
                <span>• {patient.first_name} {patient.last_name}</span>
                {patient.phone_number && (
                  <span className="text-gray-400">({patient.phone_number})</span>
                )}
              </div>
            ))}
            {selectedPatients.length > 5 && (
              <div className="text-sm text-gray-400 italic">
                ... and {selectedPatients.length - 5} more patients
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BatchCallSubmission;
