import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createAICallLogSchema } from '../validators/aiCallLog.validator.js';
import * as aiCallLogController from '../controllers/aiCallLog.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new AI call log
// POST /v1/ai-call-log/create
router.post(
  '/create',
  validate(createAICallLogSchema),
  aiCallLogController.createAICallLog
);

// Get all AI call logs
// GET /v1/ai-call-log/list
router.get(
  '/list',
  aiCallLogController.getAllAICallLogs
);

export default router; 