"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import TemplateStatsGrid from "@/components/TemplateMaster/TemplateStatsGrid";
import TemplateMasterTable from "@/components/TemplateMaster/TemplateMasterTable";
import DetailedTemplateCard from "@/components/TemplateMaster/DetailedTemplateCard";
import AddTemplateForm from "@/components/forms/AddTemplateForm";
import SearchBar from "@/components/CommonComponents/SearchBar";
import { TEMPLATE_MASTER_TITLE, TEMPLATE_MASTER_SUBTITLE, TEMPLATE_ACTIONS, TEMPLATE_MODAL_TITLES, TEMPLATE_SEARCH } from "@/Constants/TemplateMaster";
import PageSection from "@/components/CommonComponents/PageSection";
import { useTemplate } from "@/hooks/useTemplate";

interface Template {
  id: number;
  name: string;
  type: string;
  content: string;
  created_at?: string;
  updated_at?: string;
  is_deleted?: boolean;
}

const TemplateMasterPage = () => {
  const [isAddTemplateOpen, setIsAddTemplateOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [editFormInitial, setEditFormInitial] = useState<Record<string, string> | undefined>(undefined);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  const { createTemplate, getTemplates, updateTemplate, deleteTemplate } = useTemplate();

  // Fetch templates on component mount
  useEffect(() => {
    fetchTemplates();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    const result = await getTemplates({ is_deleted: false });
    if (result.success) {
      setTemplates(result.data as Template[] || []);
    }
    setLoading(false);
  };

  // Filter templates based on search term and type filter
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch = searchTerm === "" || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === "all" || template.type === filterType;
    
    return matchesSearch && matchesType;
  });

  // Pagination logic
  const paginatedTemplates = filteredTemplates.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Reset to first page when search or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterType]);

  const handleAddTemplate = async (templateData: Record<string, unknown>) => {
    const result = await createTemplate({
      type: templateData.type as string,
      name: templateData.name as string,
      content: templateData.content as string,
    });
    
    if (result.success) {
      setIsAddTemplateOpen(false);
      fetchTemplates(); // Refresh the list
    }
  };

  const handleViewTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setIsViewModalOpen(true);
  };

  const handleEditTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setEditFormInitial({
      name: template.name,
      type: template.type,
      content: template.content,
    });
    setIsEditModalOpen(true);
  };

  const handleEditTemplateSubmit = async (templateData: Record<string, unknown>) => {
    if (selectedTemplate) {
      const result = await updateTemplate(selectedTemplate.id, {
        type: templateData.type as string,
        name: templateData.name as string,
        content: templateData.content as string,
      });
      
      if (result.success) {
        setIsEditModalOpen(false);
        setSelectedTemplate(null);
        setEditFormInitial(undefined);
        fetchTemplates(); // Refresh the list
      }
    }
  };

  const handleDeleteTemplate = async (template: Template) => {
    const result = await deleteTemplate(template.id);
    if (result.success) {
      fetchTemplates(); // Refresh the list
    }
  };

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">{TEMPLATE_MASTER_TITLE}</h2>
          <p className="text-gray-600">{TEMPLATE_MASTER_SUBTITLE}</p>
        </div>
        <Button
          variant="main"
          onClick={() => setIsAddTemplateOpen(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {TEMPLATE_ACTIONS.ADD_TEMPLATE}
        </Button>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <SearchBar
              placeholder={TEMPLATE_SEARCH.PLACEHOLDER}
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-56">
                  <SelectValue placeholder="Select Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{TEMPLATE_SEARCH.FILTER_ALL}</SelectItem>
                  <SelectItem value="sms">{TEMPLATE_SEARCH.FILTER_SMS}</SelectItem>
                  <SelectItem value="email">{TEMPLATE_SEARCH.FILTER_EMAIL}</SelectItem>
                  <SelectItem value="call_prompt">{TEMPLATE_SEARCH.FILTER_CALL_PROMPT}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <TemplateStatsGrid templates={templates} />

      <TemplateMasterTable
        templates={paginatedTemplates}
        loading={loading}
        page={currentPage}
        pageSize={pageSize}
        total={filteredTemplates.length}
        onPageChange={setCurrentPage}
        onViewTemplate={handleViewTemplate}
        onEditTemplate={handleEditTemplate}
        onDeleteTemplate={handleDeleteTemplate}
      />

      {/* Add Template Modal */}
      <AddTemplateForm
        isOpen={isAddTemplateOpen}
        onClose={() => setIsAddTemplateOpen(false)}
        onSubmit={handleAddTemplate}
      />

      {/* Edit Template Modal */}
      {isEditModalOpen && selectedTemplate && (
        <AddTemplateForm
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedTemplate(null);
            setEditFormInitial(undefined);
          }}
          onSubmit={handleEditTemplateSubmit}
          initialData={editFormInitial}
          submitButtonText={TEMPLATE_ACTIONS.UPDATE_TEMPLATE}
          title={TEMPLATE_MODAL_TITLES.EDIT_TEMPLATE}
        />
      )}

      {/* View Template Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogTitle className="sr-only">{TEMPLATE_MODAL_TITLES.VIEW_TEMPLATE}</DialogTitle>
          {selectedTemplate && (
            <DetailedTemplateCard
              name={selectedTemplate.name}
              type={selectedTemplate.type}
              content={selectedTemplate.content}
              createdDate={selectedTemplate.created_at || ""}
            />
          )}
        </DialogContent>
      </Dialog>
    </PageSection>
  );
};

export default TemplateMasterPage; 