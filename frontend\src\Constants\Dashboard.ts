export const DASHBOARD_STATS = [
  {
    title: "Today's Appointments",
    value: "24",
    change: "+12%",
    icon: "Calendar",
    color: "text-blue-600",
  },
  {
    title: "AI Calls Handled",
    value: "47",
    change: "+8%",
    icon: "Phone",
    color: "text-green-600",
  },
  {
    title: "Reactivation Success",
    value: "68%",
    change: "+15%",
    icon: "TrendingUp",
    color: "text-purple-600",
  },
  {
    title: "Patient Returns",
    value: "156",
    change: "+23%",
    icon: "UserCheck",
    color: "text-orange-600",
  },
];

// New dashboard stats constants
export const DASHBOARD_STATS_MESSAGES = {
  TOTAL_CLINICS: "Total Clinics",
  TOTAL_APPOINTMENTS: "Total Appointments", 
  TOTAL_DOCTORS: "Total Doctors",
  TOTAL_PATIENTS: "Total Patients",
  CLINICS_CHANGE: "+2 this month",
  APPOINTMENTS_CHANGE: "+12 this week",
  DOCTORS_CHANGE: "+3 this month",
  PATIENTS_CHANGE: "+25 this month",
  LOADING_TEXT: "Loading...",
  ERROR_FETCHING: "Error fetching dashboard stats:",
};

export const DASHBOARD_STATS_ICONS = {
  CLINICS: "Building2",
  APPOINTMENTS: "Calendar", 
  DOCTORS: "UserCheck",
  PATIENTS: "Users",
};

export const DASHBOARD_STATS_COLORS = {
  CLINICS: "text-blue-600",
  APPOINTMENTS: "text-green-600",
  DOCTORS: "text-purple-600", 
  PATIENTS: "text-orange-600",
};

export const DASHBOARD_RECENT_ACTIVITY = [
  {
    type: "appointment",
    patient: "Sarah Wilson",
    action: "Appointment booked with Dr. Smith",
    time: "2 minutes ago",
    status: "success",
  },
  {
    type: "call",
    patient: "Michael Chen",
    action: "Reactivation call completed - Interested",
    time: "5 minutes ago",
    status: "success",
  },
  {
    type: "missed",
    patient: "Emma Davis",
    action: "Missed appointment reminder sent",
    time: "12 minutes ago",
    status: "warning",
  },
  {
    type: "call",
    patient: "Robert Johnson",
    action: "Follow-up call scheduled",
    time: "18 minutes ago",
    status: "info",
  },
];

export const DASHBOARD_SCHEDULE = [
  {
    doctor: "Dr. Sarah Johnson",
    appointments: "8 appointments scheduled",
    time: "9:00 AM - 5:00 PM",
    percent: "87% booked",
    color: "blue",
  },
  {
    doctor: "Dr. Michael Brown",
    appointments: "6 appointments scheduled",
    time: "10:00 AM - 4:00 PM",
    percent: "75% booked",
    color: "green",
  },
];

export const DASHBOARD_ALERTS = [
  {
    icon: "AlertTriangle",
    title: "3 missed calls require follow-up",
    message: "AI agent will retry in 2 hours",
    color: "yellow",
  },
  {
    icon: "Calendar",
    title: "5 appointment confirmations needed",
    message: "Scheduled for tomorrow",
    color: "blue",
  },
];

export const DASHBOARD_TITLE = "Dashboard";
export const DASHBOARD_LAST_UPDATED = "Last updated:";
export const DASHBOARD_SYSTEM_ALERTS_TITLE = "System Alerts & Notifications";
export const DASHBOARD_SCHEDULE_OVERVIEW_TITLE = "Upcoming Appointments Overview";
export const DASHBOARD_RECENT_AI_ACTIVITY_TITLE = "Clinic Activity";

// DashboardTotalCalls component constants
export const DASHBOARD_TOTAL_CALLS = {
  TITLE: "Total Calls",
  SUCCESS_RATE_LABEL: "summary",
  REFRESH_TOOLTIP: "Refresh data",
  ERROR_FETCHING: "Error fetching AI call logs:",
  DATA_LABELS: {
    TOTAL_CALLS: "Total Calls",
    TOTAL_MINUTE_USE: "Total minute use",
    AVG_CALL_DURATION: "Avg. call duration"
  },
  UNITS: {
    MINUTES: " min"
  },
  COLORS: {
    TOTAL_CALLS: "#3B82F6",      // Blue
    TOTAL_MINUTE_USE: "#10B981", // Green
    AVG_CALL_DURATION: "#8B5CF6" // Purple
  }
};

// DashboardSchedule component constants
export const DASHBOARD_SCHEDULE_COMPONENT = {
  TITLE: "Today's Schedule",
  REFRESH_TOOLTIP: "Refresh data",
  ERROR_FETCHING: "Error fetching schedule data:",
  TIME_PERIOD: "Week",
  TIME_SLOTS: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00']
};

// DashboardPatientsOverview component constants
export const DASHBOARD_PATIENTS_OVERVIEW = {
  TITLE: "Patients Overview",
  REFRESH_TOOLTIP: "Refresh data",
  ERROR_FETCHING: "Error fetching patient data:",
  TIME_PERIOD: "Week",
  LEGEND: {
    PATIENT_ENTERS: "Patient enters",
    PATIENT_OUT: "Patient is out"
  },
  CHART_COLORS: {
    ENTERS: "#3b82f6", // Blue
    EXITS: "#10b981"   // Green
  }
};

// DashboardAppointments component constants
export const DASHBOARD_APPOINTMENTS = {
  TITLE: "Appointments",
  REFRESH_TOOLTIP: "Refresh data",
  NO_APPOINTMENTS: "No upcoming appointments",
  APPOINTMENT_DETAIL: "Appt with {doctor} {status}.",
  UNKNOWN_PATIENT: "Unknown Patient",
  UNKNOWN_DOCTOR: "Dr. Unknown",
  DATE_DISPLAY: {
    TODAY: "Today",
    TOMORROW: "Tomorrow"
  }
};
