// DetailedDoctorCard.tsx
// Renders a detailed card for a doctor, showing name, specialization, contact info, clinic, schedule, and actions.
// Used in the Doctor Directory for viewing and managing doctor profiles.

import React from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

import { Phone, Mail, MapPin, Clock, Calendar} from "lucide-react";

/**
 * Props for the DetailedDoctorCard component
 * @property name - Doctor's name
 * @property specialization - Doctor's specialization
 * @property statusBadge - Status badge (e.g., Available)
 * @property phone - Doctor's phone number
 * @property email - Doctor's email address
 * @property clinic - Clinic name
 * @property consultation - Consultation hours
 * @property workingDays - Working days
 * @property experience - Years of experience
 * @property patients - Number of patients
 * @property nextAvailable - Next available slot/info
 * @property onViewProfile - Handler for view profile button
 * @property onEdit - Handler for edit button
 */
interface DetailedDoctorCardProps {
  name: string;
  specialization: string;
  statusBadge: React.ReactNode;
  phone: string;
  email: string;
  clinic: string;
  consultation: string;
  workingDays: string;
  experience: string;
  patients: number;
  nextAvailable: React.ReactNode;
  qualification?: string; // Add qualification prop
  onViewProfile?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
}

/**
 * Renders a detailed card for a doctor, showing all key information and actions.
 * Used in the Doctor Directory for profile management.
 */
const DetailedDoctorCard: React.FC<DetailedDoctorCardProps> = ({
  name,
  specialization,
  statusBadge,
  phone,
  email,
  clinic,
  consultation,
  workingDays,
  experience,
  patients,
  nextAvailable,
  qualification, // Add qualification prop
}) => (
  <Card className="w-full hover:shadow-lg transition-shadow">
    <CardHeader className="pb-4">
      <div className="flex items-start justify-between">
        <div>
          <CardTitle className="text-xl text-gray-900">{name}</CardTitle>
          <div className="text-blue-600 font-medium cursor-pointer">
            {specialization}
          </div>
          {/* Render dynamic qualification if provided */}
          {qualification && (
            <div className="text-sm text-gray-600">
              {qualification}
            </div>
          )}
        </div>
        {statusBadge}
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-600">
            <Phone className="h-4 w-4 mr-2" />
            {phone}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-4 w-4 mr-2" />
            {email}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-2" />
            {clinic}
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="h-4 w-4 mr-2" />
            {consultation}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Calendar className="h-4 w-4 mr-2" />
            {workingDays}
          </div>
          <div className="text-sm">
            <span className="text-gray-600">
              Experience:{" "}
            </span>
            <span className="font-medium">{experience}</span>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between pt-4 mt-2 border-t border-gray-200">
        <div className="text-sm">
          <span className="text-gray-600">Patients: </span>
          <span className="font-semibold text-gray-900">{patients}</span>
        </div>
        <div className="text-sm">
          <span className="text-gray-600">
            Next Available:{" "}
          </span>
          <span className="font-medium text-green-600">{nextAvailable}</span>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default DetailedDoctorCard;
