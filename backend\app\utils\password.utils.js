import bcrypt from 'bcryptjs';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import {
  SYMBOLS,
  UPPERCASE,
  LOWERCASE,
  NUMBERS,
} from './constants.utils.js';

/**
 * Hashes a password using bcrypt
 * @param {string} password - Plain text password to hash
 * @returns {Promise<string>} - Resolves with the hashed password
 * <AUTHOR>
 */
export const hashPassword = async (password) => {
  const saltRounds = 8;

  try {
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    logger.info(loggerMessages.PASSWORD_HASHED_SUCCESSFULLY);
    return hashedPassword;
  } catch (error) {
    logger.error(loggerMessages.ERROR_HASHING_PASSWORD, error);
    throw new Error(loggerMessages.ERROR_HASHING_PASSWORD);
  }
};

/**
 * Compares a plain text password with a hashed password
 * @param {string} plainPassword - Plain text password
 * @param {string} hashedPassword - Hashed password
 * @returns {Promise<boolean>} - Resolves true if passwords match, false otherwise
 * <AUTHOR>
 */
export const comparePasswords = async (plainPassword, hashedPassword) => {
  try {
    const match = await bcrypt.compare(plainPassword, hashedPassword);
    logger.info(loggerMessages.PASSWORD_COMPARISON_SUCCESSFUL);
    return match;
  } catch (error) {
    logger.error(loggerMessages.ERROR_COMPARING_PASSWORDS, error);
    throw new Error(loggerMessages.ERROR_COMPARING_PASSWORDS);
  }
};

/**
 * Generates a secure random 8-15 character password matching PASSWORD_REGEX
 * @returns {string} - password as a string
 */
export const generateEightDigitPassword = async () => {
  const crypto = await import('crypto');
  const uppercase = UPPERCASE;
  const lowercase = LOWERCASE;
  const numbers = NUMBERS;
  const symbols = SYMBOLS;

  // Ensure at least one character from each category
  let passwordArray = [
    uppercase[crypto.randomInt(0, uppercase.length)],
    lowercase[crypto.randomInt(0, lowercase.length)],
    numbers[crypto.randomInt(0, numbers.length)],
    symbols[crypto.randomInt(0, symbols.length)],
  ];

  // Fill remaining characters randomly from all categories
  const allChars = uppercase + lowercase + numbers + symbols;
  const length = crypto.randomInt(8, 16); // 8 to 15 inclusive
  while (passwordArray.length < length) {
    passwordArray.push(allChars[crypto.randomInt(0, allChars.length)]);
  }

  // Shuffle the password array securely
  for (let i = passwordArray.length - 1; i > 0; i--) {
    const j = crypto.randomInt(0, i + 1);
    [passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]];
  }
  return passwordArray.join('');
};
