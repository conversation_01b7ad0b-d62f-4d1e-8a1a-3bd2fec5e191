import bcrypt from 'bcryptjs';

const superAdmin = {
  name: 'admin',
  email: '<EMAIL>',
  password_hash: bcrypt.hashSync('admin@2025', 10),
  user_phonenumber: '1234567899',
  role_id: null, // Will be set to the super_admin role ID
  created_at: new Date(),
  updated_at: new Date(),
  is_deleted: false,
  is_active: true,
};

export async function up(queryInterface, Sequelize) {
  // First, get the super_admin role ID
  const roles = await queryInterface.sequelize.query(
    'SELECT id FROM roles WHERE "roleName" = ?',
    {
      replacements: ['super_admin'],
      type: Sequelize.QueryTypes.SELECT,
    }
  );

  if (roles.length > 0) {
    superAdmin.role_id = roles[0].id;
  }

  await queryInterface.bulkInsert('users', [superAdmin]);
}

export async function down(queryInterface, Sequelize) {
  await queryInterface.bulkDelete('users', null);
}
