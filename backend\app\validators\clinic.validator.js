/**
 * Clinic Validators: express-validator schemas for clinic create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating a clinic
// - clinic_name: required
// - timezone: required
// - clinic_email: optional, must be a valid email if present
// - clinic_phonenumber: optional, must be a string if present
export const createClinicSchema = [
  body('clinic_name').notEmpty().withMessage(constants.CLINIC_NAME_REQUIRED),
  body('timezone').notEmpty().withMessage(constants.CLINIC_TIMEZONE_REQUIRED),
  body('clinic_email')
    .optional()
    .isEmail()
    .withMessage(constants.INVALID_CLINIC_EMAIL),
  body('clinic_phonenumber')
    .optional()
    .isString()
    .withMessage(constants.INVALID_CLINIC_PHONENUMBER),
  body('working_hours')
    .optional()
    .isString()
    .withMessage(constants.WORKING_HOURS_STRING),
  body('working_days')
    .optional()
    .isString()
    .withMessage(constants.WORKING_DAYS_STRING),
];

// Validation schema for updating a clinic
// - clinic_name: optional, cannot be empty if present
// - timezone: optional, cannot be empty if present
// - location: optional
// - is_active: optional, must be boolean
// - clinic_email: optional, must be a valid email if present
// - clinic_phonenumber: optional, must be a string if present
export const updateClinicSchema = [
  body('clinic_name')
    .optional()
    .notEmpty()
    .withMessage(constants.CLINIC_NAME_CANNOT_BE_EMPTY),
  body('timezone')
    .optional()
    .notEmpty()
    .withMessage(constants.CLINIC_TIMEZONE_CANNOT_BE_EMPTY),
  body('location').optional(),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage(constants.CLINIC_IS_ACTIVE_BOOLEAN),
  body('clinic_email')
    .optional()
    .isEmail()
    .withMessage(constants.INVALID_CLINIC_EMAIL),
  body('clinic_phonenumber')
    .optional()
    .isString()
    .withMessage(constants.INVALID_CLINIC_PHONENUMBER),
  body('working_hours')
    .optional()
    .isString()
    .withMessage(constants.WORKING_HOURS_STRING),
  body('working_days')
    .optional()
    .isString()
    .withMessage(constants.WORKING_DAYS_STRING),
];

export const updateClinicReactivitationSchema = [
  body('reactivation_days')
    .notEmpty()
    .isInt({ min: 0 })
    .withMessage(constants.CLINIC_REACTIVATION_DAYS_CANNOT_BE_EMPTY),

  body('batch_call_time')
    .notEmpty()
    .matches(/^([01]\d|2[0-3]):([0-5]\d)$/) // Matches HH:mm format (e.g. 10:00, 23:59)
    .withMessage(constants.REACTIVATION_TIME_CANNOT_BE_EMPTY),
];
