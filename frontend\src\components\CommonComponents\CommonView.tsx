import React from "react";
import { formatDateForDisplay } from "@/utils/dateUtils";

interface CommonViewProps {
  open: boolean;
  onClose: () => void;
  data: Record<string, unknown>;
  title?: string;
}

const CommonView: React.FC<CommonViewProps> = ({
  open,
  onClose,
  data,
  title,
}) => {
  if (!open) return null;

  // Function to check if a field is a date field
  const isDateField = (key: string, value: unknown): boolean => {
    const dateKeywords = ['date', 'dob', 'birth', 'created', 'updated', 'visit', 'appointment'];
    const keyLower = key.toLowerCase();
    return dateKeywords.some(keyword => keyLower.includes(keyword)) && 
           typeof value === 'string' && 
           value.trim() !== '';
  };

  // Function to format the display value
  const formatDisplayValue = (key: string, value: unknown): string => {
    if (value === null || value === undefined) {
      return '—';
    }
    
    if (typeof value === "object") {
      return JSON.stringify(value);
    }
    
    const stringValue = String(value);
    
    // Check if this is a date field and format accordingly
    if (isDateField(key, stringValue)) {
      const formattedDate = formatDateForDisplay(stringValue, 'DD/MM/YYYY');
      return formattedDate || stringValue;
    }
    
    return stringValue;
  };

  return (
    <div className="fixed inset-0 h-screen w-screen bg-black/30 flex justify-center items-center z-[9999]">
      <div className="bg-white rounded-2xl w-[90vw] max-w-[650px] max-h-[90vh] p-8 md:p-10 shadow-2xl relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-5 bg-gray-100 border-none rounded-full w-9 h-9 text-[22px] font-bold text-gray-800 cursor-pointer flex items-center justify-center hover:bg-gray-200 transition"
          aria-label="Close"
        >
          ×
        </button>

        <h2 className="text-2xl font-semibold mb-7 text-[#111]">
          {title || "Patient Details"}
        </h2>

        <div className="overflow-y-auto pr-5 max-h-[calc(90vh-120px)] grid grid-cols-1 md:grid-cols-2 gap-5">
          {Object.entries(data).map(([key, value]) => (
            <div
              key={key}
              className="bg-gray-50 rounded-xl p-4 shadow-sm flex flex-col"
            >
              <div className="text-xs text-gray-500 mb-1 uppercase tracking-wider font-medium">
                {formatKey(key)}
              </div>
              <div className="text-base font-semibold text-[#111] break-words">
                {formatDisplayValue(key, value)}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const formatKey = (key: string) =>
  key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

export default CommonView;
