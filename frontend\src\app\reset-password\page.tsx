"use client";
import React, { useState, Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Toaster } from "@/components/ui/toaster";
import { Lock, Eye, EyeOff, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/components/contexts/AuthContext";
import {
  RESET_PASSWORD_CONSTANTS,
  VALIDATION_MESSAGES,
  TOAST_MESSAGES,
  FORM_FIELDS,
  FORM_IDS,
  ACCESSIBILITY,
  ASSETS,
  ANIMATION,
  INPUT_TYPES,
  BUTTON_TYPES,
  FORM_ATTRIBUTES,
  ROUTES,
} from "@/Constants/ResetPassword";
// import { ERROR_MESSAGES } from "@/Constants/HooksAPI";
import styles from "./ResetPassword.module.css";
import { toastSuccess, toastError } from "@/hooks/use-toast";

const resetPasswordSchema = z
  .object({
    [FORM_FIELDS.NEW_PASSWORD]: z
      .string()
      .min(8, { message: VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH })
      .regex(/[A-Z]/, { message: VALIDATION_MESSAGES.PASSWORD_UPPERCASE })
      .regex(/[a-z]/, { message: VALIDATION_MESSAGES.PASSWORD_LOWERCASE })
      .regex(/[0-9]/, { message: VALIDATION_MESSAGES.PASSWORD_NUMBER })
      .regex(/[!@#$%^&*]/, { message: VALIDATION_MESSAGES.PASSWORD_SPECIAL }),
    [FORM_FIELDS.CONFIRM_PASSWORD]: z
      .string()
      .min(1, { message: VALIDATION_MESSAGES.CONFIRM_PASSWORD_REQUIRED }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: VALIDATION_MESSAGES.PASSWORDS_MATCH,
    path: [FORM_FIELDS.CONFIRM_PASSWORD],
  });

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

// Create a separate component that uses useSearchParams
function ResetPasswordForm() {
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState("");
  const { resetPassword, loading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  // Password validation checks
  const passwordChecks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[!@#$%^&*]/.test(password),
  };

  // const allChecksPassed = Object.values(passwordChecks).every(Boolean);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });

  // Watch password field for real-time validation
  const watchedPassword = watch(FORM_FIELDS.NEW_PASSWORD);

  // Update password state when watched password changes
  React.useEffect(() => {
    setPassword(watchedPassword || "");
  }, [watchedPassword]);

  // Password strength indicator component
  const PasswordStrengthIndicator = () => {
    if (!password) return null;

    return (
      <div className={styles.passwordStrength}>
        <div className={styles.passwordStrengthTitle}>
          Password Requirements:
        </div>
        <div className={styles.passwordChecks}>
          <div
            className={`${styles.passwordCheck} ${
              passwordChecks.length ? styles.valid : styles.invalid
            }`}
          >
            <span className={styles.checkIcon}>
              {passwordChecks.length ? "✓" : "✗"}
            </span>
            At least 8 characters
          </div>
          <div
            className={`${styles.passwordCheck} ${
              passwordChecks.uppercase ? styles.valid : styles.invalid
            }`}
          >
            <span className={styles.checkIcon}>
              {passwordChecks.uppercase ? "✓" : "✗"}
            </span>
            One uppercase letter (A-Z)
          </div>
          <div
            className={`${styles.passwordCheck} ${
              passwordChecks.lowercase ? styles.valid : styles.invalid
            }`}
          >
            <span className={styles.checkIcon}>
              {passwordChecks.lowercase ? "✓" : "✗"}
            </span>
            One lowercase letter (a-z)
          </div>
          <div
            className={`${styles.passwordCheck} ${
              passwordChecks.number ? styles.valid : styles.invalid
            }`}
          >
            <span className={styles.checkIcon}>
              {passwordChecks.number ? "✓" : "✗"}
            </span>
            One number (0-9)
          </div>
          <div
            className={`${styles.passwordCheck} ${
              passwordChecks.special ? styles.valid : styles.invalid
            }`}
          >
            <span className={styles.checkIcon}>
              {passwordChecks.special ? "✓" : "✗"}
            </span>
            One special character (!@#$%^&*)
          </div>
        </div>
      </div>
    );
  };

  const onSubmit = async (data: ResetPasswordFormData) => {
    if (!token) {
      toastError("Invalid reset token. Please request a new password reset.");
      return;
    }

    try {
      const result = await resetPassword({
        resetToken: token,
        newPassword: data[FORM_FIELDS.NEW_PASSWORD],
        confirmPassword: data[FORM_FIELDS.CONFIRM_PASSWORD],
      });

      if (result.success) {
        toastSuccess(TOAST_MESSAGES.RESET_SUCCESS);
        router.push(ROUTES.LOGIN);
      } else {
        toastError(result.error || TOAST_MESSAGES.RESET_FAILED);
      }
    } catch {
      toastError(TOAST_MESSAGES.RESET_FAILED);
    }
  };

  if (!token) {
    return (
      <div className={styles.changeContainer}>
        <Toaster />
        <motion.div
          initial={{
            opacity: ANIMATION.INITIAL_OPACITY,
            scale: ANIMATION.INITIAL_SCALE,
          }}
          animate={{
            opacity: ANIMATION.FINAL_OPACITY,
            scale: ANIMATION.FINAL_SCALE,
          }}
          transition={{ duration: ANIMATION.DURATION }}
          className={styles.changeMotion}
        >
          <Card className={styles.changeCard}>
            <CardHeader className={styles.changeHeader}>
              <Image
                src={ASSETS.LOGO_SRC}
                alt={ACCESSIBILITY.DENSY_AI_LOGO}
                width={ASSETS.LOGO_WIDTH}
                height={ASSETS.LOGO_HEIGHT}
                className={styles.changeLogo}
                priority
              />
              <h1 className={styles.changeTitle}>Invalid Reset Link</h1>
              <p className={styles.changeSubtitle}>
                This password reset link is invalid or has expired.
              </p>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <Link href={ROUTES.LOGIN}>
                  <Button className={styles.changeButton}>
                    Return to Login
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={styles.changeContainer}>
      <Toaster />
      <motion.div
        initial={{
          opacity: ANIMATION.INITIAL_OPACITY,
          scale: ANIMATION.INITIAL_SCALE,
        }}
        animate={{
          opacity: ANIMATION.FINAL_OPACITY,
          scale: ANIMATION.FINAL_SCALE,
        }}
        transition={{ duration: ANIMATION.DURATION }}
        className={styles.changeMotion}
      >
        <Card className={styles.changeCard}>
          <CardHeader className={styles.changeHeader}>
            <Image
              src={ASSETS.LOGO_SRC}
              alt={ACCESSIBILITY.DENSY_AI_LOGO}
              width={ASSETS.LOGO_WIDTH}
              height={ASSETS.LOGO_HEIGHT}
              className={styles.changeLogo}
              priority
            />
            <h1 className={styles.changeTitle}>Reset Password</h1>
            <p className={styles.changeSubtitle}>
              {RESET_PASSWORD_CONSTANTS.SUBTITLE}
            </p>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={handleSubmit(onSubmit)}
              className={styles.changeForm}
            >
              <div>
                <Label
                  htmlFor={FORM_IDS.NEW_PASSWORD}
                  className="text-sm font-medium"
                >
                  {RESET_PASSWORD_CONSTANTS.NEW_PASSWORD_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.changeIcon}>
                    <Lock size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.NEW_PASSWORD}
                    type={
                      showNewPassword ? INPUT_TYPES.TEXT : INPUT_TYPES.PASSWORD
                    }
                    aria-label="New password"
                    placeholder={
                      RESET_PASSWORD_CONSTANTS.NEW_PASSWORD_PLACEHOLDER
                    }
                    autoComplete={FORM_ATTRIBUTES.NEW_PASSWORD_AUTOCOMPLETE}
                    {...register(FORM_FIELDS.NEW_PASSWORD)}
                    className={styles.changeInput}
                  />
                  <button
                    type={BUTTON_TYPES.BUTTON}
                    aria-label={
                      showNewPassword
                        ? ACCESSIBILITY.HIDE_PASSWORD
                        : ACCESSIBILITY.SHOW_PASSWORD
                    }
                    className={styles.changePasswordToggle}
                    onClick={() => setShowNewPassword((v) => !v)}
                    tabIndex={-1}
                  >
                    {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors[FORM_FIELDS.NEW_PASSWORD] && (
                  <span className={styles.changeError}>
                    {errors[FORM_FIELDS.NEW_PASSWORD]?.message}
                  </span>
                )}
                <PasswordStrengthIndicator />
              </div>
              <div>
                <Label
                  htmlFor={FORM_IDS.CONFIRM_PASSWORD}
                  className="text-sm font-medium"
                >
                  {RESET_PASSWORD_CONSTANTS.CONFIRM_PASSWORD_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.changeIcon}>
                    <Lock size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.CONFIRM_PASSWORD}
                    type={
                      showConfirmPassword
                        ? INPUT_TYPES.TEXT
                        : INPUT_TYPES.PASSWORD
                    }
                    aria-label="Confirm password"
                    placeholder={
                      RESET_PASSWORD_CONSTANTS.CONFIRM_PASSWORD_PLACEHOLDER
                    }
                    autoComplete={FORM_ATTRIBUTES.CONFIRM_PASSWORD_AUTOCOMPLETE}
                    {...register(FORM_FIELDS.CONFIRM_PASSWORD)}
                    className={styles.changeInput}
                  />
                  <button
                    type={BUTTON_TYPES.BUTTON}
                    aria-label={
                      showConfirmPassword
                        ? ACCESSIBILITY.HIDE_PASSWORD
                        : ACCESSIBILITY.SHOW_PASSWORD
                    }
                    className={styles.changePasswordToggle}
                    onClick={() => setShowConfirmPassword((v) => !v)}
                    tabIndex={-1}
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} />
                    ) : (
                      <Eye size={18} />
                    )}
                  </button>
                </div>
                {errors[FORM_FIELDS.CONFIRM_PASSWORD] && (
                  <span className={styles.changeError}>
                    {errors[FORM_FIELDS.CONFIRM_PASSWORD]?.message}
                  </span>
                )}
              </div>
              <Button
                type={BUTTON_TYPES.SUBMIT}
                className={styles.changeButton}
                disabled={isSubmitting || loading}
                aria-label={ACCESSIBILITY.RESET_PASSWORD}
              >
                {isSubmitting || loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={18} />
                    {RESET_PASSWORD_CONSTANTS.LOADING}
                  </span>
                ) : (
                  RESET_PASSWORD_CONSTANTS.RESET_PASSWORD
                )}
              </Button>
            </form>
            <div className={styles.changeBackToLogin}>
              <span>
                Remember your password?{" "}
                <Link
                  href={ROUTES.LOGIN}
                  className={styles.changeBackToLoginLink}
                >
                  Back to Login
                </Link>
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}

// Main component with Suspense boundary
export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className={styles.changeContainer}>
        <div className={styles.changeMotion}>
          <Card className={styles.changeCard}>
            <CardHeader className={styles.changeHeader}>
              <Image
                src={ASSETS.LOGO_SRC}
                alt={ACCESSIBILITY.DENSY_AI_LOGO}
                width={ASSETS.LOGO_WIDTH}
                height={ASSETS.LOGO_HEIGHT}
                className={styles.changeLogo}
                priority
              />
              <h1 className={styles.changeTitle}>Loading...</h1>
            </CardHeader>
          </Card>
        </div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}
