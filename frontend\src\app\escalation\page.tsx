"use client";

import React, { useState, useEffect } from "react";
import { AlertTriangle } from "lucide-react";
import { EscalationList } from "@/components/Escalation";
import PageSection from "@/components/CommonComponents/PageSection";
import { useEscalation } from "@/hooks/useEscalation";
import { EscalationData } from "@/hooks/useEscalation";
import { usePatients, Patient } from "@/hooks/usePatients";
import { useUser, User } from "@/hooks/useUser";

import {
  ESCALATION_TITLE,
  ESCALATION_SUBTITLE,
} from "@/Constants/Escalation";

const EscalationPage = () => {
  const [escalations, setEscalations] = useState<EscalationData[]>([]);
  const [loading, setLoading] = useState(true);
  
  const { getEscalations } = useEscalation();
  const patientsHook = usePatients();
  const { getAllUsers } = useUser();

  const fetchEscalations = async () => {
    try {
      setLoading(true);
      
      // Fetch escalations
      const result = await getEscalations({ is_active: true, is_deleted: false });
      
      if (result.success && result.data) {
        const escalationData = Array.isArray(result.data) ? result.data : [];
        
        // Fetch patients and users to get names
        const [patientsResult, usersResult] = await Promise.all([
          patientsHook.getAllPatients(),
          getAllUsers()
        ]);
        
        // Create lookup maps for patients and users
        const patientsMap = new Map();
        const usersMap = new Map();
        
        if (patientsResult.success && patientsResult.data) {
          const patients = Array.isArray(patientsResult.data) ? patientsResult.data : [];
          patients.forEach((patient: Patient) => {
            patientsMap.set(patient.id, {
              name: `${patient.first_name} ${patient.last_name}`.trim(),
              phone: patient.phone_number
            });
          });
        }
        
        if (usersResult.success && usersResult.data) {
          const users = Array.isArray(usersResult.data) ? usersResult.data : [];
          users.forEach((user: User) => {
            usersMap.set(user.id, user.name);
          });
        }
        
        // Enhance escalation data with names
        const enhancedEscalations = escalationData.map((escalation: EscalationData) => ({
          ...escalation,
          patient_name: patientsMap.get(escalation.patient_id)?.name || `Patient ID: ${escalation.patient_id}`,
          patient_phone: patientsMap.get(escalation.patient_id)?.phone || "Phone not available",
          assignee_name: usersMap.get(escalation.assignee_id) || `Assignee ID: ${escalation.assignee_id}`
        }));
        
        setEscalations(enhancedEscalations);
      }
      // No need to handle errors here - the hooks will show toast messages
    } catch (err) {
      // No need to handle errors here - the hooks will show toast messages
      console.error("Error fetching escalations:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEscalations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <PageSection>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{ESCALATION_TITLE}</h1>
            <p className="text-gray-600">{ESCALATION_SUBTITLE}</p>
          </div>
        </div>
      </div>

      {/* Escalation List */}
      <EscalationList escalations={escalations} loading={loading} />
    </PageSection>
  );
};

export default EscalationPage; 