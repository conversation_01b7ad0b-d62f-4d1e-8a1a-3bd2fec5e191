import express from 'express';
import { PROTECTED_ROUTE_SUCCESS } from '../utils/constants.utils.js';
import { validate } from '../middleware/validate.middleware.js';
import { signUpSchema, loginSchema, resetPasswordSchema, changePasswordSchema } from '../validators/auth.validator.js';
import * as authController from '../controllers/auth.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Refresh access token
router.post('/refresh', authController.refreshToken);

// Sample protected route
router.get('/protected', verifyToken, (req, res) => {
  res.json({ message: PROTECTED_ROUTE_SUCCESS, user: req.user });
});

// Register a new user
router.post('/signup', validate(signUpSchema), authController.signUp);

// Login user
router.post('/login', validate(loginSchema), authController.login);

// Forgot password
router.post('/forgot-password', authController.forgotPassword);

// Reset Password with resetToken as URL param
router.post('/reset-password/:resetToken', validate(resetPasswordSchema), authController.resetPassword);

// Change Password (authenticated user)
router.post('/change-password', verifyToken, validate(changePasswordSchema), authController.changePassword);

// Get User Profile
router.get('/profile', verifyToken, authController.getProfile);

// Update User Profile
router.put('/profile/:id', verifyToken, authController.updateProfile);

// Logout user
router.post('/logout', authController.logout);

export default router;
