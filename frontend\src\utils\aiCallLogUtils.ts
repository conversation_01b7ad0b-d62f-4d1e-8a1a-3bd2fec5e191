import { AICallLog } from "@/hooks/useAICallLog";
import { formatPhoneNumber } from "./commonFunctions";

export interface TransformedCallLog {
  id: number;
  type: "incoming" | "outgoing";
  patient: string;
  phone: string;
  time: string;
  duration: string;
  status: string;
  outcome: string;
  transcript: string; // Keep as string for backward compatibility
  transcriptMessages: TranscriptMessage[]; // New field for parsed transcript messages
  tags: string[];
  sentiment: string;
}

export interface TranscriptMessage {
  role: string;
  message: string;
  time_in_call_secs: number;
}

/**
 * Parses and filters transcript messages from the transcript array
 * @param transcript - Array of transcript strings
 * @returns Parsed and filtered transcript messages
 */
export const parseTranscriptMessages = (transcript: string[] | undefined): TranscriptMessage[] => {
  if (!transcript || !Array.isArray(transcript)) {
    return [];
  }
  
  return transcript
    .map((item: string): TranscriptMessage | null => {
      try {
        const parsed = JSON.parse(item) as Record<string, unknown>;
        const message = parsed.message as string;
        
        // Filter out empty messages or messages with only whitespace
        if (!message || message.trim().length === 0) {
          return null;
        }
        
        return {
          role: (parsed.role as string) || "unknown",
          message: message.trim(),
          time_in_call_secs: (parsed.time_in_call_secs as number) || 0,
        };
      } catch {
        return null;
      }
    })
    .filter((msg): msg is TranscriptMessage => msg !== null);
};

export const transformAICallLogToComponentFormat = (apiCallLog: AICallLog): TransformedCallLog => {
  // Convert direction to type
  const type: "incoming" | "outgoing" = apiCallLog.direction === "inbound" ? "incoming" : "outgoing";
  
  // Format patient name
  const patient = apiCallLog.patient 
    ? `${apiCallLog.patient.first_name} ${apiCallLog.patient.last_name}`
    : "Unknown Patient";
  
  // Format phone number
  const phone = apiCallLog.patient?.phone_number || apiCallLog.phone_number || "N/A";
  const formattedPhone = phone !== "N/A" ? formatPhoneNumber(phone) : "N/A";
  
  // Format date and time
  const callDate = new Date(apiCallLog.call_time_date);
  const date = callDate.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric',
    year: 'numeric'
  });
  const time = callDate.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: true 
  });
  const dateTime = `${date} ${time}`;
  
  // Format duration (convert seconds to MM:SS format)
  const minutes = Math.floor(apiCallLog.call_duration / 60);
  const seconds = apiCallLog.call_duration % 60;
  const duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
  
  // Map status
  const statusMap: Record<string, string> = {
    'successful': 'Completed',
    'failed': 'Missed Connection',
    'in_progress': 'In Progress'
  };
  const status = statusMap[apiCallLog.call_status] || 'Unknown';
  
  // Use call summary title as outcome
  const outcome = apiCallLog.call_summary_title || 'No Outcome';
  
  // Use conversation summary as transcript
  const transcript = apiCallLog.conversation_summary || 'No transcript available';
  
  // Parse transcript messages
  const transcriptMessages = parseTranscriptMessages(apiCallLog.transcript);
  
  // Generate tags based on call data
  const tags: string[] = [];
  if (apiCallLog.call_status === 'successful') tags.push('successful');
  if (apiCallLog.direction === 'outbound') tags.push('outbound');
  if (apiCallLog.direction === 'inbound') tags.push('inbound');
  if (apiCallLog.conversation_summary?.toLowerCase().includes('appointment')) tags.push('appointment');
  if (apiCallLog.conversation_summary?.toLowerCase().includes('reactivation')) tags.push('reactivation');
  
  // Determine sentiment based on call status and content
  let sentiment = 'neutral';
  if (apiCallLog.call_status === 'successful') {
    sentiment = 'positive';
  } else if (apiCallLog.call_status === 'failed') {
    sentiment = 'negative';
  }
  
  const result = {
    id: apiCallLog.id,
    type,
    patient,
    phone: formattedPhone,
    time: dateTime,
    duration,
    status,
    outcome,
    transcript,
    transcriptMessages,
    tags,
    sentiment,
  };
  
  return result;
};

export const transformAICallLogsToComponentFormat = (apiCallLogs: AICallLog[]): TransformedCallLog[] => {
  return apiCallLogs.map(transformAICallLogToComponentFormat);
}; 