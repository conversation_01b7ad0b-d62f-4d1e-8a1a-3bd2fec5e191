// DashboardStatsGrid.tsx
// Renders a grid of StatCard components to display key statistics on the dashboard.
// Each stat includes a title, value, and icon.

import React from "react";
import StatCard from "../CommonComponents/StatCard";
import { Calendar, Phone, RefreshCw, User, Users, TrendingUp, Clock } from "lucide-react";

// Maps icon names to React nodes
const ICONS: Record<string, React.ReactNode> = {
  Calendar: <Calendar className="h-8 w-8 text-blue-600" />,
  Phone: <Phone className="h-8 w-8 text-green-600" />,
  RefreshCw: <RefreshCw className="h-8 w-8 text-purple-600" />,
  User: <User className="h-8 w-8 text-blue-600" />,
  Users: <Users className="h-8 w-8 text-orange-600" />,
  TrendingUp: <TrendingUp className="h-8 w-8 text-green-600" />,
  Clock: <Clock className="h-8 w-8 text-purple-600" />,
};

/**
 * Represents a single statistic for the dashboard stats grid.
 * @property title - The title/label for the stat
 * @property value - The main value to display
 * @property icon - Icon name to display
 */
interface Stat {
  title: string;
  value: string | number;
  icon: string;
}

/**
 * Props for the DashboardStatsGrid component
 * @property stats - Array of stat objects to display
 */
interface DashboardStatsGridProps {
  stats: Stat[];
}

/**
 * Renders a grid of StatCard components for dashboard statistics.
 */
const DashboardStatsGrid: React.FC<DashboardStatsGridProps> = ({ stats }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    {stats.map((stat, index) => (
      <StatCard
        key={index}
        title={stat.title}
        value={stat.value}
        icon={ICONS[stat.icon]}
      />
    ))}
  </div>
);

export default DashboardStatsGrid;
