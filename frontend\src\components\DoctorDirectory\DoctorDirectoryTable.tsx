import React, { useState } from "react";
import Table from "@/components/CommonComponents/Table";
import Pagination from "@/components/CommonComponents/Pagination";
import { Eye, Edit, Trash2 } from "lucide-react";
import { doctorTableColumns } from '@/utils/column';
import ConfirmDeleteDialog from '@/components/CommonComponents/ConfirmDeleteDialog';

interface Doctor {
  id: number;
  doctor_name: string;
  specialization?: string;
  qualification?: string;
  clinic_id: number;
  doctor_phonenumber?: string;
  doctor_email?: string;
  experience_years?: number;
  is_deleted?: boolean;
  created_at?: string | number;
  consultationHours?: string;
  working_days?: string;
  patients?: number;
  [key: string]: unknown;
}

interface Clinic {
  id: number;
  clinic_name: string;
}

interface DoctorDirectoryTableProps {
  doctors: Doctor[];
  clinics: Clinic[];
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onViewDoctor: (doctor: Doctor) => void;
  onEditDoctor?: (doctor: Doctor) => void;
  onDeleteDoctor?: (doctor: Doctor) => void;
  variant?: 'striped' | 'hover' | 'bordered';
}

const DoctorDirectoryTable: React.FC<DoctorDirectoryTableProps> = ({
  doctors,
  clinics,
  page,
  pageSize,
  total,
  onPageChange,
  onViewDoctor,
  onEditDoctor,
  onDeleteDoctor,
  variant = 'striped',
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [doctorToDelete, setDoctorToDelete] = useState<Doctor | null>(null);

  // Add action render function
  const handleActions = (row: Record<string, unknown>) => {
    const doctor = row as unknown as Doctor;
    return (
      <div className="flex gap-3 items-center">
        <button
          className="text-black"
          onClick={() => onViewDoctor(doctor)}
          title="View"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Eye className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => onEditDoctor && onEditDoctor(doctor)}
          title="Edit"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Edit className="h-4 w-4" />
        </button>
        <button
          className="text-black"
          onClick={() => { setDoctorToDelete(doctor); setDeleteDialogOpen(true); }}
          title="Delete"
          style={{ background: 'none', border: 'none', padding: 0 }}
        >
          <Trash2 className="h-4 w-4" />
        </button>
      </div>
    );
  };

  return (
    <div>
      <Table
        columns={doctorTableColumns(clinics)}
        data={doctors as unknown as Record<string, unknown>[]}
        variant={variant}
        onActions={handleActions}
      />
      <div className="mt-4 flex justify-end">
        <Pagination
          currentPage={page}
          totalPages={Math.ceil(total / pageSize)}
          onPageChange={onPageChange}
        />
      </div>
      {/* Delete Confirmation Dialog */}
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Doctor"
        description={<span>Are you sure you want to delete <b>{doctorToDelete?.doctor_name}</b>? This action cannot be undone.</span>}
        onConfirm={() => { if (onDeleteDoctor && doctorToDelete) onDeleteDoctor(doctorToDelete); setDoctorToDelete(null); }}
      />
    </div>
  );
};

export default DoctorDirectoryTable; 