/**
 * Escalation Service: Handles business logic for escalation CRUD operations.
 * Manages database operations using Sequelize ORM and integrates with ElevenLabs API.
 * <AUTHOR>
 */
import { Op } from 'sequelize';
import { Escalation } from '../models/index.js';
import axios from 'axios';
import logger from '../config/logger.config.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';

/**
 * Fetch transcript from ElevenLabs API
 * @param {string} conversationId - ElevenLabs conversation ID
 * @param {string} apiKey - ElevenLabs API key
 * @returns {Promise<Array>} Transcript array with role and message
 * <AUTHOR>
 */
export const fetchTranscriptFromElevenLabs = async (conversationId, apiKey) => {
  try {
    const response = await axios.get(
      `https://api.elevenlabs.io/v1/convai/conversations/${conversationId}`,
      {
        headers: {
          'xi-api-key': api<PERSON><PERSON>,
        },
      }
    );

    if (response.data && response.data.transcript) {
      return response.data.transcript;
    }
    
    return [];
  } catch (error) {
    console.log(error,"transcript error");
    logger.error(`${logMessages.ERROR_FETCHING_TRANSCRIPT_ELEVENLABS}: ${error.message}`);
    throw new Error(`${constants.FAILED_TO_FETCH_TRANSCRIPT}: ${error.message}`);
  }
};

/**
 * Get all escalations, with optional filters
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of escalations
 * <AUTHOR>
 */
export const getAllEscalations = async (filters = {}) => {
  try {
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.clinic_id !== undefined) where.clinic_id = filters.clinic_id;
    // Query escalations
    const escalations = await Escalation.findAll({ 
      where,
      order: [['created_at', 'DESC']]
    });
    return escalations;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get an escalation by its ID
 * @param {number} id - Escalation ID
 * @returns {Promise<Object|null>} The escalation or null if not found
 * <AUTHOR>
 */
export const getEscalationById = async (id) => {
  try {
    const escalation = await Escalation.findByPk(id, {
      include: [
        {
          model: Escalation.sequelize.models.Clinic,
          as: 'Clinic',
          attributes: ['id', 'name']
        },
        {
          model: Escalation.sequelize.models.Patient,
          as: 'Patient',
          attributes: ['id', 'first_name', 'last_name', 'phone']
        },
        {
          model: Escalation.sequelize.models.User,
          as: 'Assignee',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    return escalation;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create a new escalation
 * @param {Object} escalationData - Data for new escalation
 * @returns {Promise<Object>} The created escalation
 * <AUTHOR>
 */
export const createEscalation = async (escalationData) => {
  try {
    const newEscalation = await Escalation.create(escalationData);
    return newEscalation;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update an escalation by its ID
 * @param {number} id - Escalation ID
 * @param {Object} escalationData - Data to update
 * @returns {Promise<Object|null>} The updated escalation or null if not found
 * <AUTHOR>
 */
export const updateEscalation = async (id, escalationData) => {
  try {
    await Escalation.update(escalationData, { where: { id } });
    const updatedEscalation = await Escalation.findByPk(id);
    return updatedEscalation;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Soft delete an escalation by its ID (sets is_deleted to true)
 * @param {number} id - Escalation ID
 * @returns {Promise<Object|null>} The deleted escalation or null if not found
 * <AUTHOR>
 */
export const deleteEscalation = async (id) => {
  try {
    const escalation = await Escalation.findByPk(id);
    if (!escalation) {
      return null;
    }
    
    await Escalation.update(
      { is_deleted: true, is_active: false },
      { where: { id } }
    );
    
    return await Escalation.findByPk(id);
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create escalation with transcript from ElevenLabs
 * @param {Object} escalationData - Data for new escalation
 * @param {string} conversationId - ElevenLabs conversation ID
 * @param {string} apiKey - ElevenLabs API key
 * @returns {Promise<Object>} The created escalation with transcript
 * <AUTHOR>
 */
export const createEscalationWithTranscript = async (escalationData, conversationId, apiKey) => {
  try {
    // Fetch transcript from ElevenLabs
    const transcript = await fetchTranscriptFromElevenLabs(conversationId, apiKey);
    
    // Add transcript to escalation data
    escalationData.transcript = transcript;   
    
    // Create the escalation
    const newEscalation = await Escalation.create(escalationData);
    return newEscalation;
  } catch (error) {
    throw new Error(error.message);
  }
};
