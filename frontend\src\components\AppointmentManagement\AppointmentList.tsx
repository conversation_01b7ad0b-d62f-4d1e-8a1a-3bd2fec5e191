import React from "react";
import { Appointment } from "@/hooks/useAppointment";
import AppointmentCard from "./AppointmentCard";

interface AppointmentListProps {
  appointments: Appointment[];
  onSelect: (appointment: Appointment) => void;
  onEdit: (appointment: Appointment) => void;
}

const AppointmentList: React.FC<AppointmentListProps> = ({
  appointments,
  onSelect,
  onEdit,
}) => {
  if (appointments.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No appointments found.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {appointments.map((appointment) => (
        <AppointmentCard
          key={appointment.id}
          appointment={appointment}
          onSelect={onSelect}
          onEdit={onEdit}
        />
      ))}
    </div>
  );
};

export default AppointmentList;
