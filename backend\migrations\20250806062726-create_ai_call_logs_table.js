import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable({ tableName: 'ai_call_logs'}, {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    direction: {
      type: Sequelize.TEXT,
      allowNull: false,
      validate: {
        isIn: [['inbound', 'outbound']],
      },
    },
    call_time_date: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
    },
    call_duration: {
      type: Sequelize.INTEGER, // Duration in seconds
      allowNull: false,
    },
    call_status: {
      type: Sequelize.TEXT,
      allowNull: false,
      validate: {
        isIn: [['successful', 'no_answer', 'missed_connection', 'busy', 'failed', 'voicemail']],
      },
    },
    conversation_summary: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    call_summary_title: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    clinic_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: { tableName: 'clinics' },
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    patient_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: { tableName: 'patients' },
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable({ tableName: 'ai_call_logs' });
}; 