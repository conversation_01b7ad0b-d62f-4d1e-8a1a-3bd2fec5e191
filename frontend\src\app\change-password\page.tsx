"use client";
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Lock, Eye, EyeOff, Loader2, Shield } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/contexts/AuthContext";
import {
  CHANGE_PASSWORD_CONSTANTS,
  VALIDATION_MESSAGES,
  TOAST_MESSAGES,
  FORM_FIELDS,
  FORM_IDS,
  ACCESSIBILITY,
  INPUT_TYPES,
  BUTTON_TYPES,
  FORM_ATTRIBUTES,
  ROUTES,
  ICONS,
  CSS_CLASSES,
} from "@/Constants/ChangePassword";
import styles from "./ChangePassword.module.css";
import { toastSuccess, toastError } from "@/hooks/use-toast";

const changePasswordSchema = z
  .object({
    [FORM_FIELDS.CURRENT_PASSWORD]: z
      .string()
      .min(1, { message: VALIDATION_MESSAGES.CURRENT_PASSWORD_REQUIRED }),
    [FORM_FIELDS.NEW_PASSWORD]: z
      .string()
      .min(8, { message: VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH })
      .regex(/[A-Z]/, { message: VALIDATION_MESSAGES.PASSWORD_UPPERCASE })
      .regex(/[a-z]/, { message: VALIDATION_MESSAGES.PASSWORD_LOWERCASE })
      .regex(/[0-9]/, { message: VALIDATION_MESSAGES.PASSWORD_NUMBER })
      .regex(/[!@#$%^&*]/, { message: VALIDATION_MESSAGES.PASSWORD_SPECIAL }),
    [FORM_FIELDS.CONFIRM_PASSWORD]: z
      .string()
      .min(1, { message: VALIDATION_MESSAGES.CONFIRM_PASSWORD_REQUIRED }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: VALIDATION_MESSAGES.PASSWORDS_MATCH,
    path: [FORM_FIELDS.CONFIRM_PASSWORD],
  });

type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

export default function ChangePasswordPage() {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState("");
  const { changePassword, loading } = useAuth();
  const router = useRouter();

  // Password validation checks
  const passwordChecks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[!@#$%^&*]/.test(password),
  };

  // const allChecksPassed = Object.values(passwordChecks).every(Boolean);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
  });

  // Watch password field for real-time validation
  const watchedPassword = watch(FORM_FIELDS.NEW_PASSWORD);

  // Update password state when watched password changes
  React.useEffect(() => {
    setPassword(watchedPassword || "");
  }, [watchedPassword]);

  // Password strength indicator component
  const PasswordStrengthIndicator = () => {
    if (!password) return null;

    return (
      <div className={styles[CSS_CLASSES.PASSWORD_STRENGTH]}>
        <div className={styles[CSS_CLASSES.PASSWORD_STRENGTH_TITLE]}>
          {CHANGE_PASSWORD_CONSTANTS.PASSWORD_REQUIREMENTS_TITLE}
        </div>
        <div className={styles[CSS_CLASSES.PASSWORD_CHECKS]}>
          <div
            className={`${styles[CSS_CLASSES.PASSWORD_CHECK]} ${
              passwordChecks.length ? styles[CSS_CLASSES.VALID] : styles[CSS_CLASSES.INVALID]
            }`}
          >
            <span className={styles[CSS_CLASSES.CHECK_ICON]}>
              {passwordChecks.length ? CHANGE_PASSWORD_CONSTANTS.CHECK_ICON : CHANGE_PASSWORD_CONSTANTS.CROSS_ICON}
            </span>
            {CHANGE_PASSWORD_CONSTANTS.REQUIREMENT_LENGTH}
          </div>
          <div
            className={`${styles[CSS_CLASSES.PASSWORD_CHECK]} ${
              passwordChecks.uppercase ? styles[CSS_CLASSES.VALID] : styles[CSS_CLASSES.INVALID]
            }`}
          >
            <span className={styles[CSS_CLASSES.CHECK_ICON]}>
              {passwordChecks.uppercase ? CHANGE_PASSWORD_CONSTANTS.CHECK_ICON : CHANGE_PASSWORD_CONSTANTS.CROSS_ICON}
            </span>
            {CHANGE_PASSWORD_CONSTANTS.REQUIREMENT_UPPERCASE}
          </div>
          <div
            className={`${styles[CSS_CLASSES.PASSWORD_CHECK]} ${
              passwordChecks.lowercase ? styles[CSS_CLASSES.VALID] : styles[CSS_CLASSES.INVALID]
            }`}
          >
            <span className={styles[CSS_CLASSES.CHECK_ICON]}>
              {passwordChecks.lowercase ? CHANGE_PASSWORD_CONSTANTS.CHECK_ICON : CHANGE_PASSWORD_CONSTANTS.CROSS_ICON}
            </span>
            {CHANGE_PASSWORD_CONSTANTS.REQUIREMENT_LOWERCASE}
          </div>
          <div
            className={`${styles[CSS_CLASSES.PASSWORD_CHECK]} ${
              passwordChecks.number ? styles[CSS_CLASSES.VALID] : styles[CSS_CLASSES.INVALID]
            }`}
          >
            <span className={styles[CSS_CLASSES.CHECK_ICON]}>
              {passwordChecks.number ? CHANGE_PASSWORD_CONSTANTS.CHECK_ICON : CHANGE_PASSWORD_CONSTANTS.CROSS_ICON}
            </span>
            {CHANGE_PASSWORD_CONSTANTS.REQUIREMENT_NUMBER}
          </div>
          <div
            className={`${styles[CSS_CLASSES.PASSWORD_CHECK]} ${
              passwordChecks.special ? styles[CSS_CLASSES.VALID] : styles[CSS_CLASSES.INVALID]
            }`}
          >
            <span className={styles[CSS_CLASSES.CHECK_ICON]}>
              {passwordChecks.special ? CHANGE_PASSWORD_CONSTANTS.CHECK_ICON : CHANGE_PASSWORD_CONSTANTS.CROSS_ICON}
            </span>
            {CHANGE_PASSWORD_CONSTANTS.REQUIREMENT_SPECIAL}
          </div>
        </div>
      </div>
    );
  };

  const onSubmit = async (data: ChangePasswordFormData) => {
    try {
      const result = await changePassword({
        currentPassword: data[FORM_FIELDS.CURRENT_PASSWORD],
        newPassword: data[FORM_FIELDS.NEW_PASSWORD],
        confirmPassword: data[FORM_FIELDS.CONFIRM_PASSWORD],
      });

      if (result.success) {
        toastSuccess(TOAST_MESSAGES.CHANGE_SUCCESS);
        setTimeout(() => {
          router.push(ROUTES.DASHBOARD);
        }, 2000);
      } else {
        toastError(result.error || TOAST_MESSAGES.CHANGE_FAILED);
      }
    } catch {
      toastError(TOAST_MESSAGES.CHANGE_FAILED);
    }
  };

  return (
    <div className={CSS_CLASSES.PAGE_CONTAINER}>
      <div className={CSS_CLASSES.PAGE_HEADER}>
        <h1 className={CSS_CLASSES.PAGE_TITLE}>
          {CHANGE_PASSWORD_CONSTANTS.PAGE_TITLE}
        </h1>
        <p className={CSS_CLASSES.PAGE_SUBTITLE}>
          {CHANGE_PASSWORD_CONSTANTS.PAGE_SUBTITLE}
        </p>
      </div>

      <Card className={styles[CSS_CLASSES.CHANGE_CARD]}>
        <CardHeader className={styles[CSS_CLASSES.CHANGE_HEADER]}>
          <div className={CSS_CLASSES.ICON_WRAPPER}>
            <div className={CSS_CLASSES.ICON_CONTAINER}>
              <Shield className={ICONS.SHIELD} />
            </div>
            <div>
              <CardTitle className={styles[CSS_CLASSES.CHANGE_TITLE]}>
                {CHANGE_PASSWORD_CONSTANTS.SECURITY_SETTINGS_TITLE}
              </CardTitle>
              <p className={styles[CSS_CLASSES.CHANGE_SUBTITLE]}>
                {CHANGE_PASSWORD_CONSTANTS.SUBTITLE}
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className={styles[CSS_CLASSES.CHANGE_FORM]}>
            <div className={styles[CSS_CLASSES.FORM_FIELD]}>
              <Label
                htmlFor={FORM_IDS.CURRENT_PASSWORD}
                className={styles[CSS_CLASSES.FORM_LABEL]}
              >
                {CHANGE_PASSWORD_CONSTANTS.CURRENT_PASSWORD_LABEL}
              </Label>
              <div className="relative mt-1">
                <span
                  className={`${styles[CSS_CLASSES.CHANGE_ICON]} ${styles[CSS_CLASSES.CHANGE_ICON_LEFT]}`}
                >
                  <Lock size={parseInt(ICONS.LOCK)} />
                </span>
                <Input
                  id={FORM_IDS.CURRENT_PASSWORD}
                  type={
                    showCurrentPassword
                      ? INPUT_TYPES.TEXT
                      : INPUT_TYPES.PASSWORD
                  }
                  aria-label={ACCESSIBILITY.CURRENT_PASSWORD_INPUT}
                  placeholder={
                    CHANGE_PASSWORD_CONSTANTS.CURRENT_PASSWORD_PLACEHOLDER
                  }
                  autoComplete={FORM_ATTRIBUTES.CURRENT_PASSWORD_AUTOCOMPLETE}
                  {...register(FORM_FIELDS.CURRENT_PASSWORD)}
                  className={styles[CSS_CLASSES.CHANGE_INPUT]}
                />
                <button
                  type={BUTTON_TYPES.BUTTON}
                  aria-label={
                    showCurrentPassword
                      ? ACCESSIBILITY.HIDE_PASSWORD
                      : ACCESSIBILITY.SHOW_PASSWORD
                  }
                  className={styles[CSS_CLASSES.CHANGE_PASSWORD_TOGGLE]}
                  onClick={() => setShowCurrentPassword((v) => !v)}
                  tabIndex={-1}
                >
                  {showCurrentPassword ? (
                    <EyeOff size={parseInt(ICONS.EYE_OFF)} />
                  ) : (
                    <Eye size={parseInt(ICONS.EYE)} />
                  )}
                </button>
              </div>
              {errors[FORM_FIELDS.CURRENT_PASSWORD] && (
                <span className={styles[CSS_CLASSES.CHANGE_ERROR]}>
                  {errors[FORM_FIELDS.CURRENT_PASSWORD]?.message}
                </span>
              )}
            </div>

            <div className={styles[CSS_CLASSES.FORM_FIELD]}>
              <Label
                htmlFor={FORM_IDS.NEW_PASSWORD}
                className={styles[CSS_CLASSES.FORM_LABEL]}
              >
                {CHANGE_PASSWORD_CONSTANTS.NEW_PASSWORD_LABEL}
              </Label>
              <div className="relative mt-1">
                <span
                  className={`${styles[CSS_CLASSES.CHANGE_ICON]} ${styles[CSS_CLASSES.CHANGE_ICON_LEFT]}`}
                >
                  <Lock size={parseInt(ICONS.LOCK)} />
                </span>
                <Input
                  id={FORM_IDS.NEW_PASSWORD}
                  type={
                    showNewPassword ? INPUT_TYPES.TEXT : INPUT_TYPES.PASSWORD
                  }
                  aria-label={ACCESSIBILITY.NEW_PASSWORD_INPUT}
                  placeholder={
                    CHANGE_PASSWORD_CONSTANTS.NEW_PASSWORD_PLACEHOLDER
                  }
                  autoComplete={FORM_ATTRIBUTES.NEW_PASSWORD_AUTOCOMPLETE}
                  {...register(FORM_FIELDS.NEW_PASSWORD)}
                  className={styles[CSS_CLASSES.CHANGE_INPUT]}
                />
                <button
                  type={BUTTON_TYPES.BUTTON}
                  aria-label={
                    showNewPassword
                      ? ACCESSIBILITY.HIDE_PASSWORD
                      : ACCESSIBILITY.SHOW_PASSWORD
                  }
                  className={styles[CSS_CLASSES.CHANGE_PASSWORD_TOGGLE]}
                  onClick={() => setShowNewPassword((v) => !v)}
                  tabIndex={-1}
                >
                  {showNewPassword ? <EyeOff size={parseInt(ICONS.EYE_OFF)} /> : <Eye size={parseInt(ICONS.EYE)} />}
                </button>
              </div>
              {errors[FORM_FIELDS.NEW_PASSWORD] && (
                <span className={styles[CSS_CLASSES.CHANGE_ERROR]}>
                  {errors[FORM_FIELDS.NEW_PASSWORD]?.message}
                </span>
              )}
              <PasswordStrengthIndicator />
            </div>

            <div className={styles[CSS_CLASSES.FORM_FIELD]}>
              <Label
                htmlFor={FORM_IDS.CONFIRM_PASSWORD}
                className={styles[CSS_CLASSES.FORM_LABEL]}
              >
                {CHANGE_PASSWORD_CONSTANTS.CONFIRM_PASSWORD_LABEL}
              </Label>
              <div className="relative mt-1">
                <span
                  className={`${styles[CSS_CLASSES.CHANGE_ICON]} ${styles[CSS_CLASSES.CHANGE_ICON_LEFT]}`}
                >
                  <Lock size={parseInt(ICONS.LOCK)} />
                </span>
                <Input
                  id={FORM_IDS.CONFIRM_PASSWORD}
                  type={
                    showConfirmPassword
                      ? INPUT_TYPES.TEXT
                      : INPUT_TYPES.PASSWORD
                  }
                  aria-label={ACCESSIBILITY.CONFIRM_PASSWORD_INPUT}
                  placeholder={
                    CHANGE_PASSWORD_CONSTANTS.CONFIRM_PASSWORD_PLACEHOLDER
                  }
                  autoComplete={FORM_ATTRIBUTES.CONFIRM_PASSWORD_AUTOCOMPLETE}
                  {...register(FORM_FIELDS.CONFIRM_PASSWORD)}
                  className={styles[CSS_CLASSES.CHANGE_INPUT]}
                />
                <button
                  type={BUTTON_TYPES.BUTTON}
                  aria-label={
                    showConfirmPassword
                      ? ACCESSIBILITY.HIDE_PASSWORD
                      : ACCESSIBILITY.SHOW_PASSWORD
                  }
                  className={styles[CSS_CLASSES.CHANGE_PASSWORD_TOGGLE]}
                  onClick={() => setShowConfirmPassword((v) => !v)}
                  tabIndex={-1}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={parseInt(ICONS.EYE_OFF)} />
                  ) : (
                    <Eye size={parseInt(ICONS.EYE)} />
                  )}
                </button>
              </div>
              {errors[FORM_FIELDS.CONFIRM_PASSWORD] && (
                <span className={styles[CSS_CLASSES.CHANGE_ERROR]}>
                  {errors[FORM_FIELDS.CONFIRM_PASSWORD]?.message}
                </span>
              )}
            </div>

            <div className={CSS_CLASSES.FLEX_CONTAINER}>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(ROUTES.DASHBOARD)}
                className={CSS_CLASSES.FLEX_ITEM}
              >
                {CHANGE_PASSWORD_CONSTANTS.CANCEL}
              </Button>
              <Button
                type={BUTTON_TYPES.SUBMIT}
                className={`${styles[CSS_CLASSES.CHANGE_BUTTON]} ${CSS_CLASSES.FLEX_ITEM}`}
                disabled={isSubmitting || loading}
                aria-label={ACCESSIBILITY.CHANGE_PASSWORD}
              >
                {isSubmitting || loading ? (
                  <span className={CSS_CLASSES.BUTTON_WRAPPER}>
                    <Loader2 className="animate-spin" size={parseInt(ICONS.LOADER)} />
                    {CHANGE_PASSWORD_CONSTANTS.LOADING}
                  </span>
                ) : (
                  CHANGE_PASSWORD_CONSTANTS.CHANGE_PASSWORD
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 
