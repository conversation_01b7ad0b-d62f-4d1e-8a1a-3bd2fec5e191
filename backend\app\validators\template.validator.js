/**
 * Template Validators: express-validator schemas for template create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating a template
export const createTemplateSchema = [
  body('type')
    .isIn(['sms', 'email', 'call_prompt'])
    .withMessage(constants.TEMPLATE_TYPE_INVALID)
    .notEmpty()
    .withMessage(constants.TEMPLATE_TYPE_REQUIRED),
  body('name')
    .notEmpty()
    .withMessage(constants.TEMPLATE_NAME_REQUIRED)
    .isString()
    .withMessage(constants.TEMPLATE_NAME_STRING),
  body('content')
    .notEmpty()
    .withMessage(constants.TEMPLATE_CONTENT_REQUIRED)
    .isString()
    .withMessage(constants.TEMPLATE_CONTENT_STRING),
];

// Validation schema for updating a template
export const updateTemplateSchema = [
  body('type')
    .optional()
    .isIn(['sms', 'email', 'call_prompt'])
    .withMessage(constants.TEMPLATE_TYPE_INVALID),
  body('name')
    .optional()
    .isString()
    .withMessage(constants.TEMPLATE_NAME_STRING),
  body('content')
    .optional()
    .isString()
    .withMessage(constants.TEMPLATE_CONTENT_STRING),
]; 