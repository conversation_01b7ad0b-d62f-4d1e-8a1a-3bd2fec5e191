import { parse } from 'csv-parse/sync';
import xlsx from 'xlsx';
import * as constants from '../utils/constants.utils.js';

/**
 * Parse patient file (CSV or Excel) to array of patient objects
 */
export function parsePatientFile(file) {
  const allowedFields = constants.PATIENT_ALLOWED_FIELDS;
  let patients = [];
  if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
    const csv = file.buffer.toString('utf8');
    const records = parse(csv, {
      columns: true,
      skip_empty_lines: true,
      trim: true,
      // Ensure proper handling of special characters like +
      relax_quotes: true,
      relax_column_count: true,
    });
    for (const row of records) {
      const obj = {};
      const custom = {};
      for (const key in row) {
        if (allowedFields.includes(key)) {
          obj[key] = row[key];
        } else if (key) {
          custom[key] = row[key];
        }
      }
      
      // Merge country code and phone number if both exist
      if (obj.country_code && obj.phone_number) {
        // Clean the country code (preserve the + symbol, remove only extra spaces)
        const cleanCountryCode = obj.country_code.trim();
        // Clean the phone number (remove any extra spaces or special characters)
        const cleanPhoneNumber = obj.phone_number.trim().replace(/\s+/g, '');
        // Merge them together (preserving the + from country code)
        obj.phone_number = `${cleanCountryCode}${cleanPhoneNumber}`;
        // Remove country_code from the object since it's now merged
        delete obj.country_code;
      }
      
      if (Object.keys(custom).length > 0) {
        obj.custom = JSON.stringify(custom);
      }
      patients.push(obj);
    }
  } else if (
    file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.mimetype === 'application/vnd.ms-excel' ||
    file.originalname.endsWith('.xlsx') ||
    file.originalname.endsWith('.xls')
  ) {
    const workbook = xlsx.read(file.buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows = xlsx.utils.sheet_to_json(sheet);
    for (const row of rows) {
      const obj = {};
      const custom = {};
      for (const key in row) {
        if (allowedFields.includes(key)) {
          obj[key] = row[key];
        } else if (key) {
          custom[key] = row[key];
        }
      }
      
      // Merge country code and phone number if both exist
      if (obj.country_code && obj.phone_number) {
        // Clean the country code (preserve the + symbol, remove only extra spaces)
        const cleanCountryCode = obj.country_code.trim();
        // Clean the phone number (remove any extra spaces or special characters)
        const cleanPhoneNumber = obj.phone_number.trim().replace(/\s+/g, '');
        // Merge them together (preserving the + from country code)
        obj.phone_number = `${cleanCountryCode}${cleanPhoneNumber}`;
        // Remove country_code from the object since it's now merged
        delete obj.country_code;
      }
      
      if (Object.keys(custom).length > 0) {
        obj.custom = JSON.stringify(custom);
      }
      patients.push(obj);
    }
  } else {
    throw new Error(constants.PATIENT_FILE_TYPE_INVALID);
  }
  return patients;
}

/**
 * Normalize patient fields (tags, doctors, booleans, nulls, clinic_id)
 */
export function normalizePatients(patients, req) {
  const allowedFields = constants.PATIENT_ALLOWED_FIELDS;
  return patients.map((p) => {
    const newP = { ...p };
    if (req.user && req.user.id) {
      newP.created_by = req.user.id;
      newP.updated_by = req.user.id;
    }
    if (typeof newP.tags === 'string') {
      let val = newP.tags.trim();
      if (val.toUpperCase() === 'NULL' || val === '') {
        newP.tags = null;
      } else {
        if ((val.startsWith('{') && val.endsWith('}')) || (val.startsWith('[') && val.endsWith(']'))) {
          val = val.slice(1, -1);
        }
        newP.tags = val.split(',').map(s => s.trim()).filter(Boolean);
      }
    }
    if (typeof newP.doctors === 'string') {
      let val = newP.doctors.trim();
      if (val.toUpperCase() === 'NULL' || val === '') {
        newP.doctors = null;
      } else {
        if ((val.startsWith('{') && val.endsWith('}')) || (val.startsWith('[') && val.endsWith(']'))) {
          val = val.slice(1, -1);
        }
        newP.doctors = val.split(',').map(s => s.trim()).filter(Boolean).map(Number);
      }
    }
    if (typeof newP.is_active === 'string') {
      if (newP.is_active.toUpperCase() === 'TRUE') newP.is_active = true;
      else if (newP.is_active.toUpperCase() === 'FALSE') newP.is_active = false;
    }
    if (typeof newP.is_deleted === 'string') {
      if (newP.is_deleted.toUpperCase() === 'TRUE') newP.is_deleted = true;
      else if (newP.is_deleted.toUpperCase() === 'FALSE') newP.is_deleted = false;
    }
    // Handle date fields specifically
    const dateFields = ['dob', 'last_visit'];
    for (const field of dateFields) {
      if (newP[field]) {
        const dateValue = newP[field].toString().trim();
        if (dateValue.toUpperCase() === 'NULL' || dateValue === '') {
          newP[field] = null;
        } else {
          // Try to parse the date
          const parsedDate = new Date(dateValue);
          if (isNaN(parsedDate.getTime())) {
            // Invalid date, set to null
            newP[field] = null;
          } else {
            // Valid date, format as ISO string
            newP[field] = parsedDate.toISOString();
          }
        }
      }
    }

    // Handle other fields
    for (const key in newP) {
      if (typeof newP[key] === 'string' && (newP[key].toUpperCase() === 'NULL' || newP[key].trim() === '')) {
        newP[key] = null;
      }
    }
    if (req.params && req.params.clinic_id && !isNaN(Number(req.params.clinic_id))) {
      newP.clinic_id = Number(req.params.clinic_id);
    } else {
      newP.clinic_id = 1;
    }
    return newP;
  });
}

/**
 * Filter out invalid patient records (must have at least one allowed field)
 */
export function filterValidPatients(patients) {
  const allowedFields = constants.PATIENT_ALLOWED_FIELDS;
  return patients.filter(p => allowedFields.some(f => p[f] && p[f] !== ''));
}
/**
 * Bulk create patients from array
 * @param {Array} patientsData
 * @returns {Promise<Array>} Inserted patients
 */
export const bulkCreatePatients = async (patientsData) => {
  try {
    const inserted = await Patient.bulkCreate(patientsData, { validate: true });
    return inserted;
  } catch (error) {
    // Log the error and the first patient data for debugging
    console.error('Bulk create error:', error);
    if (patientsData && patientsData.length > 0) {
      console.error('First patient data:', patientsData[0]);
    }
    throw error;
  }
};
import { Op } from 'sequelize';
import { Patient } from '../models/index.js';

/**
 * Check if a patient exists by email or phone number
 * @param {Object} params - { email, phone_number }
 * @returns {Promise<Object|null>} The patient if found, else null
 */
export const findPatientByEmailOrPhone = async ({ email, phone_number }) => {
  const orConditions = [];
  if (email) orConditions.push({ email });
  if (phone_number) orConditions.push({ phone_number });
  if (orConditions.length === 0) return null;
  return await Patient.findOne({
    where: {
      [Op.or]: orConditions,
    },
  });
};

/**
 * Get all the patient in the clinic who have not visted in last three months
 * @param {Object} params - { clinic_id }
 * @returns {Promise<Object|null>} The patient list
 */
export const findInactivePatients = async (clinic_id, days) => {
  const WeeksAgo = new Date();
  WeeksAgo.setDate(WeeksAgo.getDate() - days);

  return await Patient.findAll({
    where: {
      last_visit: { [Op.lt]: WeeksAgo },
      clinic_id: clinic_id,
      is_deleted: false,
      is_active: true
    }
  });
};

/**
 * Get all patients, with optional filters for is_active and is_deleted
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of patients
 */
export const getAllPatients = async (filters = {}) => {
  try {
    // logger.info(loggerMessages.FETCHING_PATIENTS);
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.clinic_id !== undefined) where.clinic_id = filters.clinic_id;
    const patients = await Patient.findAll({ 
      where,
      order: [['created_at', 'DESC']]
    });
    return patients;
  } catch (error) {
    // logger.error(loggerMessages.ERROR_FETCHING_PATIENTS, error);
    throw new Error(error.message);
  }
};

/**
 * Get a patient by its ID
 * @param {number} id - Patient ID
 * @returns {Promise<Object|null>} The patient or null if not found
 */
export const getPatientById = async (id) => {
  try {
    // logger.info(`${loggerMessages.FETCHING_PATIENT_BY_ID}: ${id}`);
    const patient = await Patient.findByPk(id);
    return patient;
  } catch (error) {
    // logger.error(`${loggerMessages.ERROR_FETCHING_PATIENT_BY_ID}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Create a new patient
 * @param {Object} patientData - Data for new patient
 * @returns {Promise<Object>} The created patient
 */
export const createPatient = async (patientData) => {
  try {
    // logger.info(loggerMessages.CREATING_PATIENT);
    const newPatient = await Patient.create(patientData);
    return newPatient;
  } catch (error) {
    // logger.error(loggerMessages.ERROR_CREATING_PATIENT, error);
    throw new Error(error.message);
  }
};

/**
 * Update a patient by its ID
 * @param {number} id - Patient ID
 * @param {Object} patientData - Data to update
 * @returns {Promise<Object|null>} The updated patient or null if not found
 */
export const updatePatient = async (id, patientData) => {
  try {
    // logger.info(`${loggerMessages.UPDATING_PATIENT}: ${id}`);
    await Patient.update(patientData, { where: { id } });
    const updatedPatient = await Patient.findByPk(id);
    return updatedPatient;
  } catch (error) {
    // logger.error(`${loggerMessages.ERROR_UPDATING_PATIENT}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Soft delete a patient by its ID (sets is_deleted to true)
 * @param {number} id - Patient ID
 * @returns {Promise<Object|null>} The deleted patient or null if not found
 */
export const deletePatient = async (id) => {
  try {
    // logger.info(`${loggerMessages.DELETING_PATIENT}: ${id}`);
    await Patient.update({ is_deleted: true }, { where: { id } });
    const deletedPatient = await Patient.findByPk(id);
    return deletedPatient;
  } catch (error) {
    // logger.error(`${loggerMessages.ERROR_DELETING_PATIENT}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Get patients by their IDs with user details
 * @param {Array} patientIds - Array of patient IDs
 * @returns {Promise<Array>} List of patients with user details
 */
export const getPatientsByIds = async (patientIds) => {
  try {
    const { Patient, User } = await import('../models/index.js');
    
    const patients = await Patient.findAll({
      where: {
        id: patientIds,
        is_deleted: false,
        is_active: true
      },
      include: [
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email', 'user_phonenumber'],
          where: { is_deleted: false, is_active: true }
        },
        {
          model: User,
          as: 'Updater',
          attributes: ['id', 'name', 'email', 'user_phonenumber'],
          where: { is_deleted: false, is_active: true }
        }
      ],
      attributes: [
        'id', 'clinic_id', 'first_name', 'last_name', 'email', 
        'phone_number', 'last_visit', 'last_visit_summary', 'tags', 
        'preferences', 'doctors', 'custom', 'dob', 'gender', 'address',
        'created_at', 'updated_at', 'created_by', 'updated_by'
      ]
    });
    
    return patients;
  } catch (error) {
    throw new Error(error.message);
  }
};
