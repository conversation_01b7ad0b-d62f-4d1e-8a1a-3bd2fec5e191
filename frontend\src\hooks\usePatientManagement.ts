import { useState, useCallback } from "react";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { apiRequest } from "@/utils/axios.utils";

export interface PatientWithUserDetails {
  id: number;
  clinic_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  last_visit: string | null;
  last_visit_summary: string | null;
  tags: string[] | null;
  preferences: Record<string, unknown> | null;
  dob: string | null;
  gender: "male" | "female" | "other" | null;
  address: string | null;
  doctors: number[] | null;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  Creator?: {
    id: number;
    name: string;
    email: string;
    user_phonenumber: string;
  };
  Updater?: {
    id: number;
    name: string;
    email: string;
    user_phonenumber: string;
  };
}

export interface BatchCallResponse {
  patients: PatientWithUserDetails[];
  total_selected: number;
  clinic_id: number;
  scheduled_time: string;
  reactivation_id?: number | null;
}

export function usePatientManagement() {
  const [patients, setPatients] = useState<PatientWithUserDetails[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getPatientsByClinic = useCallback(async (clinicId: number) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`/patient/clinic/${clinicId}`)) as { status: boolean; message?: string; data?: PatientWithUserDetails[] };
      if (response && response.status) {
        setPatients(response.data || []);
        return { success: true, data: response.data || [] };
      } else {
        const errorMsg = response?.message || 'Failed to fetch patients';
        compactErrorMessage(errorMsg);
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to fetch patients');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const submitBatchCall = useCallback(async (clinicId: number, patientIds: number[], time?: string) => {
    setLoading(true);
    setError(null);
    try {
      const payload = {
        clinic_id: clinicId,
        patient_ids: patientIds,
        time: time || new Date().toISOString()
      };

      const response = (await apiRequest.post('/patient/batch-call', payload)) as { status: boolean; message?: string; data?: BatchCallResponse };
      if (response && response.status) {
        const successMessage = response.data?.reactivation_id 
          ? `Batch call submitted successfully! Reactivation ID: ${response.data.reactivation_id}`
          : 'Batch call submitted successfully';
        compactSuccessMessage(successMessage);
        return { success: true, data: response.data };
      } else {
        const errorMsg = response?.message || 'Failed to submit batch call';
        compactErrorMessage(errorMsg);
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, 'Failed to submit batch call');
      compactErrorMessage(errorMsg);
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const clearPatients = useCallback(() => {
    setPatients([]);
    setError(null);
  }, []);

  return {
    patients,
    loading,
    error,
    getPatientsByClinic,
    submitBatchCall,
    clearPatients,
  };
}
