/**
 * Timezone Utility Service: Handles timezone conversions and operations
 */
import * as constants from './constants.utils.js';
import * as loggerMessages from './log_messages.utils.js';

/**
 * Convert Indian time to UTC
 * @param {string|Date} indianDateTime - DateTime string or Date object in Indian timezone
 * @returns {Date} UTC Date object
 */
export const convertIndianTimeToUTC = (indianDateTime) => {
  try {
    // Create a date object from the input
    const date = new Date(indianDateTime);
    
    if (isNaN(date.getTime())) {
      throw new Error(constants.INVALID_DATE_FORMAT);
    }
    
    // Get the timezone offset for IST (UTC+5:30)
    const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
    
    // Convert to UTC by subtracting the IST offset
    const utcDate = new Date(date.getTime() - istOffset);
    
    return utcDate;
  } catch (error) {
    throw new Error(`${constants.ERROR_CONVERTING_TIMEZONE}: ${error.message}`);
  }
};

/**
 * Convert UTC time to Indian time
 * @param {string|Date} utcDateTime - DateTime string or Date object in UTC
 * @returns {Date} Indian time Date object
 */
export const convertUTCToIndianTime = (utcDateTime) => {
  try {
    // Create a date object from the input
    const date = new Date(utcDateTime);
    
    if (isNaN(date.getTime())) {
      throw new Error(constants.INVALID_DATE_FORMAT);
    }
    
    // Get the timezone offset for IST (UTC+5:30)
    const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
    
    // Convert to Indian time by adding the IST offset
    const indianDate = new Date(date.getTime() + istOffset);
    
    return indianDate;
  } catch (error) {
    throw new Error(`${constants.ERROR_CONVERTING_TIMEZONE}: ${error.message}`);
  }
};

/**
 * Format date for display in Indian timezone
 * @param {string|Date} utcDateTime - DateTime string or Date object in UTC
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string in Indian timezone
 */
export const formatIndianTime = (utcDateTime, options = {}) => {
  try {
    const indianDate = convertUTCToIndianTime(utcDateTime);
    
    const defaultOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZone: 'Asia/Kolkata'
    };
    
    const formatOptions = { ...defaultOptions, ...options };
    
    return indianDate.toLocaleDateString('en-IN', formatOptions);
  } catch (error) {
    throw new Error(`${constants.ERROR_CONVERTING_TIMEZONE}: ${error.message}`);
  }
};

/**
 * Get current Indian time
 * @returns {Date} Current date and time in Indian timezone
 */
export const getCurrentIndianTime = () => {
  const now = new Date();
  return convertUTCToIndianTime(now);
};

/**
 * Get current UTC time
 * @returns {Date} Current date and time in UTC
 */
export const getCurrentUTCTime = () => {
  return new Date();
};

/**
 * Check if a date is in the future (Indian timezone)
 * @param {string|Date} appointmentDateTime - Appointment date and time
 * @returns {boolean} True if appointment is in the future
 */
export const isAppointmentInFuture = (appointmentDateTime) => {
  try {
    const appointmentDate = new Date(appointmentDateTime);
    const currentIndianTime = getCurrentIndianTime();
    
    return appointmentDate > currentIndianTime;
  } catch (error) {
    return false;
  }
};

/**
 * Get timezone offset information
 * @returns {Object} Timezone offset details
 */
export const getTimezoneInfo = () => {
  return {
    IST_OFFSET_HOURS: 5.5,
    IST_OFFSET_MINUTES: 330,
    IST_OFFSET_MS: 5.5 * 60 * 60 * 1000,
    TIMEZONE_NAME: 'Asia/Kolkata',
    DESCRIPTION: 'Indian Standard Time (UTC+5:30)'
  };
};
