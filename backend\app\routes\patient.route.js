import multer from 'multer';
import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createPatientSchema, updatePatientSchema, batchCallSchema } from '../validators/patient.validator.js';
import * as patientController from '../controllers/patient.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new patient
// POST /v1/patient/create
router.post(
  '',
  verifyToken,
  validate(createPatientSchema),
  patientController.createPatient
);

router.post(
  '/elevenlab',
  // verifyToken,
  validate(createPatientSchema),
  patientController.createPatient
);

// Get all patients
// GET /v1/patient/list
router.get(
  '',
  verifyToken,
  patientController.getAllPatients
);

// Outbound call for inactive patients
// POST /v1/patient/outbound
router.post(
  '/outbound',
  patientController.outboundCall
);

// Outbound call for all patients in a clinic
// POST /v1/patient/all-outbound
router.post(
  '/outbound/all',
  verifyToken,
  patientController.allOutboundCall
);

// Manually trigger outbound calls for a clinic
// POST /v1/patient/trigger-outbound/:clinicId
router.post(
  '/trigger-outbound/:clinicId',
  verifyToken,
  patientController.triggerOutboundCalls
);

// Manually trigger API call for a clinic
// POST /v1/patient/call-api/:clinicId
router.post(
  '/call-api/:clinicId',
  verifyToken,
  patientController.callOutboundAPI
);

// Get cron job status
// GET /v1/patient/cron-status
router.get(
  '/cron-status',
  verifyToken,
  patientController.getCronStatus
);

// Get patient by phone
// GET /v1/patient/phone
router.get(
  '/phone',
  // verifyToken,
  patientController.getPatientByPhone
);

// Get patient by ID
// GET /v1/patient/:id
router.get(
  '/:id',
  verifyToken,
  patientController.getPatientById
);
// Update patient by ID
// PUT /v1/patient/:id
router.put(
  '/:id',
  verifyToken,
  validate(updatePatientSchema),
  patientController.updatePatient
);


// Soft delete patient by ID
// DELETE /v1/patient/:id
router.delete(
  '/:id',
  verifyToken,
  patientController.deletePatient
);

const upload = multer({ storage: multer.memoryStorage() });
// Upload patient list (CSV/Excel)
// POST /v1/patient/upload
router.post(
  '/upload/:clinic_id',
  verifyToken,
  upload.single('file'),
  patientController.uploadPatientList
);

// Get patients by clinic ID
// GET /v1/patient/clinic/:clinicId
router.get(
  '/clinic/:clinicId',
  verifyToken,
  patientController.getPatientsByClinic
);

// Submit batch call with selected patients
// POST /v1/patient/batch-call
router.post(
  '/batch-call',
  verifyToken,
  validate(batchCallSchema),
  patientController.submitBatchCall
);

// Get reactivation records for a clinic
// GET /v1/patient/reactivations/:clinicId
router.get(
  '/reactivations/:clinicId',
  verifyToken,
  patientController.getReactivationsByClinic
);

// Get reactivation statistics for a clinic
// GET /v1/patient/reactivations/:clinicId/stats
router.get(
  '/reactivations/:clinicId/stats',
  verifyToken,
  patientController.getReactivationStats
);

export default router;
