import React from "react";
import { Calendar, Phone, RefreshCw, Users, UserCheck } from "lucide-react";
import StatCard from "@/components/CommonComponents/StatCard";
import {
  ANALYTICS_METRICS,
} from "@/Constants/Analytics";

// Props for the AnalyticsStatsGrid component
interface AnalyticsStatsGridProps {
  metrics?: typeof ANALYTICS_METRICS; // Optional custom metrics, defaults to ANALYTICS_METRICS
}

/**
 * Renders a grid of analytics stat cards.
 * Each card shows a metric (title, value, icon, trend, etc).
 */
const AnalyticsStatsGrid: React.FC<AnalyticsStatsGridProps> = ({
  metrics = ANALYTICS_METRICS,
}) => {
  // Maps icon name (string) to a Lucide icon component
  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      Calendar: <Calendar className="h-8 w-8 text-blue-600" />,
      Phone: <Phone className="h-8 w-8 text-green-600" />,
      RefreshCw: <RefreshCw className="h-8 w-8 text-purple-600" />,
      Users: <Users className="h-8 w-8 text-orange-600" />,
      UserCheck: <UserCheck className="h-8 w-8 text-orange-600" />,
    };
    return iconMap[iconName] || iconMap.Calendar;
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
      {/* Render a StatCard for each metric */}
      {metrics.map((metric, index) => {
        const icon = getIconComponent(metric.icon);

        return (
          <StatCard
            key={index}
            title={metric.title}
            value={metric.value}
            icon={icon}
          />
        );
      })}
    </div>
  );
};

export default AnalyticsStatsGrid;
