import dotenv from 'dotenv';
import sgMail from '@sendgrid/mail';
import * as constants from '../utils/constants.utils.js';

dotenv.config();

// Configure SendGrid with API key
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

export const sendEmail = async (email, subject, text = '', html) => {
  const msg = {
    to: email,
    from: process.env.SENDGRID_FROM_EMAIL, // Verified sender email
    subject,
    text: text || '', // Ensure text is always a string
    html,
  };

  try {
    await sgMail.send(msg);
    return constants.EMAIL_SENT_SUCCESS;
  } catch (error) {
    console.log(error);
    
    throw new Error(constants.EMAIL_SEND_FAILED);
  }
};

export const sendResetPasswordEmail = async (email, resetToken) => {
  const subject = 'Densy AI - Password Reset Request';
  const resetUrl = `${process.env.FRONTEND_BASE_URL}/reset-password?token=${resetToken}`;
  const text = `Hello,\n\nWe received a request to reset your password for your Densy AI account.\n\nClick the link below to set a new password:\n${resetUrl}\n\nIf you did not request this, please contact support immediately.\n\nThank you,\nDensy AI Team`;
  const logoUrl = `${process.env.BACKEND_BASE_URL}/public/Densy-AI.svg`;
  const html = `
    <div style="font-family: 'Segoe UI', Arial, sans-serif; background: #f6f8fa; padding: 32px; border-radius: 12px; max-width: 480px; margin: auto; color: #222;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src='${logoUrl}' alt='Densy AI Logo' style='height: 40px;'/>
        <h2 style="margin: 16px 0 8px 0; font-size: 1.5rem; color: #2563eb; font-weight: 700;">Password Reset Request</h2>
      </div>
      <p style="font-size: 1rem; color: #444;">Hello,</p>
      <p style="font-size: 1rem; color: #444;">We received a request to reset your password for your <strong>Densy AI</strong> account.</p>
      <div style="background: #e0f2fe; padding: 16px; border-radius: 8px; margin: 24px 0; text-align: center;">
        <span style="font-size: 1.1rem; color: #2563eb;">Click the link below to set a new password:</span><br/>
        <a href='${resetUrl}' style="font-size: 1.1rem; color: #22c55e; font-weight: bold; text-decoration: underline;">Set New Password</a>
      </div>
      <p style="font-size: 1rem; color: #444;">If you did not request this, please contact our support team immediately.</p>
      <hr style="margin: 32px 0; border: none; border-top: 1px solid #e5e7eb;"/>
      <p style="font-size: 0.95rem; color: #6b7280; text-align: center;">Thank you,<br/>Densy AI Team</p>
    </div>
  `;
  return sendEmail(email, subject, text, html);
};
