import * as templateService from '../services/template.service.js';
import { logTemplateAudit } from '../services/templateAuditLog.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { AuditActions, TemplateAuditDescriptions } from '../utils/auditlog_messages.utils.js';

/**
 * Create a new template
 * @route POST /v1/template/create
 * <AUTHOR>
 */
export const createTemplate = async (req, res) => {
  logger.info(loggerMessages.CREATING_TEMPLATE);
  try {
    const templateData = req.body;
    if (req.user && req.user.id) {
      templateData.created_by = req.user.id;
      templateData.updated_by = req.user.id;
    }
    
    // Check for existing template by name and type
    const existingTemplate = await templateService.findTemplateByNameAndType({
      name: templateData.name,
      type: templateData.type,
    });
    if (existingTemplate) {
      return res.status(status.STATUS_CODE_CONFLICT).json(
        errorResponse(constants.TEMPLATE_ALREADY_EXISTS_ERROR)
      );
    }
    
    // Create the template
    const newTemplate = await templateService.createTemplate(templateData);
    await logTemplateAudit({
      action: AuditActions.CREATE,
      record_id: newTemplate.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newTemplate),
      description: TemplateAuditDescriptions.TEMPLATE_CREATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.TEMPLATE_CREATED_SUCCESSFULLY, newTemplate)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_TEMPLATE);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all templates
 * @route GET /v1/template/list
 * <AUTHOR>
 */
export const getAllTemplates = async (req, res) => {
  logger.info(loggerMessages.FETCHING_TEMPLATES);
  try {
    const filters = {
      is_active: req.query.is_active,
      is_deleted: req.query.is_deleted,
      type: req.query.type,
    };
    const templates = await templateService.getAllTemplates(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.TEMPLATES_FETCHED_SUCCESSFULLY, templates)
    );
  } catch (error) {
    logger.info(loggerMessages.ERROR_FETCHING_TEMPLATES);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Get a single template by ID
 * @route GET /v1/template/:id
 * <AUTHOR>
 */
export const getTemplateById = async (req, res) => {
  logger.info(loggerMessages.FETCHING_TEMPLATE_BY_ID);
  try {
    const template = await templateService.getTemplateById(req.params.id);
    if (!template) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.TEMPLATE_NOT_FOUND)
      );
    }
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.TEMPLATE_FETCHED_SUCCESSFULLY, template)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_TEMPLATE_BY_ID);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Update a template by ID
 * @route PUT /v1/template/:id
 * <AUTHOR>
 */
export const updateTemplate = async (req, res) => {
  logger.info(loggerMessages.UPDATING_TEMPLATE);
  try {
    const templateData = req.body;
    const { id } = req.params;
    if (req.user && req.user.id) {
      templateData.updated_by = req.user.id;
      templateData.updated_at = Date.now();
    }
    
    const oldTemplate = await templateService.getTemplateById(id);
    const updatedTemplate = await templateService.updateTemplate(
      req.params.id,
      templateData
    );
    if (!updatedTemplate) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.TEMPLATE_NOT_FOUND)
      );
    }
    await logTemplateAudit({
      action: AuditActions.UPDATE,
      record_id: updatedTemplate.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldTemplate),
      new_value: JSON.stringify(updatedTemplate),
      description: TemplateAuditDescriptions.TEMPLATE_UPDATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.TEMPLATE_UPDATED_SUCCESSFULLY, updatedTemplate)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_TEMPLATE);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Soft delete a template by ID
 * @route DELETE /v1/template/:id
 * <AUTHOR>
 */
export const deleteTemplate = async (req, res) => {
  logger.info(loggerMessages.DELETING_TEMPLATE);
  try {
    const deletedTemplate = await templateService.deleteTemplate(req.params.id);
    if (!deletedTemplate) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.TEMPLATE_NOT_FOUND)
      );
    }
    await logTemplateAudit({
      action: AuditActions.DELETE,
      record_id: deletedTemplate.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(deletedTemplate),
      description: TemplateAuditDescriptions.TEMPLATE_DELETED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.TEMPLATE_DELETED_SUCCESSFULLY, deletedTemplate)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_TEMPLATE);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
}; 