import React, { useState, useEffect, useMemo, useCallback } from "react";
import * as PatientConst from "@/Constants/PatientRegistry";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import styles from '../CommonComponents/PhoneInput.module.css';
import { normalizePhoneForAPI, validatePhoneNumber } from '@/utils/phoneUtils';
import {
  FORM_TITLES,
  FORM_BUTTONS,
  FIELD_TYPES,
  FORM_VALIDATION,
} from "@/Constants/CommonComponents";
import { <PERSON>, Doctor } from "@/app/patient-registry/page";
import { parseDateForInput } from "@/utils/dateUtils";

interface FormField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  defaultValue?: string;
  validation?: (value: string) => string | null;
  gridCols?: number;
  isMulti?: boolean;
  country?: string;
  enableAreaCodes?: boolean;
}

interface FormData {
  [key: string]: string | string[] | Date;
}

// Type guards for form data
const isString = (value: string | string[] | Date): value is string => typeof value === 'string';
const isStringArray = (value: string | string[] | Date): value is string[] => Array.isArray(value);
const isDate = (value: string | string[] | Date): value is Date => value instanceof Date;

// Helper function to safely convert form value to string
const getFormValueAsString = (value: string | string[] | Date): string => {
  if (isString(value)) return value;
  if (isStringArray(value)) return value.join(', ');
  if (isDate(value)) return value.toISOString().split('T')[0];
  return '';
};

interface PatientFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Record<string, unknown>) => void;
  fields: FormField[];
  clinics: Clinic[];
  doctors: Doctor[];
  initialData?: Record<string, string | Date>;
  title?: string;
  submitButtonText?: string;
}

const PatientForm: React.FC<PatientFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  fields: originalFields,
  clinics,
  doctors,
  initialData = {},
  title,
  submitButtonText,
}) => {
  // Memoize clinic and doctor options to prevent unnecessary re-renders
  const clinicOptions = useMemo(
    () =>
      clinics.map((clinic) => ({
        value: String(clinic.id),
        label: clinic.clinic_name,
        id: clinic.id,
      })),
    [clinics]
  );

  const doctorOptions = useMemo(
    () =>
      doctors.map((doctor) => ({
        value: String(doctor.id),
        label: doctor.doctor_name,
        id: doctor.id,
        clinic_id: doctor.clinic_id,
      })),
    [doctors]
  );

  // Memoize fields array to prevent recreation on every render
  const fields = useMemo(
    () => [
      ...originalFields,
      {
        name: PatientConst.FIELD_CLINIC_ID,
        label: PatientConst.LABEL_CLINIC,
        type: FIELD_TYPES.SELECT,
        required: true,
        gridCols: 2,
        options: clinicOptions,
      },
      {
        name: PatientConst.FIELD_DOCTOR_IDS,
        label: PatientConst.LABEL_DOCTORS,
        type: FIELD_TYPES.SELECT,
        required: true,
        isMulti: true,
        gridCols: 2,
        options: doctorOptions,
      },
      {
        name: PatientConst.FIELD_TAGS,
        label: PatientConst.LABEL_TAGS,
        type: FIELD_TYPES.TEXT,
        required: false,
      },
      {
        name: "last_visit_summary",
        label: PatientConst.PATIENT_FORM_FIELDS.LAST_VISIT_SUMMARY,
        type: FIELD_TYPES.TEXTAREA,
        required: false,
        placeholder: PatientConst.PATIENT_FORM_PLACEHOLDERS.LAST_VISIT_SUMMARY,
        gridCols: 2,
      },
    ],
    [originalFields, clinicOptions, doctorOptions]
  );

  const [formData, setFormData] = useState<FormData>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isInitialized, setIsInitialized] = useState(false);

  // Function to initialize form data
  const initializeFormData = useCallback((initialData: Record<string, string | Date>) => {
    const defaultData: FormData = {};
    fields.forEach((field) => {
      defaultData[field.name] =
        initialData[field.name] || field.defaultValue || "";
    });

    // Handle clinic_id - ensure it's a string
    defaultData.clinic_id = initialData.clinic_id
      ? String(initialData.clinic_id)
      : "";

    // Handle doctor_ids - ensure it's an array of strings for checkboxes
    if (initialData.doctors) {
      if (Array.isArray(initialData.doctors)) {
        defaultData.doctor_ids = initialData.doctors.map((id: string | number) =>
          String(id)
        );
      } else {
        defaultData.doctor_ids = [String(initialData.doctor_ids)];
      }
    } else {
      defaultData.doctor_ids = [];
    }

    // Handle tags properly - convert array to string if needed
    const initialTags = initialData.tags;
    if (Array.isArray(initialTags)) {
      defaultData.tags = initialTags.join(", ");
    } else {
      defaultData.tags = initialTags || "";
    }

    // Handle date fields - ensure proper format for input type="date"
    if (initialData.dob) {
      defaultData.dob = parseDateForInput(initialData.dob);
    }

    return defaultData;
  }, [fields]);

  // Initialize form data when component mounts or when initialData changes (but only if initialData exists)
  useEffect(() => {
    // Only initialize if we have initialData or if we haven't initialized yet
    if (initialData && Object.keys(initialData).length > 0) {
      const newFormData = initializeFormData(initialData);
      setFormData(newFormData);
      setErrors({});
      setIsInitialized(true);
    } else if (!isInitialized) {
      // Initialize with empty data only once if no initialData
      const newFormData = initializeFormData({});
      setFormData(newFormData);
      setIsInitialized(true);
    }
  }, [initialData, isInitialized, fields, initializeFormData]); // Include initializeFormData in dependencies

  const validateField = (name: string, value: string): string | null => {
    const field = fields.find((f) => f.name === name);
    if (!field) return null;
    if (
      field.required &&
      name !== PatientConst.FIELD_CLINIC_ID &&
      name !== PatientConst.FIELD_DOCTOR_IDS &&
      !value.trim()
    ) {
      return FORM_VALIDATION.REQUIRED;
    }
    if (field.validation) {
      return field.validation(value);
    }
    if (field.type === FIELD_TYPES.EMAIL && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return FORM_VALIDATION.INVALID_EMAIL;
      }
    }
    if (field.type === FIELD_TYPES.NUMBER && value) {
      if (isNaN(Number(value)) || Number(value) < 0) {
        return FORM_VALIDATION.INVALID_NUMBER;
      }
    }
    if (field.type === FIELD_TYPES.PHONE && value) {
      const phoneValidation = validatePhoneNumber(value);
      if (!phoneValidation.isValid) {
        return phoneValidation.error || 'Invalid phone number';
      }
    }
    return null;
  };

  const handleFieldChange = (name: string, value: string | string[] | Date) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
    // Reset doctors if clinic changes
    if (name === "clinic_id") {
      setFormData((prev) => ({ ...prev, doctor_ids: [] }));
    }
  };

  const handleFieldBlur = (name: string, value: string) => {
    const error = validateField(name, value);
    if (error) {
      setErrors((prev) => ({ ...prev, [name]: error }));
    }
  };

  const handleDoctorCheckboxChange = (doctorId: string, isChecked: boolean) => {
    setFormData((prev) => {
      const currentDoctorIds = Array.isArray(prev.doctor_ids)
        ? [...prev.doctor_ids]
        : [];
      let updatedDoctorIds;

      if (isChecked) {
        // Only add if not already present
        if (!currentDoctorIds.includes(doctorId)) {
          updatedDoctorIds = [...currentDoctorIds, doctorId];
        } else {
          updatedDoctorIds = currentDoctorIds;
        }
      } else {
        updatedDoctorIds = currentDoctorIds.filter((id) => id !== doctorId);
      }

      return { ...prev, doctor_ids: updatedDoctorIds };
    });

    if (errors.doctor_ids) {
      setErrors((prev) => ({ ...prev, doctor_ids: "" }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    fields.forEach((field) => {
      if (field.name !== PatientConst.FIELD_CLINIC_ID && field.name !== PatientConst.FIELD_DOCTOR_IDS) {
        const fieldValue = formData[field.name];
        if (isString(fieldValue)) {
          const error = validateField(field.name, fieldValue);
          if (error) {
            newErrors[field.name] = error;
            hasErrors = true;
          }
        }
      }
    });

    if (!formData.clinic_id) {
      newErrors.clinic_id = FORM_VALIDATION.REQUIRED;
      hasErrors = true;
    }

    const doctorIds = formData.doctor_ids;
    if (!isStringArray(doctorIds) || doctorIds.length === 0) {
      newErrors.doctor_ids = PatientConst.PLEASE_SELECT_AT_LEAST_ONE_DOCTOR;
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Process tags from comma-separated string to array
    let tagsArray: string[] = [];
    if (formData.tags) {
      if (Array.isArray(formData.tags)) {
        // If it's already an array, use it directly
        tagsArray = formData.tags.filter((tag: string) => tag.trim() !== "");
      } else if (typeof formData.tags === "string") {
        // If it's a string, split it
        tagsArray = formData.tags
          .split(",")
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag !== "");
      }
    }

    const processedData = {
      first_name: isString(formData.first_name) ? formData.first_name : "",
      last_name: isString(formData.last_name) ? formData.last_name : "",
      dob: getFormValueAsString(formData.dob),
      clinic_id: isString(formData.clinic_id) ? formData.clinic_id : "",
      doctors: isStringArray(formData.doctor_ids) ? formData.doctor_ids.map((id: string) => parseInt(id, 10)) : [],
      gender: isString(formData.gender) ? formData.gender : "",
      phone_number: normalizePhoneForAPI(isString(formData.phone_number) ? formData.phone_number : ""),
      address: isString(formData.address) ? formData.address : "",
      email: isString(formData.email) ? formData.email : "",
      tags: tagsArray,
    };

    onSubmit(processedData);

    // Reset form to initial state
    const resetData = initializeFormData({});
    setFormData(resetData);
    setErrors({});
    onClose();
  };

  const renderField = (field: FormField) => {
    const fieldError = errors[field.name];
    const errorClass = fieldError ? "border-red-500" : "border-gray-200";
    const inputClass = errorClass;
    const selectTextareaClass = `w-full p-2 border rounded-md ${errorClass}`;
    const commonProps = {
      id: field.name,
      value: getFormValueAsString(formData[field.name]),
      onChange: (
        e: React.ChangeEvent<
          HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
        >
      ) => handleFieldChange(field.name, e.target.value),
      required: field.required,
      placeholder: field.placeholder,
    };

    // Custom rendering for clinics, doctors, tags
    if (field.name === PatientConst.FIELD_CLINIC_ID) {
      return (
        <select
          id="clinic_id"
          value={isString(formData.clinic_id) ? formData.clinic_id : ""}
          onChange={(e) => handleFieldChange("clinic_id", e.target.value)}
          className={selectTextareaClass}
          required
        >
          <option value="">{PatientConst.SELECT_CLINIC_PLACEHOLDER}</option>
          {clinicOptions.map((clinic) => (
            <option key={clinic.id} value={clinic.value}>
              {clinic.label}
            </option>
          ))}
        </select>
      );
    }

    if (field.name === PatientConst.FIELD_DOCTOR_IDS) {
      const filteredDoctors = doctorOptions.filter((doc) =>
        isString(formData.clinic_id) && formData.clinic_id
          ? doc.clinic_id === parseInt(formData.clinic_id, 10)
          : true
      );

      const selectedDoctorIds = Array.isArray(formData.doctor_ids)
        ? formData.doctor_ids
        : [];

      return (
        <div
          className={`border rounded-md p-3 ${errorClass} ${
            !formData.clinic_id ? "bg-gray-100" : ""
          }`}
        >
          {!formData.clinic_id ? (
            <p className="text-gray-500 text-sm">
              {PatientConst.PLEASE_SELECT_CLINIC_FIRST}
            </p>
          ) : filteredDoctors.length === 0 ? (
            <p className="text-gray-500 text-sm">
              {PatientConst.NO_DOCTORS_FOR_CLINIC}
            </p>
          ) : (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {filteredDoctors.map((doctor) => {
                const isChecked = selectedDoctorIds.includes(doctor.value);
                return (
                  <label
                    key={doctor.id}
                    className="flex items-center space-x-2 cursor-pointer"
                  >
                    <input
                      type="checkbox"
                      checked={isChecked}
                      onChange={(e) => {
                        handleDoctorCheckboxChange(
                          doctor.value,
                          e.target.checked
                        );
                      }}
                      className="rounded border-gray-300"
                      disabled={!formData.clinic_id}
                    />
                    <span className="text-sm">{doctor.label}</span>
                  </label>
                );
              })}
            </div>
          )}
        </div>
      );
    }

    if (field.name === PatientConst.FIELD_TAGS) {
      return (
        <Input
          id="tags"
          value={isString(formData.tags) ? formData.tags : ""}
          onChange={(e) => handleFieldChange(PatientConst.FIELD_TAGS, e.target.value)}
          placeholder={PatientConst.TAGS_PLACEHOLDER}
          className={inputClass}
        />
      );
    }

    switch (field.type) {
      case FIELD_TYPES.SELECT:
        return (
          <select {...commonProps} className={selectTextareaClass}>
            <option value="">
              {PatientConst.SELECT_PLACEHOLDER_PREFIX}
              {field.label}
            </option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      case FIELD_TYPES.TEXTAREA:
        return (
          <Textarea {...commonProps} rows={3} className={selectTextareaClass} />
        );
      case FIELD_TYPES.PHONE:
        const initialCountry = field.country || 'us'; // Use field country or default to 'us'
        const phoneErrorClass = fieldError ? styles.error : '';
        return (
          <div className={`${styles.phoneInputContainer} ${phoneErrorClass}`}>
            <PhoneInput
              country={initialCountry}
              value={getFormValueAsString(formData[field.name])}
              onChange={(phone: string) => handleFieldChange(field.name, phone)}
              onBlur={() => handleFieldBlur(field.name, getFormValueAsString(formData[field.name]))}
              placeholder={field.placeholder}
              enableAreaCodes={field.enableAreaCodes || false}
              inputProps={{
                required: field.required,
              }}
              enableSearch={true}
              disableSearchIcon={false}
              searchPlaceholder="Search country..."
              preferredCountries={['us', 'in', 'gb', 'ca', 'au']}
            />
          </div>
        );
      default:
        return (
          <Input {...commonProps} className={inputClass} type={field.type} />
        );
    }
  };

  const renderFieldGroup = (fieldGroup: FormField[]) => {
    if (fieldGroup.length === 1) {
      const field = fieldGroup[0];
      return (
        <div key={field.name}>
          <Label htmlFor={field.name} className="font-semibold">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {renderField(field)}
          {errors[field.name] && (
            <p className="text-red-500 text-sm mt-1">{errors[field.name]}</p>
          )}
        </div>
      );
    }
    return (
      <div
        key={fieldGroup[0].name}
        className={`grid grid-cols-${fieldGroup[0].gridCols || 2} gap-4`}
      >
        {fieldGroup.map((field) => (
          <div key={field.name}>
            <Label htmlFor={field.name} className="font-semibold">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            {renderField(field)}
            {errors[field.name] && (
              <p className="text-red-500 text-sm mt-1">{errors[field.name]}</p>
            )}
          </div>
        ))}
      </div>
    );
  };

  // Group fields by grid layout
  const groupedFields: FormField[][] = [];
  let i = 0;
  while (i < fields.length) {
    const currentField = fields[i];
    const nextField = fields[i + 1];
    if (
      currentField.gridCols &&
      currentField.gridCols > 1 &&
      nextField &&
      nextField.gridCols &&
      nextField.gridCols > 1
    ) {
      groupedFields.push([currentField, nextField]);
      i += 2;
    } else if (currentField.gridCols && currentField.gridCols > 1) {
      groupedFields.push([currentField]);
      i += 1;
    } else {
      groupedFields.push([currentField]);
      i += 1;
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>{title || FORM_TITLES.ADD_PATIENT}</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={handleSubmit}
          className="space-y-4 p-2 max-h-[80vh] overflow-y-auto"
        >
          {groupedFields.map((fieldGroup) => renderFieldGroup(fieldGroup))}
          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              {FORM_BUTTONS.CANCEL}
            </Button>
            <Button type="submit" className="flex-1 bg-black text-white" variant="main">
              {submitButtonText || FORM_BUTTONS.ADD_PATIENT}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default PatientForm;
