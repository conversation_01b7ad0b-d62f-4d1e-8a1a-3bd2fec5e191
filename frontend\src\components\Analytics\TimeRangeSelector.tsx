import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ANALYTICS_TIME_RANGE_24H,
  ANALYTICS_TIME_RANGE_7D,
  ANALYTICS_TIME_RANGE_30D,
  ANALYTICS_TIME_RANGE_90D,
} from "@/Constants/Analytics";

// Props for the TimeRangeSelector component
interface TimeRangeSelectorProps {
  timeRange: string; // Currently selected time range value
  onTimeRangeChange: (range: string) => void; // Handler for changing time range
}

/**
 * Renders a set of buttons to select the analytics time range.
 * Calls onTimeRangeChange when a button is clicked.
 */
const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({
  timeRange,
  onTimeRangeChange,
}) => {
  // List of available time ranges
  const timeRanges = [
    { value: "24h", label: ANALYTICS_TIME_RANGE_24H },
    { value: "7d", label: ANALYTICS_TIME_RANGE_7D },
    { value: "30d", label: ANALYTICS_TIME_RANGE_30D },
    { value: "90d", label: ANALYTICS_TIME_RANGE_90D },
  ];

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex gap-2">
          {/* Render a button for each time range */}
          {timeRanges.map((range) => (
            <Button
              key={range.value}
              size="sm"
              onClick={() => onTimeRangeChange(range.value)}
              className={
                timeRange === range.value
                  ? 'bg-black text-white font-semibold rounded-lg shadow-none border-none'
                  : 'bg-white text-black font-semibold rounded-lg border border-gray-200 shadow-none'
              }
            >
              {range.label}
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TimeRangeSelector;
