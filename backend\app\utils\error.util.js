import * as status from './status_code.utils.js';
import { errorResponse } from './response.util.js';

/**
 * Custom error class for application errors
 */
export class AppError extends Error {
  constructor(message, statusCode, errorCode = null) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Handle Sequelize errors and convert to AppError
 */
export const handleSequelizeError = (error) => {
  if (error.name === 'SequelizeValidationError') {
    return new AppError(
      'Validation error',
      status.STATUS_CODE_BAD_REQUEST,
      'VALIDATION_ERROR'
    );
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    return new AppError(
      'Duplicate entry',
      status.STATUS_CODE_BAD_REQUEST,
      'DUPLICATE_ENTRY'
    );
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return new AppError(
      'Foreign key constraint error',
      status.STATUS_CODE_BAD_REQUEST,
      'FOREIGN_KEY_ERROR'
    );
  }

  if (error.name === 'SequelizeConnectionError') {
    return new AppError(
      'Database connection error',
      status.STATUS_CODE_INTERNAL_SERVER_STATUS,
      'DATABASE_ERROR'
    );
  }

  return error;
};

/**
 * Handle JWT errors and convert to AppError
 */
export const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new AppError(
      'Invalid token',
      status.STATUS_CODE_UNAUTHORIZED,
      'INVALID_TOKEN'
    );
  }

  if (error.name === 'TokenExpiredError') {
    return new AppError(
      'Token expired',
      status.STATUS_CODE_UNAUTHORIZED,
      'TOKEN_EXPIRED'
    );
  }

  return error;
};

/**
 * Async error handler wrapper
 */
export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Error response formatter
 */
export const formatErrorResponse = (error, req) => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  const response = {
    success: false,
    message: error.message || 'Internal server error',
    error: error.errorCode || 'INTERNAL_SERVER_ERROR',
    ...(isDevelopment && {
      stack: error.stack,
      path: req?.originalUrl,
      method: req?.method,
    }),
  };

  // Add validation details if available
  if (error.details) {
    response.details = error.details;
  }

  return response;
};
