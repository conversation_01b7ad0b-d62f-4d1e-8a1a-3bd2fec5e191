// RecentCallActivityTable.tsx
// Renders a table of recent call activities for the Reactivation Program, with search and filter controls.
// Shows patient, contact, visit, call details, outcome, and notes.

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Filter } from 'lucide-react';
import Table from '@/components/CommonComponents/Table';
import { reactivationCallTableColumns } from '@/utils/column';
import {
  REACTIVATION_PROGRAM_RECENT_ACTIVITY,
  REACTIVATION_PROGRAM_SEARCH_PLACEHOLDER,
  REACTIVATION_PROGRAM_FILTER,
} from '@/Constants/ReactivationProgram';

/**
 * Represents a single call log entry for the recent call activity table.
 * @property id - Unique identifier for the call log
 * @property patientName - Name of the patient
 * @property phone - Patient's phone number
 * @property lastVisit - Last visit date
 * @property callDate - Date of the call
 * @property callTime - Time of the call
 * @property outcome - Outcome of the call
 * @property appointmentDate - Appointment date if booked
 * @property notes - Additional notes
 */
interface CallLog {
  id: number;
  patientName: string;
  phone: string;
  lastVisit: string;
  callDate: string;
  callTime: string;
  outcome: string;
  appointmentDate: string | null;
  notes: string;
}

/**
 * Props for the RecentCallActivityTable component
 * @property filteredCalls - Array of filtered call logs
 * @property searchTerm - Current search term
 * @property setSearchTerm - Handler to update search term
 */
interface RecentCallActivityTableProps {
  filteredCalls: CallLog[];
  searchTerm: string;
  setSearchTerm: (val: string) => void;
}

/**
 * Renders a table of recent call activities for the Reactivation Program.
 * Includes search and filter controls.
 */
const RecentCallActivityTable: React.FC<RecentCallActivityTableProps> = ({ filteredCalls, searchTerm, setSearchTerm }) => (
  <Card>
    <CardHeader>
      <div className="flex items-center justify-between">
        <CardTitle>{REACTIVATION_PROGRAM_RECENT_ACTIVITY}</CardTitle>
        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={REACTIVATION_PROGRAM_SEARCH_PLACEHOLDER}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64 border border-gray-200"
            />
          </div>
          <Button variant="outline" size="sm" className="border border-gray-200 text-black">
            <Filter className="h-4 w-4 mr-2" />
            {REACTIVATION_PROGRAM_FILTER}
          </Button>
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <Table
        columns={reactivationCallTableColumns}
        data={filteredCalls as unknown as Record<string, unknown>[]}
      />
    </CardContent>
  </Card>
);

export default RecentCallActivityTable; 