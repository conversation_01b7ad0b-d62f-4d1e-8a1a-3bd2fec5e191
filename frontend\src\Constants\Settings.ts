export const SETTINGS_PAGE_TITLE = "Settings";
export const SETTINGS_PAGE_SUBTITLE = "Manage system preferences and configurations";
export const SETTINGS_SAVE_CHANGES = "Save Changes";

// Profile Section
export const SETTINGS_PROFILE_TITLE = "Profile Settings";
export const SETTINGS_PROFILE_DEFAULT_NAME = "Dr. <PERSON>";
export const SETTINGS_PROFILE_DEFAULT_EMAIL = "<EMAIL>";
export const SETTINGS_PROFILE_DEFAULT_PHONE = "+****************";
export const SETTINGS_PROFILE_ROLE = "Clinic Administrator";
export const SETTINGS_PROFILE_CHANGE_PASSWORD = "Change Password";
export const SETTINGS_PROFILE_CURRENT_PASSWORD = "Current Password";
export const SETTINGS_PROFILE_NEW_PASSWORD = "New Password";

// Notification Section
export const SETTINGS_NOTIFICATION_TITLE = "Notification Preferences";
export const SETTINGS_NOTIFICATION_APPOINTMENT = "Appointment Notifications";
export const SETTINGS_NOTIFICATION_APPOINTMENT_DESC = "Get notified about new appointments and changes";
export const SETTINGS_NOTIFICATION_AI_CALLS = "AI Call Alerts";
export const SETTINGS_NOTIFICATION_AI_CALLS_DESC = "Notifications for AI agent activity and outcomes";
export const SETTINGS_NOTIFICATION_REACTIVATION = "Reactivation Updates";
export const SETTINGS_NOTIFICATION_REACTIVATION_DESC = "Updates on reactivation campaign progress";
export const SETTINGS_NOTIFICATION_SYSTEM = "System Alerts";
export const SETTINGS_NOTIFICATION_SYSTEM_DESC = "Important system notifications and updates";
export const SETTINGS_NOTIFICATION_DEFAULTS = {
  appointments: true,
  aiCalls: true,
  reactivation: false,
  system: true,
};

// AI Agent Section
export const SETTINGS_AI_AGENT_TITLE = "AI Agent Configuration";
export const SETTINGS_AI_AGENT_VOICE = "Voice Interaction";
export const SETTINGS_AI_AGENT_VOICE_DESC = "Enable voice-based AI interactions";
export const SETTINGS_AI_AGENT_AUTO_REACTIVATION = "Auto Reactivation";
export const SETTINGS_AI_AGENT_AUTO_REACTIVATION_DESC = "Automatically trigger reactivation calls";
export const SETTINGS_AI_AGENT_CALL_RECORDING = "Call Recording";
export const SETTINGS_AI_AGENT_CALL_RECORDING_DESC = "Record AI agent conversations";
export const SETTINGS_AI_AGENT_SMART_SCHEDULING = "Smart Scheduling";
export const SETTINGS_AI_AGENT_SMART_SCHEDULING_DESC = "AI-powered appointment optimization";
export const SETTINGS_AI_AGENT_DEFAULTS = {
  voiceEnabled: true,
  autoReactivation: true,
  callRecording: false,
  smartScheduling: true,
};
export const SETTINGS_AI_AGENT_VOICE_SETTINGS = "Voice Settings";
export const SETTINGS_AI_AGENT_VOICE_SPEED = "Voice Speed";
export const SETTINGS_AI_AGENT_VOICE_VOLUME = "Voice Volume";

// Business Hours Section
export const SETTINGS_BUSINESS_HOURS_TITLE = "Business Hours Configuration";
export const SETTINGS_BUSINESS_HOURS_DAYS = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];
export const SETTINGS_BUSINESS_HOURS_DEFAULT = {
  start: "09:00",
  end: "17:00",
  closed: "Sunday",
};

// System Information Section
export const SETTINGS_SYSTEM_INFO_TITLE = "System Information";
export const SETTINGS_SYSTEM_INFO_VERSION = "System Version:";
export const SETTINGS_SYSTEM_INFO_VERSION_VALUE = "v2.1.0";
export const SETTINGS_SYSTEM_INFO_LAST_UPDATE = "Last Update:";
export const SETTINGS_SYSTEM_INFO_LAST_UPDATE_VALUE = "Jan 15, 2024";
export const SETTINGS_SYSTEM_INFO_DB_SIZE = "Database Size:";
export const SETTINGS_SYSTEM_INFO_DB_SIZE_VALUE = "2.4 GB";
export const SETTINGS_SYSTEM_INFO_ACTIVE_USERS = "Active Users:";
export const SETTINGS_SYSTEM_INFO_ACTIVE_USERS_VALUE = "4";
export const SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS = "AI Agent Status:";
export const SETTINGS_SYSTEM_INFO_AI_AGENT_STATUS_VALUE = "Online";
export const SETTINGS_SYSTEM_INFO_BACKUP_STATUS = "Backup Status:";
export const SETTINGS_SYSTEM_INFO_BACKUP_STATUS_VALUE = "Daily";
export const SETTINGS_SYSTEM_INFO_SECURITY_SETTINGS = "Security Settings";
export const SETTINGS_SYSTEM_INFO_BACKUP_NOW = "Backup Now";
