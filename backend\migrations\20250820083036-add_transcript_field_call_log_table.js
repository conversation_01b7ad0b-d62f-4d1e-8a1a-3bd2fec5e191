export const up = async (queryInterface, Sequelize) => {
  await queryInterface.addColumn('ai_call_logs', 'transcript', {
    type: Sequelize.ARRAY(Sequelize.TEXT),
    allowNull: true,
    comment: 'Transcript of the call conversation as an array of text segments'
  });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('ai_call_logs', 'transcript');
};
