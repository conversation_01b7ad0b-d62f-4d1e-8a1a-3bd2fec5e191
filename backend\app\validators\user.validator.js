import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';
import { PASSWORD_REGEX } from '../utils/pattern.utils.js';
/**
 * Validation schema for creating a user
 * <AUTHOR>
 */
export const createUserSchema = [
  // Validate firstName
  body('firstName').notEmpty().withMessage(constants.FIRST_NAME_REQUIRED),

  // Validate lastName
  body('lastName').notEmpty().withMessage(constants.LAST_NAME_REQUIRED),

  // Validate email
  body('email')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isEmail()
    .withMessage(constants.INVALID_EMAIL),

  // Validate phoneNumber
  body('phoneNumber')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isMobilePhone()
    .withMessage(constants.INVALID_PHONE_NUMBER)
    .isLength({ max: 10 })
    .withMessage(constants.PHONE_NUMBER_MIN_LENGTH),

  // Validate password
  body('password')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(constants.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(constants.INVALID_PASSWORD_FORMAT),
];

/**
 * Validation schema for updating a user
 * All fields are optional, but if present, must be valid
 */
export const updateUserSchema = [
  body('name')
    .optional()
    .notEmpty()
    .withMessage(constants.NAME_REQUIRED),

  body('email')
    .optional()
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isEmail()
    .withMessage(constants.INVALID_EMAIL),

  body('user_phonenumber')
    .optional()
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isMobilePhone()
    .withMessage(constants.INVALID_PHONE_NUMBER)
    .isLength({ max: 10 })
    .withMessage(constants.PHONE_NUMBER_MIN_LENGTH),

  body('password')
    .optional()
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(constants.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(constants.INVALID_PASSWORD_FORMAT),
];


