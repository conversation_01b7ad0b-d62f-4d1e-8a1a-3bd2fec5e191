"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/contexts/AuthContext";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Loader2,
  Mail,
  Lock,
  User,
  Phone,
  ShieldCheck,
  Eye,
  EyeOff,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toastSuccess, toastError } from "@/hooks/use-toast";
import {
  REGISTER_CONSTANTS,
  ROLE_OPTIONS,
  VALIDATION_MESSAGES,
  TOAST_MESSAGES,
  FORM_FIELDS,
  FORM_IDS,
  R<PERSON><PERSON>_VALUES,
  DEFAULT_VALUES,
  STYLES,
  ACCESSIBILITY,
  ASSETS,
  ANIMATION,
  INPUT_TYPES,
  BUTTON_TYPES,
  FORM_ATTRIBUTES,
} from "@/Constants/Register";
import { ERROR_MESSAGES } from "@/Constants/HooksAPI";
import Image from "next/image";
import Link from "next/link";
import { motion } from "framer-motion";
import styles from "./Register.module.css";
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import phoneStyles from '@/components/CommonComponents/PhoneInput.module.css';

// Form validation schema
const registerSchema = z
  .object({
    [FORM_FIELDS.NAME]: z.string().min(2, VALIDATION_MESSAGES.NAME_MIN_LENGTH),
    [FORM_FIELDS.EMAIL]: z.string().email(VALIDATION_MESSAGES.EMAIL_INVALID),
    [FORM_FIELDS.PASSWORD]: z
      .string()
      .min(8, VALIDATION_MESSAGES.PASSWORD_MIN_LENGTH),
    [FORM_FIELDS.CONFIRM_PASSWORD]: z.string(),
    [FORM_FIELDS.PHONE]: z
      .string()
      .min(8, VALIDATION_MESSAGES.PHONE_MIN_LENGTH)
      .refine((value) => {
        // Phone input includes country code, so we need to validate accordingly
        // Remove any non-digit characters and check if it's a valid phone number
        const digitsOnly = value.replace(/\D/g, '');
        return digitsOnly.length >= 8; // Minimum 8 digits including country code
      }, "Please enter a valid phone number"),
    [FORM_FIELDS.ROLE]: z.enum([
      ROLE_VALUES.SUPER_ADMIN,
      ROLE_VALUES.CLINIC_ADMIN,
      ROLE_VALUES.FRONT_DESK,
      ROLE_VALUES.AGENT,
    ]),
  })
  .refine(
    (data) => data[FORM_FIELDS.PASSWORD] === data[FORM_FIELDS.CONFIRM_PASSWORD],
    {
      message: VALIDATION_MESSAGES.PASSWORDS_DONT_MATCH,
      path: [FORM_FIELDS.CONFIRM_PASSWORD],
    }
  );

type RegisterFormData = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const { register: registerUser, loading } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      [FORM_FIELDS.ROLE]: DEFAULT_VALUES.ROLE,
    },
  });

  const watchedRole = watch(FORM_FIELDS.ROLE);

  const onSubmit = async (data: RegisterFormData) => {
    try {
      const result = await registerUser(
        data[FORM_FIELDS.NAME],
        data[FORM_FIELDS.EMAIL],
        data[FORM_FIELDS.PASSWORD],
        data[FORM_FIELDS.PHONE],
        data[FORM_FIELDS.ROLE],
        DEFAULT_VALUES.CLINIC_ID, // clinic_id set to null
        () => {
          toastSuccess(TOAST_MESSAGES.REGISTRATION_SUCCESS);
          router.push("/login");
        }
      );
      if (result.success) {
        toastSuccess(TOAST_MESSAGES.REGISTRATION_SUCCESS);
        router.push("/login");
      } else {
        toastError(result.error || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      }
    } catch {
      toastError(ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
    }
  };

  return (
    <div className={styles.registerContainer}>
      <motion.div
        initial={{
          opacity: ANIMATION.INITIAL_OPACITY,
          scale: ANIMATION.INITIAL_SCALE,
        }}
        animate={{
          opacity: ANIMATION.FINAL_OPACITY,
          scale: ANIMATION.FINAL_SCALE,
        }}
        transition={{ duration: ANIMATION.DURATION }}
        className={styles.registerMotion}
        style={{
          maxWidth: STYLES.CONTAINER_MAX_WIDTH,
          width: STYLES.CONTAINER_WIDTH,
        }}
      >
        <Card
          className={styles.registerCard}
          style={{
            maxWidth: STYLES.CONTAINER_MAX_WIDTH,
            width: STYLES.CONTAINER_WIDTH,
          }}
        >
          <CardHeader className={styles.registerHeader}>
            <Image
              src={ASSETS.LOGO_SRC}
              alt={ACCESSIBILITY.DENSY_AI_LOGO}
              width={ASSETS.LOGO_WIDTH}
              height={ASSETS.LOGO_HEIGHT}
              className={styles.registerLogo}
              priority
            />
            <p className={styles.registerSubtitle}>
              {REGISTER_CONSTANTS.SUBTITLE}
            </p>
          </CardHeader>
          <CardContent>
            <form
              className={styles.registerForm}
              onSubmit={handleSubmit(onSubmit)}
              autoComplete={FORM_ATTRIBUTES.AUTOCOMPLETE}
            >
              {/* Name Field */}
              <div>
                <Label htmlFor={FORM_IDS.NAME}>
                  {REGISTER_CONSTANTS.NAME_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <User size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.NAME}
                    type={INPUT_TYPES.TEXT}
                    placeholder={REGISTER_CONSTANTS.NAME_PLACEHOLDER}
                    className={styles.registerInput}
                    {...register(FORM_FIELDS.NAME)}
                  />
                </div>
                {errors[FORM_FIELDS.NAME] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.NAME]?.message}
                  </span>
                )}
              </div>

              {/* Role Field */}
              <div>
                <Label htmlFor={FORM_IDS.ROLE}>
                  {REGISTER_CONSTANTS.ROLE_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <ShieldCheck size={18} />
                  </span>
                  <Select
                    value={watchedRole}
                    onValueChange={(value: string) =>
                      setValue(
                        FORM_FIELDS.ROLE,
                        value as
                          | typeof ROLE_VALUES.SUPER_ADMIN
                          | typeof ROLE_VALUES.CLINIC_ADMIN
                          | typeof ROLE_VALUES.FRONT_DESK
                          | typeof ROLE_VALUES.AGENT
                      )
                    }
                  >
                    <SelectTrigger
                      className={styles.registerInput}
                      id={FORM_IDS.ROLE}
                    >
                      <SelectValue
                        placeholder={REGISTER_CONSTANTS.ROLE_LABEL}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {ROLE_OPTIONS.map((opt) => (
                        <SelectItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                {errors[FORM_FIELDS.ROLE] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.ROLE]?.message}
                  </span>
                )}
              </div>

              {/* Email Field */}
              <div>
                <Label htmlFor={FORM_IDS.EMAIL}>
                  {REGISTER_CONSTANTS.EMAIL_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <Mail size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.EMAIL}
                    type={INPUT_TYPES.EMAIL}
                    placeholder={REGISTER_CONSTANTS.EMAIL_PLACEHOLDER}
                    className={styles.registerInput}
                    {...register(FORM_FIELDS.EMAIL)}
                  />
                </div>
                {errors[FORM_FIELDS.EMAIL] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.EMAIL]?.message}
                  </span>
                )}
              </div>

              {/* Phone Field */}
              <div>
                <Label htmlFor={FORM_IDS.PHONE}>
                  {REGISTER_CONSTANTS.PHONE_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <Phone size={18} />
                  </span>
                  <div className={phoneStyles.phoneInputContainer}>
                    <PhoneInput
                      country="us"
                      value={watch(FORM_FIELDS.PHONE) || ''}
                      onChange={(phone: string) => setValue(FORM_FIELDS.PHONE, phone)}
                      placeholder={REGISTER_CONSTANTS.PHONE_PLACEHOLDER}
                      enableAreaCodes={true}
                      inputProps={{
                        id: FORM_IDS.PHONE,
                        required: true,
                      }}
                      enableSearch={true}
                      disableSearchIcon={false}
                      searchPlaceholder="Search country..."
                      preferredCountries={['us', 'in', 'gb', 'ca', 'au']}
                    />
                  </div>
                </div>
                {errors[FORM_FIELDS.PHONE] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.PHONE]?.message}
                  </span>
                )}
              </div>

              {/* Password Field */}
              <div>
                <Label htmlFor={FORM_IDS.PASSWORD}>
                  {REGISTER_CONSTANTS.PASSWORD_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <Lock size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.PASSWORD}
                    type={
                      showPassword ? INPUT_TYPES.TEXT : INPUT_TYPES.PASSWORD
                    }
                    placeholder={REGISTER_CONSTANTS.PASSWORD_PLACEHOLDER}
                    className={styles.registerInputPassword}
                    {...register(FORM_FIELDS.PASSWORD)}
                  />
                  <button
                    type={BUTTON_TYPES.BUTTON}
                    aria-label={
                      showPassword
                        ? ACCESSIBILITY.HIDE_PASSWORD
                        : ACCESSIBILITY.SHOW_PASSWORD
                    }
                    className={styles.registerPasswordToggle}
                    onClick={() => setShowPassword(!showPassword)}
                    tabIndex={-1}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors[FORM_FIELDS.PASSWORD] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.PASSWORD]?.message}
                  </span>
                )}
              </div>

              {/* Confirm Password Field */}
              <div>
                <Label htmlFor={FORM_IDS.CONFIRM_PASSWORD}>
                  {REGISTER_CONSTANTS.CONFIRM_PASSWORD_LABEL}
                </Label>
                <div className="relative mt-1">
                  <span className={styles.registerIcon}>
                    <Lock size={18} />
                  </span>
                  <Input
                    id={FORM_IDS.CONFIRM_PASSWORD}
                    type={
                      showConfirmPassword
                        ? INPUT_TYPES.TEXT
                        : INPUT_TYPES.PASSWORD
                    }
                    placeholder={
                      REGISTER_CONSTANTS.CONFIRM_PASSWORD_PLACEHOLDER
                    }
                    className={styles.registerInputPassword}
                    {...register(FORM_FIELDS.CONFIRM_PASSWORD)}
                  />
                  <button
                    type={BUTTON_TYPES.BUTTON}
                    aria-label={
                      showConfirmPassword
                        ? ACCESSIBILITY.HIDE_PASSWORD
                        : ACCESSIBILITY.SHOW_PASSWORD
                    }
                    className={styles.registerPasswordToggle}
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    tabIndex={-1}
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} />
                    ) : (
                      <Eye size={18} />
                    )}
                  </button>
                </div>
                {errors[FORM_FIELDS.CONFIRM_PASSWORD] && (
                  <span className={styles.registerError}>
                    {errors[FORM_FIELDS.CONFIRM_PASSWORD]?.message}
                  </span>
                )}
              </div>

              <Button
                type={BUTTON_TYPES.SUBMIT}
                className={styles.registerButton}
                disabled={isSubmitting || loading}
              >
                {isSubmitting || loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="animate-spin" size={18} />
                    {REGISTER_CONSTANTS.REGISTER}
                  </span>
                ) : (
                  REGISTER_CONSTANTS.REGISTER
                )}
              </Button>
            </form>
            <div className={styles.registerLogin}>
              <span>
                {REGISTER_CONSTANTS.ALREADY_HAVE_ACCOUNT}{" "}
                <Link href="/login" className={styles.registerLoginLink}>
                  {REGISTER_CONSTANTS.LOGIN_LINK}
                </Link>
              </span>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
