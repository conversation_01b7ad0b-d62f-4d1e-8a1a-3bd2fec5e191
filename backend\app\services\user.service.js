import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { User } from '../models/index.js';

/**
 * Get all users from the database
 * @returns {Promise<Array>} Array of user documents
 * <AUTHOR>
 */
export const getAllUsers = async () => {
  try {
    logger.info(loggerMessages.FETCHING_USERS);
    const users = await User.findAll();
    return users;
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_USERS, error);
    throw new Error(error.message);
  }
};

/**
 * Get a user by ID from the database
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} User document or null if not found
 * <AUTHOR>
 */
export const getUserById = async (id) => {
  try {
    logger.info(`${loggerMessages.FETCHING_USER_BY_ID}: ${id}`);
    const user = await User.findByPk(id);
    return user;
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_FETCHING_USER_BY_ID}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Update a user in the database by ID
 * @param {string} id - User ID
 * @param {Object} userData - User data to update
 * @returns {Promise<Object|null>} Updated user document or null if user not found
 * <AUTHOR>
 */
export const updateUser = async (id, userData) => {
  try {
    logger.info(`${loggerMessages.UPDATING_USER}: ${id}`);
    await User.update(userData, { where: { id } });
    const updatedUser = await User.findByPk(id);
    return updatedUser;
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_UPDATING_USER}: ${id}`, error);
    throw new Error(error.message);
  }
};

/**
 * Delete a user from the database by ID
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} Deleted user document or null if user not found
 * <AUTHOR>
 */
export const deleteUser = async (id) => {
  try {
    logger.info(`${loggerMessages.DELETING_USER}: ${id}`);
    const deletedUser = await User.destroy({ where: { id } });
    return deletedUser;
  } catch (error) {
    logger.error(`${loggerMessages.ERROR_DELETING_USER}: ${id}`, error);
    throw new Error(error.message);
  }
};
