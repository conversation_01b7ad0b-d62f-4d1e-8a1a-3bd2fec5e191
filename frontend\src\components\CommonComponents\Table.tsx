// Table.tsx
// Renders a styled table with custom headers and children rows.

import React from 'react';
import { Table as UITable } from "@/components/ui/table";

/**
 * Props for the Table component
 * @property columns - Array of column definitions { header, accessor, render? }
 * @property data - Array of row objects
 * @property className - Optional additional class names
 */
interface Column {
  header: string;
  accessor: string;
  render?: (row: Record<string, unknown>) => React.ReactNode;
  // Optional: for actions column
  isActions?: boolean;
}

interface TableProps {
  columns: Column[];
  data: Record<string, unknown>[];
  className?: string;
  // Optional: actions render function for each row
  onActions?: (row: Record<string, unknown>) => React.ReactNode;
  variant?: 'striped' | 'hover' | 'bordered'; // Add variant prop
}

/**
 * Renders a table with dynamic columns and data.
 * Used for displaying tabular data in a consistent style.
 */
const Table: React.FC<TableProps> = ({ columns, data, className = '', onActions, variant = 'striped' }) => (
  <div className={`overflow-x-auto ${className}`}>
    <UITable variant={variant}>
      <thead>
        <tr>
          {columns.map((col, idx) => (
            <th key={idx} className="text-left p-4 font-medium text-gray-600">{col.header}</th>
          ))}
          {/* If actions column is present, add header */}
          {onActions && <th className="text-left p-4 font-medium text-gray-600">Actions</th>}
        </tr>
      </thead>
      <tbody>
        {data.map((row, rowIdx) => (
          <tr key={(row as Record<string, unknown>).id as string || rowIdx}>
            {columns.map((col, colIdx) => {
              // Add specific styling for phone columns
              const isPhoneColumn = col.accessor.includes('phone');
              const isWorkingDaysColumn = col.accessor.includes('working_days');
              
              return (
                <td 
                  key={colIdx} 
                  className={`p-4 ${isPhoneColumn ? 'whitespace-nowrap' : ''} ${isWorkingDaysColumn ? 'max-w-32' : ''}`}
                >
                  {col.render ? col.render(row) : String((row as Record<string, unknown>)[col.accessor] ?? '-')}
                </td>
              );
            })}
            {/* Render actions if provided */}
            {onActions && (
              <td className="p-4 whitespace-nowrap">{onActions(row)}</td>
            )}
          </tr>
        ))}
      </tbody>
    </UITable>
  </div>
);

export default Table; 