import { useState, useCallback } from "react";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { apiRequest } from "@/utils/axios.utils";
import * as PatientRegistry from "@/Constants/PatientRegistry";

export interface Patient {
  id: string;
  clinic_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  last_visit: string | null; // ISO 8601 timestamp with timezone (e.g. UTC or user-specific), or null if not visited
  last_visit_summary: string | null; // Summary of the last visit
  tags: string[] | null; // Assuming it's an array if used later, or string/null
  preferences: Record<string, unknown> | null; // Object for patient preferences
  dob: string | null; // Date of Birth in ISO format
  gender: "male" | "female" | "other" | null;
  address: string | null;
  doctors: number[] | null; // Array of selected doctor IDs
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  is_deleted: boolean;
  is_active: boolean;
}

export function usePatients() {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = "/patient";
  const GET_USERS_URL = `${API_BASE}/?is_deleted=false`;
  const UPLOAD_API_URL = (clinicId: string | number) =>
    `${API_BASE}/upload/${clinicId}`;

  const getAllPatients = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(GET_USERS_URL)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setPatients(response.data as Patient[]);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED);
        return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, [GET_USERS_URL]);

  const getPatientById = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`${API_BASE}/${id}`)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setPatient(response.data as Patient);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED);
        return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const createPatient = useCallback(async (data: Omit<Patient, "id">) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.post(API_BASE, data)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setPatients((prev) => [...prev, response.data as Patient]);
        compactSuccessMessage(response.message || PatientRegistry.MESSAGES.PATIENT_ADDED_SUCCESSFULLY);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_ADD_FAILED);
        return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_ADD_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_ADD_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePatient = useCallback(
    async (data: Partial<Omit<Patient, "id">>, id: string) => {
      setLoading(true);
      setError(null);
      try {
        const response = (await apiRequest.put(`${API_BASE}/${id}`, data)) as { status: boolean; message?: string; data?: unknown };
        if (response && response.status) {
          setPatients((prev) =>
            prev.map((p) => (p.id === id ? response.data as Patient : p))
          );
          compactSuccessMessage(response.message || PatientRegistry.MESSAGES.PATIENT_UPDATED_SUCCESSFULLY);
          return { success: true, data: response.data };
        } else {
          compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_UPDATE_FAILED);
          return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_UPDATE_FAILED };
        }
      } catch (error: unknown) {
        const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_UPDATE_FAILED);
        compactErrorMessage(errorMsg);
        return { success: false, error: errorMsg };
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const uploadPatientList = async (clinicId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);
      const response = (await apiRequest.post(
        UPLOAD_API_URL(clinicId),
        formData,
        PatientRegistry.FILE_UPLOAD_HEADER
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || PatientRegistry.MESSAGES.PATIENT_UPLOAD_SUCCESSFULLY);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_UPLOAD_FAILED);
        return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_UPLOAD_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_UPLOAD_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const deletePatient = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.delete(`${API_BASE}/${id}`)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setPatients((prev) => prev.filter((p) => p.id !== id));
        compactSuccessMessage(response.message || PatientRegistry.MESSAGES.PATIENT_DELETED_SUCCESSFULLY);
        return { success: true };
      } else {
        compactErrorMessage(response?.message || PatientRegistry.MESSAGES.PATIENT_DELETE_FAILED);
        return { success: false, error: response?.message || PatientRegistry.MESSAGES.PATIENT_DELETE_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, PatientRegistry.MESSAGES.PATIENT_DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    patients,
    patient,
    loading,
    error,
    setLoading,
    setError,
    getAllPatients,
    getPatientById,
    createPatient,
    updatePatient,
    deletePatient,
    uploadPatientList,
  };
}
