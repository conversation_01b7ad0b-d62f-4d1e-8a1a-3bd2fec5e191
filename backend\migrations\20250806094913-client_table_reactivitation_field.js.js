'use strict';

/** @type {import('sequelize-cli').Migration} */
export const up = async (queryInterface, Sequelize) => {
  await queryInterface.addColumn('clinics', 'reactivation_days', {
    type: Sequelize.INTEGER,
    allowNull: true,
  });
  await queryInterface.addColumn('clinics', 'batch_call_time', {
    type: Sequelize.TIME,
    allowNull: true,
  });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('clinics', 'reactivation_days');
  await queryInterface.removeColumn('clinics', 'batch_call_time');
};
