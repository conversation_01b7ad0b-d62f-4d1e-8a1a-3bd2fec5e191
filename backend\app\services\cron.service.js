import cron from 'node-cron';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { Clinic } from '../models/index.js';
import { findInactivePatients, getAllPatients } from './patient.service.js';
import { submitElevenLabsBatchCall } from './elevenlabs.service.js';
import * as constants from '../utils/constants.utils.js';
import { Op } from 'sequelize';
import axios from 'axios';

// Configuration constants
const DEFAULT_DAYS = 90;
const FIVE_MINUTES_MS = 5 * 60 * 1000;
const API_TIMEOUT = 30000;

/**
 * Initialize the cron job for automated outbound calls
 */
export const initializeCronJobs = () => {
  logger.info(loggerMessages.INITIALIZING_CRON_JOBS);

  // Get cron schedules from environment variables or use defaults
  const schedules = {
    daily: process.env.CRON_DAILY_SCHEDULE || '0 0 * * *',
    continuous: process.env.CRON_CONTINUOUS_SCHEDULE || '0 */6 * * *',
    apiCall: process.env.CRON_API_CALL_SCHEDULE || '0 */2 * * *',
  };

  // Schedule cron jobs
  const cronJobs = [
    {
      name: 'Daily',
      schedule: schedules.daily,
      handler: processAllClinicsForOutboundCalls,
      description: constants.CRON_DAILY_DESCRIPTION,
    },
    {
      name: 'Continuous',
      schedule: schedules.continuous,
      handler: processContinuousOutboundCalls,
      description: constants.CRON_CONTINUOUS_DESCRIPTION,
    },
    {
      name: 'API Call',
      schedule: schedules.apiCall,
      handler: callOutboundAPIForAllClinics,
      description: constants.CRON_API_CALL_DESCRIPTION,
    },
  ];

  // Initialize all cron jobs
  cronJobs.forEach((job) => {
    cron.schedule(
      job.schedule,
      async () => {
        logger.info(loggerMessages.STARTING_CRON_JOB(job.name));
        try {
          await job.handler();
          logger.info(loggerMessages.CRON_JOB_COMPLETED(job.name));
        } catch (error) {
          logger.error(loggerMessages.ERROR_IN_CRON_JOB(job.name, error));
        }
      },
      { scheduled: true, timezone: constants.CRON_TIMEZONE }
    );
  });

  logger.info(loggerMessages.CRON_JOBS_INITIALIZED, schedules);
};

/**
 * Get all active clinics with optional filters
 * @param {Object} filters - Additional filters
 * @returns {Array} Array of clinics
 */
const getActiveClinics = async (filters = {}) => {
  const where = {
    is_active: true,
    is_deleted: false,
    ...filters,
  };

  return await Clinic.findAll({ where });
};

/**
 * Process patients and submit to ElevenLabs
 * @param {Array} patients - Array of patients
 * @param {string} time - Optional time parameter
 * @returns {Object} Result object
 */
const processPatientsAndSubmit = async (patients, time) => {
  if (!patients || patients.length === 0) {
    return { success: true, patientsCount: 0 };
  }

  try {
    await submitElevenLabsBatchCall(patients, time);
    return {
      success: true,
      patientsCount: patients.length,
      patients: patients,
    };
  } catch (error) {
    logger.error('Error submitting to ElevenLabs:', error.message);
    return {
      success: true,
      patientsCount: patients.length,
      patients: patients,
      elevenLabsError: error.message,
    };
  }
};

/**
 * Make API call to /outbound endpoint
 * @param {number} clinicId - The clinic ID
 * @returns {Object} Response object
 */
const makeOutboundAPICall = async (clinicId) => {
  const payload = { clinic_id: clinicId };
  const apiUrl = process.env.BACKEND_BASE_URL;
  const endpoint = `${apiUrl}/api/v1/patient/outbound`;

  logger.info(loggerMessages.MAKING_OUTBOUND_API_CALL(endpoint, clinicId, payload), {
    clinicId,
    payload,
  });

  const response = await axios.post(endpoint, payload, {
    headers: { 'Content-Type': 'application/json' },
    timeout: API_TIMEOUT,
  });

  logger.info(loggerMessages.OUTBOUND_API_CALL_SUCCESSFUL(clinicId, response.status), {
    status: response.status,
  });
  return response;
};

/**
 * Check if it's time to make outbound calls for a clinic
 * @param {Object} clinic - The clinic object
 * @returns {boolean} True if calls should be made
 */
const shouldMakeOutboundCalls = async (clinic) => {
  try {
    if (!clinic.batch_call_time) return false;

    const [hours, minutes, seconds] = clinic.batch_call_time
      .split(':')
      .map(Number);
    const timezone = clinic.timezone || 'UTC';

    const now = new Date();
    const clinicDate = new Date();
    const clinicTimeString = clinicDate.toLocaleString('en-US', {
      timeZone: timezone,
    });
    const clinicDateObj = new Date(clinicTimeString);
    clinicDateObj.setHours(hours, minutes, seconds, 0);

    const currentTimeInClinicTZ = new Date(
      now.toLocaleString('en-US', { timeZone: timezone })
    );
    const timeDiff = Math.abs(
      currentTimeInClinicTZ.getTime() - clinicDateObj.getTime()
    );

    logger.info(`Clinic ${clinic.id} time check:`, {
      currentTime: currentTimeInClinicTZ.toLocaleString(),
      scheduledTime: clinicDateObj.toLocaleString(),
      timeDiff: timeDiff,
      timezone: timezone,
    });

    return timeDiff <= FIVE_MINUTES_MS;
  } catch (error) {
    logger.error(
      `Error checking if should make calls for clinic ${clinic.id}:`,
      error
    );
    return false;
  }
};

/**
 * Process a single clinic for outbound calls
 * @param {Object} clinic - The clinic object
 * @param {boolean} checkTime - Whether to check if it's time to make calls
 */
const processClinicForOutboundCall = async (clinic, checkTime = true) => {
  try {
    logger.info(loggerMessages.PROCESSING_CLINIC(clinic.id, clinic.clinic_name));

    if (checkTime) {
      const shouldMakeCalls = await shouldMakeOutboundCalls(clinic);
      if (!shouldMakeCalls) {
        logger.info(loggerMessages.SKIPPING_CLINIC_TIME(clinic.id));
        return;
      }
    }

    const days = clinic.reactivation_days || DEFAULT_DAYS;
    const patients = await findInactivePatients(clinic.id, days);
    const result = await processPatientsAndSubmit(patients);

    if (result.patientsCount > 0) {
      logger.info(loggerMessages.SUCCESSFULLY_SUBMITTED_BATCH(clinic.id, result.patientsCount));
    } else {
      logger.info(loggerMessages.NO_INACTIVE_PATIENTS(clinic.id));
    }

    return result;
  } catch (error) {
    logger.error(loggerMessages.ERROR_PROCESSING_CLINIC(clinic.id, error));
    throw error;
  }
};

/**
 * Process all clinics for outbound calls (daily job)
 */
const processAllClinicsForOutboundCalls = async () => {
  try {
    logger.info(loggerMessages.PROCESSING_ALL_CLINICS);

    const clinics = await getActiveClinics({
      reactivation_days: { [Op.not]: null },
      batch_call_time: { [Op.not]: null },
    });

    logger.info(loggerMessages.FOUND_CLINICS_WITH_REACTIVATION(clinics.length));

    const results = await Promise.allSettled(
      clinics.map((clinic) => processClinicForOutboundCall(clinic))
    );

    const successful = results.filter((r) => r.status === 'fulfilled').length;
    const failed = results.filter((r) => r.status === 'rejected').length;

    logger.info(loggerMessages.COMPLETED_PROCESSING_ALL_CLINICS, {
      total: clinics.length,
      successful,
      failed,
    });
  } catch (error) {
    logger.error(loggerMessages.ERROR_IN_PROCESS_ALL_CLINICS(error));
  }
};

/**
 * Process continuous outbound calls for all clinics (6-hourly job)
 */
const processContinuousOutboundCalls = async () => {
  try {
    logger.info(loggerMessages.PROCESSING_CONTINUOUS_CALLS);

    const clinics = await getActiveClinics();
    logger.info(loggerMessages.FOUND_ACTIVE_CLINICS(clinics.length));

    const results = await Promise.allSettled(
      clinics.map((clinic) => processClinicForOutboundCall(clinic, false))
    );

    const successful = results.filter((r) => r.status === 'fulfilled').length;
    const failed = results.filter((r) => r.status === 'rejected').length;

    logger.info(loggerMessages.COMPLETED_CONTINUOUS_CALLS, {
      total: clinics.length,
      successful,
      failed,
    });
  } catch (error) {
    logger.error(loggerMessages.ERROR_IN_PROCESS_CONTINUOUS_CALLS(error));
  }
};

/**
 * Call the /outbound API endpoint for a specific clinic
 * @param {Object} clinic - The clinic object
 */
const callOutboundAPIForClinic = async (clinic) => {
  try {
    logger.info(loggerMessages.CALLING_OUTBOUND_API_FOR_CLINIC(clinic.id, clinic.clinic_name));

    const response = await makeOutboundAPICall(clinic.id);

    logger.info(loggerMessages.SUCCESSFULLY_CALLED_OUTBOUND_API(clinic.id), {
      status: response.status,
      patientsCount: response.data?.data?.patientsCount || 'unknown',
      message: response.data?.message || 'No message',
    });

    return {
      success: true,
      clinicId: clinic.id,
      clinicName: clinic.clinic_name,
      response: response.data,
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_CALLING_OUTBOUND_API(clinic.id, error), {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      clinicName: clinic.clinic_name,
    });

    return {
      success: false,
      clinicId: clinic.id,
      clinicName: clinic.clinic_name,
      error: error.message,
      status: error.response?.status,
    };
  }
};

/**
 * Call the /outbound API endpoint for all clinics (2-hourly job)
 */
const callOutboundAPIForAllClinics = async () => {
  try {
    logger.info(loggerMessages.CALLING_OUTBOUND_API);

    const clinics = await getActiveClinics();
    logger.info(loggerMessages.FOUND_CLINICS_FOR_API_CALLS(clinics.length));

    const results = await Promise.allSettled(
      clinics.map((clinic) => callOutboundAPIForClinic(clinic))
    );

    const successful = results.filter(
      (r) => r.status === 'fulfilled' && r.value.success
    ).length;
    const failed = results.filter(
      (r) =>
        r.status === 'rejected' ||
        (r.status === 'fulfilled' && !r.value.success)
    ).length;

    logger.info(loggerMessages.COMPLETED_API_CALLS, {
      total: clinics.length,
      successful,
      failed,
    });

    return {
      success: true,
      totalClinics: clinics.length,
      successfulCalls: successful,
      failedCalls: failed,
      results: results.map((r) =>
        r.status === 'fulfilled' ? r.value : { success: false, error: r.reason }
      ),
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_IN_CALL_OUTBOUND_API_ALL_CLINICS(error));
    throw error;
  }
};

/**
 * Process a clinic with specific days parameter
 * @param {number} clinicId - The clinic ID
 * @param {number} days - Number of days to consider for inactive patients
 * @param {string} time - Optional time parameter for batch calls
 */
export const processClinicWithSpecificDays = async (clinicId, days, time) => {
  try {
    logger.info(loggerMessages.PROCESSING_CLINIC_WITH_DAYS(clinicId, days));

    const patients = await findInactivePatients(clinicId, days);
    const result = await processPatientsAndSubmit(patients, time);

    if (result.patientsCount === 0) {
      logger.info(loggerMessages.NO_INACTIVE_PATIENTS_WITH_DAYS(clinicId, days));
      return {
        success: true,
        message: constants.CRON_SUCCESS_NO_INACTIVE_PATIENTS(clinicId),
        patientsCount: 0,
      };
    }

    logger.info(loggerMessages.FOUND_INACTIVE_PATIENTS(clinicId, result.patientsCount));

    return {
      success: true,
      message: constants.CRON_SUCCESS_PROCESSED_CLINIC(clinicId),
      patientsCount: result.patientsCount,
      patients: result.patients,
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_PROCESSING_CLINIC_WITH_DAYS(clinicId, error));
    throw error;
  }
};

/**
 * Process all patients for a clinic
 * @param {number} clinicId - The clinic ID
 * @param {string} time - Optional time parameter for batch calls
 */
export const processAllPatientsForClinic = async (clinicId, time = null) => {
  try {
    logger.info(loggerMessages.PROCESSING_ALL_PATIENTS(clinicId));

    const patients = await getAllPatients({ clinic_id: clinicId });
    const result = await processPatientsAndSubmit(patients, time);

    if (result.patientsCount === 0) {
      logger.info(loggerMessages.NO_PATIENTS_FOUND(clinicId));
      return {
        success: true,
        message: constants.CRON_SUCCESS_NO_PATIENTS_FOUND(clinicId),
        patientsCount: 0,
      };
    }

    logger.info(loggerMessages.FOUND_PATIENTS(clinicId, result.patientsCount));

    return {
      success: true,
      message: constants.CRON_SUCCESS_PROCESSED_ALL_PATIENTS(clinicId),
      patientsCount: result.patientsCount,
      patients: result.patients,
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_PROCESSING_ALL_PATIENTS(clinicId, error));
    throw error;
  }
};

/**
 * Call the /outbound API endpoint manually for testing
 * @param {number} clinicId - The clinic ID
 */
export const callOutboundAPIManually = async (clinicId) => {
  try {
    logger.info(loggerMessages.MANUALLY_CALLING_OUTBOUND_API(clinicId));

    const response = await makeOutboundAPICall(clinicId);

    logger.info(loggerMessages.SUCCESSFULLY_CALLED_OUTBOUND_API_MANUALLY(clinicId));

    return {
      success: true,
      message: constants.CRON_SUCCESS_CALLED_OUTBOUND_API(clinicId),
      response: response.data,
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_MANUALLY_CALLING_OUTBOUND_API(clinicId, error));
    throw error;
  }
};

/**
 * Manually trigger outbound calls for a specific clinic
 * @param {number} clinicId - The clinic ID
 */
export const triggerOutboundCallsForClinic = async (clinicId) => {
  try {
    const clinic = await Clinic.findByPk(clinicId);

    if (!clinic) {
      throw new Error(loggerMessages.CLINIC_NOT_FOUND_FOR_TRIGGER(clinicId));
    }

    if (!clinic.is_active || clinic.is_deleted) {
      throw new Error(loggerMessages.CLINIC_NOT_ACTIVE(clinicId));
    }

    await processClinicForOutboundCall(clinic, false);

    return {
      success: true,
      message: constants.CRON_SUCCESS_TRIGGERED_OUTBOUND_CALLS(clinicId),
      clinic: clinic.clinic_name,
    };
  } catch (error) {
    logger.error(loggerMessages.ERROR_TRIGGERING_OUTBOUND_CALLS(clinicId, error));
    throw error;
  }
};

/**
 * Get cron job status
 */
export const getCronJobStatus = () => {
  const now = new Date();
  const schedules = {
    daily: process.env.CRON_DAILY_SCHEDULE || '0 0 * * *',
    continuous: process.env.CRON_CONTINUOUS_SCHEDULE || '0 */6 * * *',
    apiCall: process.env.CRON_API_CALL_SCHEDULE || '0 */2 * * *',
  };

  return {
    status: constants.CRON_STATUS_RUNNING,
    lastRun: now.toISOString(),
    schedules: {
      daily: {
        schedule: schedules.daily,
        description: constants.CRON_DAILY_DESCRIPTION,
        nextRun: constants.CRON_NEXT_RUN_DAILY,
      },
      continuous: {
        schedule: schedules.continuous,
        description: constants.CRON_CONTINUOUS_DESCRIPTION,
        nextRun: constants.CRON_NEXT_RUN_CONTINUOUS,
      },
      apiCall: {
        schedule: schedules.apiCall,
        description: constants.CRON_API_CALL_DESCRIPTION,
        nextRun: constants.CRON_NEXT_RUN_API_CALL,
      },
    },
    configuration: {
      apiBaseUrl: process.env.BACKEND_BASE_URL,
      timezone: constants.CRON_TIMEZONE,
      defaultDays: DEFAULT_DAYS,
      apiTimeout: API_TIMEOUT,
    },
    description: constants.CRON_OVERALL_DESCRIPTION,
  };
};
