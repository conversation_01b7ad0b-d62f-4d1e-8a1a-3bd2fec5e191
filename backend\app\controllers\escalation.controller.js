/**
 * Escalation Controller: Handles HTTP requests for escalation CRUD operations.
 * Uses escalationService and returns consistent API responses.
 * <AUTHOR>
 */
import * as escalationService from '../services/escalation.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as status from '../utils/status_code.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';
import * as patientService from '../services/patient.service.js'
import dotenv from 'dotenv';
dotenv.config();

const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY


/**
 * Create a new escalation
 * @route POST /v1/escalation/create
 * <AUTHOR>
 */
export const createEscalation = async (req, res) => {
  logger.info(logMessages.CREATING_ESCALATION);
  try {
    const escalationData = req.body;
    
    // Add user information if available
    if (req.user && req.user.id) {
      escalationData.created_by = req.user.id;
      escalationData.updated_by = req.user.id;
    }
    
    // Create the escalation
    const newEscalation = await escalationService.createEscalation(escalationData);
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATION_CREATED_SUCCESSFULLY, newEscalation)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_CREATING_ESCALATION}: ${error.message}`);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Create a new escalation with transcript from ElevenLabs
 * @route POST /v1/escalation/create-with-transcript
 * <AUTHOR>
 */
export const createEscalationWithTranscript = async (req, res) => {
  logger.info(logMessages.CREATING_ESCALATION_WITH_TRANSCRIPT);
  try {
    const { 
      clinic_id, 
      phone_number,
      summary, 
      description, 
      tags, 
      reason, 
      assignee_id,
      conversation_id,
    } = req.body;

    
    const  patient = await patientService.findPatientByEmailOrPhone({phone_number})
    
    const patient_id = patient.id
    
    // Validate required fields for ElevenLabs integration
    if (!conversation_id) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.CONVERSATION_ID_REQUIRED)
      );
    }
    
    const escalationData = {
      clinic_id,
      patient_id,
      summary,
      description,
      tags: tags || 'open',
      reason,
      assignee_id
    };
    
    // Add user information if available
    if (req.user && req.user.id) {
      escalationData.created_by = req.user.id;
      escalationData.updated_by = req.user.id;
    }
    
    // Create escalation with transcript
    const newEscalation = await escalationService.createEscalationWithTranscript(
      escalationData,
      conversation_id,
      ELEVENLABS_API_KEY
    );
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATION_CREATED_WITH_TRANSCRIPT_SUCCESSFULLY, newEscalation)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_CREATING_ESCALATION_WITH_TRANSCRIPT}: ${error.message}`);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all escalations
 * @route GET /v1/escalation/list
 * <AUTHOR>
 */
export const getAllEscalations = async (req, res) => {
  logger.info(logMessages.FETCHING_ESCALATIONS);
  try {
    const filters = {
      is_active: req.query.is_active,
      is_deleted: req.query.is_deleted,
      clinic_id: req.query.clinic_id
    };
    
    const escalations = await escalationService.getAllEscalations(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATIONS_FETCHED_SUCCESSFULLY, escalations)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_FETCHING_ESCALATIONS}: ${error.message}`);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Get a single escalation by ID
 * @route GET /v1/escalation/:id
 * <AUTHOR>
 */
export const getEscalationById = async (req, res) => {
  logger.info(logMessages.FETCHING_ESCALATION_BY_ID(req.params.id));
  try {
    const escalation = await escalationService.getEscalationById(req.params.id);
    if (!escalation) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.ESCALATION_NOT_FOUND)
      );
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATION_FETCHED_SUCCESSFULLY, escalation)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_FETCHING_ESCALATION_BY_ID}: ${error.message}`);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Update escalation by ID
 * @route PUT /v1/escalation/:id
 * <AUTHOR>
 */
export const updateEscalation = async (req, res) => {
  logger.info(logMessages.UPDATING_ESCALATION(req.params.id));
  try {
    const escalationData = req.body;
    
    // Add user information if available
    if (req.user && req.user.id) {
      escalationData.updated_by = req.user.id;
    }
    
    const updatedEscalation = await escalationService.updateEscalation(
      req.params.id,
      escalationData
    );
    
    if (!updatedEscalation) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.ESCALATION_NOT_FOUND)
      );
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATION_UPDATED_SUCCESSFULLY, updatedEscalation)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_UPDATING_ESCALATION}: ${error.message}`);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Delete escalation by ID (soft delete)
 * @route DELETE /v1/escalation/:id
 * <AUTHOR>
 */
export const deleteEscalation = async (req, res) => {
  logger.info(logMessages.DELETING_ESCALATION(req.params.id));
  try {
    const deletedEscalation = await escalationService.deleteEscalation(req.params.id);
    
    if (!deletedEscalation) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.ESCALATION_NOT_FOUND)
      );
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.ESCALATION_DELETED_SUCCESSFULLY, deletedEscalation)
    );
  } catch (error) {
    logger.error(`${logMessages.ERROR_DELETING_ESCALATION}: ${error.message}`);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};
