import React from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { Building2, Clock, Users, Calendar } from "lucide-react";

// Props for the ClinicStatsGrid component
import { Appointment } from "@/hooks/useAppointment";

// Define types locally since we're not using supabaseService
interface Clinic {
  id: number;
  name?: string;
  clinic_name?: string;
  address?: string;
  location?: string;
  email?: string;
  clinic_email?: string;
  contact_number?: string;
  clinic_phonenumber?: string;
  working_hours?: string;
  workingHours?: string;
  working_days?: string;
  workingDays?: string;
  timezone?: string;
  is_active?: boolean;
  sms_service?: boolean;
  email_service?: boolean;
  is_deleted?: boolean;
}

interface Doctor {
  id: number;
  doctor_name?: string;
  name?: string;
  specialization?: string;
  doctor_email?: string;
  email?: string;
  is_deleted?: boolean;
}

interface ClinicStatsGridProps {
  clinics: Clinic[]; // List of clinic objects
  doctors: Doctor[]; // List of doctor objects
  appointments: Appointment[]; // List of appointment objects
  getClinicStatus: (clinic: Clinic) => string; // Function to get clinic status
}

/**
 * Renders a grid of stat cards for clinic master overview.
 * Shows total clinics, active clinics, total doctors, and patients today.
 */
const ClinicStatsGrid: React.FC<ClinicStatsGridProps> = ({
  clinics,
  doctors,
  appointments,
  getClinicStatus,
}) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
    {/* Total Clinics Stat */}
    <StatCard
      title="Total Clinics"
      value={clinics.length}
      icon={<Building2 className="h-8 w-8 text-blue-600" />}
    />
    {/* Active Clinics Stat */}
    <StatCard
      title="Active Clinics"
      value={clinics.filter((c) => getClinicStatus(c) === "Active").length}
      icon={<Clock className="h-8 w-8 text-green-600" />}
    />
    {/* Total Doctors Stat */}
    <StatCard
      title="Total Doctors"
      value={doctors.length}
      icon={<Users className="h-8 w-8 text-purple-600" />}
    />
    {/* Patients Today Stat */}
    <StatCard
      title="Patients Today"
      value={
        appointments.filter(
          (a) => a.appointment_date === new Date().toISOString().split("T")[0]
        ).length
      }
      icon={<Calendar className="h-8 w-8 text-orange-600" />}
    />
  </div>
);

export default ClinicStatsGrid;
