
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  LayoutDashboard,
  Calendar,
  Users,
  Phone,
  Bot,
  Stethoscope,
  Building2,
  BarChart3,
  RefreshCw,
  FileText,
  AlertTriangle,
  Settings,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { SIDEBAR_MENU_ITEMS, TAB_TO_ROUTE } from "@/Constants/Layout";

import styles from "./Sidebar.module.css";

interface SidebarProps {
  activeTab: string;
}

const iconMap: Record<string, React.ComponentType<React.SVGProps<SVGSVGElement>>> = {
  LayoutDashboard,
  Calendar,
  Users,
  Phone,
  Bot,
  Stethoscope,
  Building2,
  BarChart3,
  RefreshCw,
  FileText,
  AlertTriangle,
  Settings,
  ChevronDown,
  ChevronRight,
};

const Sidebar = ({ activeTab }: SidebarProps) => {
  const router = useRouter();
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  
  // Filter out settings and logout items from sidebar
  const filteredMenuItems = SIDEBAR_MENU_ITEMS.filter(item => 
    item.label.toLowerCase() !== 'next'
  );

  // Auto-expand settings menu if any of its sub-tabs are active
  React.useEffect(() => {
    if (activeTab.startsWith('settings-') && !expandedMenus.includes('settings')) {
      setExpandedMenus(prev => [...prev, 'settings']);
    }
  }, [activeTab, expandedMenus]);

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus(prev => 
      prev.includes(menuId) 
        ? prev.filter(id => id !== menuId)
        : [...prev, menuId]
    );
  };

  const isSubmenuExpanded = (menuId: string) => expandedMenus.includes(menuId);

  return (
    <div className={styles.sidebarContainer}>
      <div>
        <div className={`${styles.logoContainer} text-sm text-slate-400 mb-4 md:block hidden`}>
          <Image
            src="/Densy%20AI.svg"
            alt="Densy AI Logo"
            width={150}
            height={40}
            priority
          />
        </div>
        <nav className={styles.menuNav}>
          {filteredMenuItems.map((item) => {
            const Icon = iconMap[item.icon];
            const hasSubmenu = 'hasSubmenu' in item && item.hasSubmenu;
            const isExpanded = isSubmenuExpanded(item.id);
            
            return (
              <div key={item.id}>
                <button
                  type="button"
                  onClick={() => {
                    if (hasSubmenu) {
                      toggleSubmenu(item.id);
                    } else {
                      router.push(TAB_TO_ROUTE[item.id as keyof typeof TAB_TO_ROUTE] || "/dashboard");
                    }
                  }}
                  className={cn(
                    styles.menuButton,
                    activeTab === item.id && styles.menuButtonActive
                  )}
                >
                  {Icon && <Icon className={styles.menuIcon} />}
                  <span className={styles.menuLabel}>{item.label}</span>
                  {hasSubmenu && (
                    <div className={styles.chevronIcon}>
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </div>
                  )}
                </button>
                
                {hasSubmenu && isExpanded && item.submenu && (
                  <div className={styles.submenu}>
                    {item.submenu.map((subItem) => {
                      const SubIcon = iconMap[subItem.icon];
                      return (
                        <button
                          key={subItem.id}
                          type="button"
                          onClick={() => router.push(TAB_TO_ROUTE[subItem.id as keyof typeof TAB_TO_ROUTE] || "/dashboard")}
                          className={cn(
                            styles.submenuButton,
                            activeTab === subItem.id && styles.submenuButtonActive
                          )}
                        >
                          {SubIcon && <SubIcon className={styles.submenuIcon} />}
                          <span className={styles.submenuLabel}>{subItem.label}</span>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;
