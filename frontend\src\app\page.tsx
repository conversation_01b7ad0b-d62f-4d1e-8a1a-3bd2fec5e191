"use client";

import React, { useState, useEffect } from "react";
import Dashboard from "@/app/dashboard/page";
import AppointmentManagement from "@/app/appointment-management/page";
import AICallLogs from "@/app/ai-call-logs/page";
import PatientRegistry from "@/app/patient-registry/page";
import DoctorDirectory from "@/app/doctor-directory/page";
import ClinicMaster from "@/app/clinic-master/page";
import ReactivationProgram from "@/app/reactivation-program/page";
import Analytics from "@/app/analytics/page";
import Settings from "@/app/settings/page";
import { useAuth } from "@/hooks/useAuth";
import { usePathname, useRouter } from "next/navigation";

// ProtectedRoute component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { getToken } = useAuth();
  const router = useRouter();
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    const token = getToken();
    if (!token) {
      router.replace("/login");
    } else {
      setChecked(true);
    }
  }, [getToken, router]);

  if (!checked) {
    return (
      <div className="p-8 text-center text-gray-500">
        Checking authentication...
      </div>
    );
  }
  return <>{children}</>;
}

const Index = () => {
  const pathname = usePathname();
  const { user } = useAuth();

  const elevenlabsWidgetUrl = process.env.NEXT_PUBLIC_ELEVENLABS_WIDGET_URL;
  const elevenlabsAgentId = process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID;

  useEffect(() => {
    if (!elevenlabsWidgetUrl || !user) return;
    const script = document.createElement("script");
    script.src = elevenlabsWidgetUrl;
    script.async = true;
    script.type = "text/javascript";
    script.id = "elevenlabs-widget-script";
    document.body.appendChild(script);
    return () => {
      // Clean up script and widget
      const scriptEl = document.getElementById("elevenlabs-widget-script");
      if (scriptEl) document.body.removeChild(scriptEl);
      const widgetEl = document.querySelector("elevenlabs-convai");
      if (widgetEl) widgetEl.remove();
    };
  }, [elevenlabsWidgetUrl, user]);

  // Map route to component
  const routeToComponent: Record<string, React.ReactNode> = {
    "/dashboard": <Dashboard />,
    "/appointment-management": <AppointmentManagement />,
    "/patient-registry": <PatientRegistry />,
    "/doctor-directory": <DoctorDirectory />,
    "/clinic-master": <ClinicMaster />,
    "/ai-call-logs": <AICallLogs />,
    "/reactivation-program": <ReactivationProgram />,
    "/analytics": <Analytics />,
    "/settings": <Settings />,
  };

  // Return null for root path as it will redirect to dashboard
  if (pathname === "/") {
    return null;
  }

  return (
    <ProtectedRoute>
      {/* Render the appropriate component based on the current route */}
      {routeToComponent[pathname] || <Dashboard />}

      {/* ElevenLabs Convai Widget */}
      {elevenlabsAgentId && user && (
        <div
          style={{
            position: "fixed",
            bottom: 24,
            left: 24,
            zIndex: 10000,
            pointerEvents: "auto",
          }}
          dangerouslySetInnerHTML={{
            __html: `<elevenlabs-convai agent-id="${elevenlabsAgentId}"></elevenlabs-convai>`,
          }}
        />
      )}

      {/* Preserve Tailwind utility */}
      <div className="border border-border hidden" />
    </ProtectedRoute>
  );
};

export default Index;
