/**
 * Escalation Audit Log Service: Handles business logic for escalation audit log operations.
 * Manages database operations using Sequelize ORM for tracking escalation changes.
 * <AUTHOR>
 */
import { EscalationAuditLog } from '../models/index.js';
import logger from '../config/logger.config.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';

/**
 * Create an escalation audit log entry
 * @param {Object} auditData - Data for new audit log entry
 * @returns {Promise<Object>} The created audit log entry
 * <AUTHOR>
 */
export const createEscalationAuditLog = async (auditData) => {
  try {
    const newAuditLog = await EscalationAuditLog.create(auditData);
    return newAuditLog;
  } catch (error) {
    logger.error(`${logMessages.ERROR_CREATING_ESCALATION}: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Get all escalation audit logs for a specific escalation
 * @param {number} escalationId - Escalation ID
 * @returns {Promise<Array>} List of audit log entries
 * <AUTHOR>
 */
export const getEscalationAuditLogs = async (escalationId) => {
  try {
    const auditLogs = await EscalationAuditLog.findAll({
      where: { escalation_id: escalationId },
      order: [['created_at', 'DESC']]
    });
    return auditLogs;
  } catch (error) {
    logger.error(`${logMessages.ERROR_FETCHING_ESCALATIONS}: ${error.message}`);
    throw new Error(error.message);
  }
};

/**
 * Get all escalation audit logs with optional filters
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of audit log entries
 * <AUTHOR>
 */
export const getAllEscalationAuditLogs = async (filters = {}) => {
  try {
    const where = {};
    if (filters.escalation_id) where.escalation_id = filters.escalation_id;
    if (filters.action) where.action = filters.action;
    if (filters.created_by) where.created_by = filters.created_by;
    
    const auditLogs = await EscalationAuditLog.findAll({
      where,
      order: [['created_at', 'DESC']]
    });
    return auditLogs;
  } catch (error) {
    logger.error(`${logMessages.ERROR_FETCHING_ESCALATIONS}: ${error.message}`);
    throw new Error(error.message);
  }
};
