/**
 * Campaign Service: Handles business logic for campaign CRUD operations.
 * Manages database operations using Sequelize ORM.
 * <AUTHOR>
 */
import { Op } from 'sequelize';
import { Campaign } from '../models/index.js';

/**
 * Check if a campaign exists by name and clinic_id
 * @param {Object} params - { name, clinic_id }
 * @returns {Promise<Object|null>} The campaign if found, else null
 * <AUTHOR>
 */
export const findCampaignByNameAndClinic = async ({ name, clinic_id }) => {
  return await Campaign.findOne({
    where: {
      name,
      clinic_id,
      is_deleted: false,
    },
  });
};

/**
 * Get all campaigns, with optional filters for is_active, is_deleted, type, and clinic_id
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of campaigns
 * <AUTHOR>
 */
export const getAllCampaigns = async (filters = {}) => {
  try {
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.type) where.type = filters.type;
    if (filters.clinic_id) where.clinic_id = filters.clinic_id;
    
    const campaigns = await Campaign.findAll({ where });
    return campaigns;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get a campaign by its ID
 * @param {number} id - Campaign ID
 * @returns {Promise<Object|null>} The campaign or null if not found
 * <AUTHOR>
 */
export const getCampaignById = async (id) => {
  try {
    const campaign = await Campaign.findByPk(id);
    return campaign;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create a new campaign
 * @param {Object} campaignData - Data for new campaign
 * @returns {Promise<Object>} The created campaign
 * <AUTHOR>
 */
export const createCampaign = async (campaignData) => {
  try {
    const newCampaign = await Campaign.create(campaignData);
    return newCampaign;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update a campaign by its ID
 * @param {number} id - Campaign ID
 * @param {Object} campaignData - Data to update
 * @returns {Promise<Object|null>} The updated campaign or null if not found
 * <AUTHOR>
 */
export const updateCampaign = async (id, campaignData) => {
  try {
    await Campaign.update(campaignData, { where: { id } });
    const updatedCampaign = await Campaign.findByPk(id);
    return updatedCampaign;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Soft delete a campaign by its ID (sets is_deleted to true)
 * @param {number} id - Campaign ID
 * @returns {Promise<Object|null>} The deleted campaign or null if not found
 * <AUTHOR>
 */
export const deleteCampaign = async (id) => {
  try {
    await Campaign.update({ is_deleted: true }, { where: { id } });
    const deletedCampaign = await Campaign.findByPk(id);
    return deletedCampaign;
  } catch (error) {
    throw new Error(error.message);
  }
}; 