import { Clinic, Doctor } from "@/app/patient-registry/page";
import { formConfigs } from "@/components/CommonComponents/formConfigs";

export const patientFields = (doctors: Doctor[], clinics: Clinic[]) => {
  return formConfigs.patient.map((field) => {
    if (field.name === "doctor_id") {
      return {
        ...field,
        options: doctors.map((doctor) => ({
          value: doctor.id,
          label: doctor.doctor_name,
        })),
      };
    }
    if (field.name === "clinic_id") {
      return {
        ...field,
        options: clinics.map((clinic) => ({
          value: clinic.id,
          label: clinic.clinic_name,
        })),
      };
    }
    return field;
  });
};