import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey);

// Test connection by checking if we can access the database
(async () => {
  try {
    // Try to get the current user session (this will work even with service role key)
    const { error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
    } else {
      console.log('✅ Supabase connection successful!');
      console.log('🔗 Connected to:', supabaseUrl);
      
      // Try to list tables (this might fail if no tables exist, but connection is still working)
      try {
        const { data: tables, error: tableError } = await supabase
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public')
          .limit(5);
        
        if (tableError) {
          console.log('ℹ️ Could not list tables (this is normal if no tables exist yet):', tableError.message);
        } else {
          console.log('📋 Available tables:', tables?.map(t => t.table_name) || 'No tables found');
        }
      } catch (tableErr) {
        console.log('ℹ️ Table listing not available (this is normal):', tableErr);
      }
    }
  } catch (err) {
    console.error('❌ Supabase connection error:', err);
  }
})();

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
} 