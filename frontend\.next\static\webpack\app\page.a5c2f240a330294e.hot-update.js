"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/reactivation-program/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/reactivation-program/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsGrid */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsGrid.tsx\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsTable */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsTable.tsx\");\n/* harmony import */ var _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Constants/ReactivationProgram */ \"(app-pages-browser)/./src/Constants/ReactivationProgram.ts\");\n/* harmony import */ var _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useReactivationManagement */ \"(app-pages-browser)/./src/hooks/useReactivationManagement.ts\");\n/* harmony import */ var _components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CommonComponents/PageSection */ \"(app-pages-browser)/./src/components/CommonComponents/PageSection.tsx\");\n/* harmony import */ var _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useAppointment */ \"(app-pages-browser)/./src/hooks/useAppointment.ts\");\n/* harmony import */ var _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAICallLog */ \"(app-pages-browser)/./src/hooks/useAICallLog.ts\");\n/* harmony import */ var _components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ReactivationProgram/AddReactivationForm */ \"(app-pages-browser)/./src/components/ReactivationProgram/AddReactivationForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReactivationProgram = ()=>{\n    _s();\n    const [isAddReactivationOpen, setIsAddReactivationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGNS_DATA);\n    // View state\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"campaigns\");\n    const { aiCallLogs, getAllAICallLogs } = (0,_hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__.useAICallLog)();\n    const { appointments, getAllAppointments } = (0,_hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__.useAppointment)();\n    const { summary, reactivations, error, getReactivationSummary, getReactivationsByClinic } = (0,_hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__.useReactivationManagement)();\n    const handleAddCampaign = (data)=>{\n        setCampaigns([\n            ...campaigns,\n            data\n        ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAppointments();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAppointments\n    ]);\n    // Fetch reactivations when stats tab is active and clinic is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Fetching data for stats tab, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error fetching stats data:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        activeView,\n        selectedClinicId,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAICallLogs();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAICallLogs\n    ]);\n    const today = new Date().toISOString().split(\"T\")[0]; // \"2025-09-15\" format\n    const appointmentCount = Array.isArray(appointments) ? appointments.filter((appointment)=>appointment.appointment_date === today).length : 0;\n    // Fetch data when component mounts if clinic is already selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Initial data fetch for stats, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error in initial stats fetch:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        selectedClinicId,\n        activeView,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_TITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_SUBTITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"main\",\n                        onClick: ()=>setIsAddReactivationOpen(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_ADD_NEW\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                appointments: appointmentCount,\n                calls: (aiCallLogs === null || aiCallLogs === void 0 ? void 0 : aiCallLogs.length) || 0,\n                successRate: \"\".concat((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, \"%\"),\n                newBookings: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"campaigns\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"campaigns\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Campaigns\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"patients\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"patients\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Patient Management\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: async ()=>{\n                                setActiveView(\"stats\");\n                                // Fetch data immediately when stats tab is clicked\n                                if (selectedClinicId) {\n                                    await Promise.all([\n                                        getReactivationSummary(selectedClinicId),\n                                        getReactivationsByClinic(selectedClinicId)\n                                    ]);\n                                }\n                            },\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"stats\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Call Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"campaigns\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_CAMPAIGNS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_TITLE\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_DESCRIPTION\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"patients\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PATIENT_MANAGEMENT_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: \"Patient Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: 'Patient management functionality has been moved to the Add Reactivation Program form. Click the \"Add New Program\" button to access patient management features.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"stats\" && // <></>\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedClinicId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600\",\n                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.LOADING_STATISTICS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 17\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.ERROR_LOADING_STATISTICS,\n                                                \" \",\n                                                error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 17\n                            }, undefined),\n                            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_TOTAL_CAMPAIGNS\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_campaigns) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: [\n                                                                            (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGN_STATUS_BREAKDOWN_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_ACTIVE_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.active_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_COMPLETED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_FAILED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-red-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.failed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 360,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PERFORMANCE_METRICS_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-purple-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 384,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: [\n                                                                                (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_CAMPAIGN_EFFICIENCY\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: [\n                                                                                summary && summary.total_campaigns > 0 ? Math.round((summary.total_patients_contacted || 0) / summary.total_campaigns) : 0,\n                                                                                \" \",\n                                                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_PER_CAMPAIGN\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        reactivations: reactivations || [],\n                                        loading: loading\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_SELECT_CLINIC\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isAddReactivationOpen,\n                onClose: ()=>setIsAddReactivationOpen(false),\n                onSubmit: handleAddCampaign\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: '<elevenlabs-convai agent-id=\"agent_01jybn5qtwfnd8twmvjffcb0h3\"></elevenlabs-convai>'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReactivationProgram, \"GnCretw7JNJh/xQmrMDfU2UHoCY=\", false, function() {\n    return [\n        _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_10__.useAICallLog,\n        _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_9__.useAppointment,\n        _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_7__.useReactivationManagement\n    ];\n});\n_c = ReactivationProgram;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactivationProgram);\nvar _c;\n$RefreshReg$(_c, \"ReactivationProgram\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reactivation-program/page.tsx\n"));

/***/ })

});