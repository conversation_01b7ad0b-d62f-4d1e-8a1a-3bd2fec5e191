.logoContainer {
  text-align: center;
}
.menuNav {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px 18px 32px 18px;
}
.hidden {
  display: none;
}
.md\:inline {
  display: inline;
}
.ml-0 {
  /* margin-left: 0; Removed for cleaner alignment */
}
.md\:ml-2 {
  /* margin-left: 0.5rem; Removed for cleaner alignment */
}
.sidebarContainer {
  width: 264px;
  height: 1132px;
  padding-top: 24px;
  padding-right: 18px;
  padding-bottom: 32px;
  padding-left: 18px;
  background: #FFFFFF;
  border-right: 1px solid #F0F1F3;
  box-shadow: 4px 0px 30px 0px #8362EA0D;
  opacity: 1;
}

.menuButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  transition: background 0.2s;
  border-radius: 12px;
  padding: 8px 12px;
  text-align: left;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.5%;
  color: #667085;
  background: transparent;
  cursor: pointer;
}
.menuButton:hover {
  background: #e0e7ef;
  color: #fff;
  /* Removed gap, border-radius, and padding overrides for consistency */
}
.menuButtonActive:hover {
  background: #0023FF;
  color: #fff;
}
.menuButtonActive {
  background: #0023FF;
  color: #fff;
  border-radius: 24px;
}
.menuIcon {
  height: 20px;
  width: 20px;
  color: #667085;
  background: transparent;
}
.menuButtonActive .menuIcon {
  color: #fff;
}
.menuLabel {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.5%;
  background: transparent;
  color: #667085;
}
.menuButtonActive .menuLabel {
  color: #fff;
}
.logoutButton {
  margin-top: 4px;
}

.chevronIcon {
  margin-left: auto;
  display: flex;
  align-items: center;
  color: #667085;
}

.submenu {
  margin-left: 20px;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.submenuButton {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  transition: background 0.2s;
  border-radius: 8px;
  padding: 6px 12px;
  text-align: left;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  letter-spacing: 0.5%;
  color: #667085;
  background: transparent;
  cursor: pointer;
}

.submenuButton:hover {
  background: #e0e7ef;
  color: #475467;
}

.submenuButtonActive {
  background: #0023FF;
  color: #fff;
  border-radius: 8px;
}

.submenuIcon {
  height: 16px;
  width: 16px;
  color: #667085;
  background: transparent;
}

.submenuButtonActive .submenuIcon {
  color: #fff;
}

.submenuLabel {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  letter-spacing: 0.5%;
  background: transparent;
  color: #667085;
}

.submenuButtonActive .submenuLabel {
  color: #fff;
}