/**
 * Campaign Controller: Handles HTTP requests for campaign CRUD operations.
 * Uses campaignService and returns consistent API responses.
 * <AUTHOR>
 */
import * as campaignService from '../services/campaign.service.js';
import { logCampaignAudit } from '../services/campaignAuditLog.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { AuditActions, CampaignAuditDescriptions } from '../utils/auditlog_messages.utils.js';

/**
 * Create a new campaign
 * @route POST /v1/campaign/create
 * <AUTHOR>
 */
export const createCampaign = async (req, res) => {
  logger.info(loggerMessages.CREATING_CAMPAIGN);
  try {
    const campaignData = req.body;
    if (req.user && req.user.id) {
      campaignData.created_by = req.user.id;
      campaignData.updated_by = req.user.id;
    }
    
    // Check for existing campaign by name and clinic_id
    const existingCampaign = await campaignService.findCampaignByNameAndClinic({
      name: campaignData.name,
      clinic_id: campaignData.clinic_id,
    });
    if (existingCampaign) {
      return res.status(status.STATUS_CODE_CONFLICT).json(
        errorResponse(constants.CAMPAIGN_ALREADY_EXISTS_ERROR)
      );
    }
    
    // Create the campaign
    const newCampaign = await campaignService.createCampaign(campaignData);
    await logCampaignAudit({
      action: AuditActions.CREATE,
      record_id: newCampaign.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newCampaign),
      description: CampaignAuditDescriptions.CAMPAIGN_CREATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CAMPAIGN_CREATED_SUCCESSFULLY, newCampaign)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_CAMPAIGN);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all campaigns
 * @route GET /v1/campaign/list
 * <AUTHOR>
 */
export const getAllCampaigns = async (req, res) => {
  logger.info(loggerMessages.FETCHING_CAMPAIGNS);
  try {
    const filters = {
      is_active: req.query.is_active,
      is_deleted: req.query.is_deleted,
      type: req.query.type,
      clinic_id: req.query.clinic_id,
    };
    const campaigns = await campaignService.getAllCampaigns(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CAMPAIGNS_FETCHED_SUCCESSFULLY, campaigns)
    );
  } catch (error) {
    logger.info(loggerMessages.ERROR_FETCHING_CAMPAIGNS);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Get a single campaign by ID
 * @route GET /v1/campaign/:id
 * <AUTHOR>
 */
export const getCampaignById = async (req, res) => {
  logger.info(loggerMessages.FETCHING_CAMPAIGN_BY_ID);
  try {
    const campaign = await campaignService.getCampaignById(req.params.id);
    if (!campaign) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.CAMPAIGN_NOT_FOUND)
      );
    }
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CAMPAIGN_FETCHED_SUCCESSFULLY, campaign)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_CAMPAIGN_BY_ID);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Update a campaign by ID
 * @route PUT /v1/campaign/:id
 * <AUTHOR>
 */
export const updateCampaign = async (req, res) => {
  logger.info(loggerMessages.UPDATING_CAMPAIGN);
  try {
    const campaignData = req.body;
    const { id } = req.params;
    if (req.user && req.user.id) {
      campaignData.updated_by = req.user.id;
      campaignData.updated_at = Date.now();
    }
    
    const oldCampaign = await campaignService.getCampaignById(id);
    const updatedCampaign = await campaignService.updateCampaign(
      req.params.id,
      campaignData
    );
    if (!updatedCampaign) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.CAMPAIGN_NOT_FOUND)
      );
    }
    await logCampaignAudit({
      action: AuditActions.UPDATE,
      record_id: updatedCampaign.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(oldCampaign),
      new_value: JSON.stringify(updatedCampaign),
      description: CampaignAuditDescriptions.CAMPAIGN_UPDATED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CAMPAIGN_UPDATED_SUCCESSFULLY, updatedCampaign)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_CAMPAIGN);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
};

/**
 * Soft delete a campaign by ID
 * @route DELETE /v1/campaign/:id
 * <AUTHOR>
 */
export const deleteCampaign = async (req, res) => {
  logger.info(loggerMessages.DELETING_CAMPAIGN);
  try {
    const deletedCampaign = await campaignService.deleteCampaign(req.params.id);
    if (!deletedCampaign) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.CAMPAIGN_NOT_FOUND)
      );
    }
    await logCampaignAudit({
      action: AuditActions.DELETE,
      record_id: deletedCampaign.id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(deletedCampaign),
      description: CampaignAuditDescriptions.CAMPAIGN_DELETED,
    });
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.CAMPAIGN_DELETED_SUCCESSFULLY, deletedCampaign)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_CAMPAIGN);
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(error.message)
    );
  }
}; 