import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Phone, 
  Users, 
  Clock, 
  CheckCircle, 
  User, 
  Mail, 
  Calendar,
  ChevronDown,
  ChevronUp,
  Building,
  FileText
} from 'lucide-react';
import { BatchCallResponse } from '@/hooks/usePatientManagement';
import { format } from 'date-fns';
import { BATCH_CALL_RESULTS } from '@/Constants/ReactivationProgram';

interface BatchCallResultsProps {
  results: BatchCallResponse | null;
  onClose: () => void;
}

const BatchCallResults: React.FC<BatchCallResultsProps> = ({ results, onClose }) => {
  const [expandedPatients, setExpandedPatients] = useState<Set<number>>(new Set());

  if (!results) return null;

  const togglePatientExpansion = (patientId: number) => {
    const newExpanded = new Set(expandedPatients);
    if (newExpanded.has(patientId)) {
      newExpanded.delete(patientId);
    } else {
      newExpanded.add(patientId);
    }
    setExpandedPatients(newExpanded);
  };

  const toggleAllPatients = () => {
    if (expandedPatients.size === results.patients.length) {
      setExpandedPatients(new Set());
    } else {
      setExpandedPatients(new Set(results.patients.map(p => p.id)));
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return BATCH_CALL_RESULTS.INVALID_DATE;
    }
  };

  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <CardTitle className="text-green-900">{BATCH_CALL_RESULTS.SUCCESS_TITLE}</CardTitle>
              <p className="text-green-700 text-sm">
                {BATCH_CALL_RESULTS.SUCCESS_SUBTITLE} {results.total_selected} patients
              </p>
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            {BATCH_CALL_RESULTS.CLOSE_BUTTON}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Batch Call Summary */}
        <div className="bg-white p-6 rounded-lg border border-green-200">
          <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            {BATCH_CALL_RESULTS.BATCH_CALL_SUMMARY_TITLE}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-600">{results.total_selected}</div>
              <div className="text-sm text-gray-600">{BATCH_CALL_RESULTS.TOTAL_PATIENTS}</div>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Building className="h-6 w-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{results.clinic_id}</div>
              <div className="text-sm text-gray-600">{BATCH_CALL_RESULTS.CLINIC_ID}</div>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-sm font-medium text-purple-600">
                {formatDate(results.scheduled_time)}
              </div>
              <div className="text-sm text-gray-600">{BATCH_CALL_RESULTS.SCHEDULED_TIME}</div>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <CheckCircle className="h-6 w-6 text-orange-600" />
              </div>
              <div className="text-2xl font-bold text-orange-600">{BATCH_CALL_RESULTS.ACTIVE_STATUS}</div>
              <div className="text-sm text-gray-600">{BATCH_CALL_RESULTS.STATUS}</div>
            </div>
          </div>
        </div>

        {/* Patient Details Section */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              {BATCH_CALL_RESULTS.PATIENT_DETAILS_TITLE}
            </h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={toggleAllPatients}
              className="text-xs"
            >
              {expandedPatients.size === results.patients.length ? BATCH_CALL_RESULTS.COLLAPSE_ALL : BATCH_CALL_RESULTS.EXPAND_ALL}
            </Button>
          </div>
          
          <div className="space-y-3">
            {results.patients.map((patient, index) => (
              <div key={patient.id} className="border border-gray-200 rounded-lg">
                <div 
                  className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => togglePatientExpansion(patient.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {patient.first_name} {patient.last_name}
                        </h4>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <span>ID: {patient.id}</span>
                          {patient.tags && patient.tags.length > 0 && (
                            <>
                              <span>•</span>
                              <Badge variant="outline" className="text-xs">
                                {patient.tags[0]}
                              </Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-gray-500">
                        {patient.phone_number && (
                          <div className="flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            <span>{patient.phone_number}</span>
                          </div>
                        )}
                      </div>
                      {expandedPatients.has(patient.id) ? (
                        <ChevronUp className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
                
                {expandedPatients.has(patient.id) && (
                  <div className="px-4 pb-4 border-t border-gray-100">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 pt-4">
                      {/* Patient Information */}
                      <div className="space-y-4">
                        <h5 className="font-medium text-gray-900 text-sm border-b pb-2">
                          {BATCH_CALL_RESULTS.PATIENT_INFORMATION}
                        </h5>
                        <div className="space-y-3 text-sm">
                          {patient.email && (
                            <div className="flex items-center gap-3 text-gray-600">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span>{patient.email}</span>
                            </div>
                          )}
                          {patient.phone_number && (
                            <div className="flex items-center gap-3 text-gray-600">
                              <Phone className="h-4 w-4 text-gray-400" />
                              <span>{patient.phone_number}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-3 text-gray-600">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span>
                              {BATCH_CALL_RESULTS.LAST_VISIT_PREFIX} {patient.last_visit ? formatDate(patient.last_visit) : BATCH_CALL_RESULTS.NEVER_VISITED}
                            </span>
                          </div>
                          {patient.gender && (
                            <div className="flex items-center gap-3 text-gray-600">
                              <User className="h-4 w-4 text-gray-400" />
                              <span>{BATCH_CALL_RESULTS.GENDER_PREFIX} {patient.gender.charAt(0).toUpperCase() + patient.gender.slice(1)}</span>
                            </div>
                          )}
                          {patient.address && (
                            <div className="text-gray-600">
                              <div className="font-medium mb-1">{BATCH_CALL_RESULTS.ADDRESS_LABEL}</div>
                              <div className="pl-7">{patient.address}</div>
                            </div>
                          )}
                          {patient.last_visit_summary && (
                            <div className="text-gray-600">
                              <div className="font-medium mb-1">{BATCH_CALL_RESULTS.LAST_VISIT_SUMMARY_LABEL}</div>
                              <div className="pl-7 text-xs bg-gray-50 p-2 rounded">
                                {patient.last_visit_summary}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* User Information */}
                      <div className="space-y-4">
                        <h5 className="font-medium text-gray-900 text-sm border-b pb-2">
                          {BATCH_CALL_RESULTS.USER_INFORMATION}
                        </h5>
                        <div className="space-y-3">
                          {patient.Creator && (
                            <div className="bg-blue-50 p-3 rounded border border-blue-200">
                              <div className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                                <User className="h-4 w-4" />
                                {BATCH_CALL_RESULTS.CREATED_BY}
                              </div>
                              <div className="text-blue-800 space-y-1 text-sm">
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.NAME_LABEL}</span> {patient.Creator.name}</div>
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.EMAIL_LABEL}</span> {patient.Creator.email}</div>
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.PHONE_LABEL}</span> {patient.Creator.user_phonenumber}</div>
                              </div>
                            </div>
                          )}
                          {patient.Updater && (
                            <div className="bg-green-50 p-3 rounded border border-green-200">
                              <div className="font-medium text-green-900 mb-2 flex items-center gap-2">
                                <User className="h-4 w-4" />
                                {BATCH_CALL_RESULTS.LAST_UPDATED_BY}
                              </div>
                              <div className="text-green-800 space-y-1 text-sm">
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.NAME_LABEL}</span> {patient.Updater.name}</div>
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.EMAIL_LABEL}</span> {patient.Updater.email}</div>
                                <div><span className="font-medium">{BATCH_CALL_RESULTS.PHONE_LABEL}</span> {patient.Updater.user_phonenumber}</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BatchCallResults;
