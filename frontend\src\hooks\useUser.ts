import { apiRequest } from "../utils/axios.utils";
import { extractErrorMessage } from "@/utils/commonFunctions";

export interface User {
  id: number;
  name: string;
  email: string;
  role: number;
  clinic_id?: number;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  is_active: boolean;
}

export const useUser = () => {
  const getUserById = async (id: number) => {
    try {
      const response = await apiRequest.get(`/user/${id}`) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data as User };
      } else {
        return {
          success: false,
          error: response?.message || "Failed to fetch user",
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, "Failed to fetch user");
      return { success: false, error: errorMsg };
    }
  };

  const getAllUsers = async () => {
    try {
      const response = await apiRequest.get("/user/list") as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data as User[] };
      } else {
        return {
          success: false,
          error: response?.message || "Failed to fetch users",
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, "Failed to fetch users");
      return { success: false, error: errorMsg };
    }
  };

  return {
    getUserById,
    getAllUsers,
  };
}; 