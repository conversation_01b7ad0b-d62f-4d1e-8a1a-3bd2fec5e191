import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createTemplateSchema, updateTemplateSchema } from '../validators/template.validator.js';
import * as templateController from '../controllers/template.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new template
// POST /v1/template/create
router.post(
  '',
  verifyToken,
  validate(createTemplateSchema),
  templateController.createTemplate
);

// Get all templates
// GET /v1/template/list
router.get(
  '',
  verifyToken,
  templateController.getAllTemplates
);

// Get template by ID
// GET /v1/template/:id
router.get(
  '/:id',
  verifyToken,
  templateController.getTemplateById
);

// Update template by ID
// PUT /v1/template/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateTemplateSchema),
  templateController.updateTemplate
);

// Soft delete template by ID
// DELETE /v1/template/:id
router.delete(
  '/:id',
  verifyToken,
  templateController.deleteTemplate
);

export default router; 