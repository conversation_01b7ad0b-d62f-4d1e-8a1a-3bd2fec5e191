export const RESET_PASSWORD_CONSTANTS = {
  // Page Titles and Headers
  PAGE_TITLE: "Reset Password",
  INVALID_LINK_TITLE: "Invalid Reset Link",
  INVALID_LINK_SUBTITLE: "This password reset link is invalid or has expired.",
  SUBTITLE: "Reset your password",
  
  // Form Labels
  NEW_PASSWORD_LABEL: "New Password",
  CONFIRM_PASSWORD_LABEL: "Confirm Password",
  
  // Form Placeholders
  NEW_PASSWORD_PLACEHOLDER: "••••••••",
  CONFIRM_PASSWORD_PLACEHOLDER: "••••••••",
  
  // Button Text
  RESET_PASSWORD: "Reset Password",
  RETURN_TO_LOGIN: "Return to Login",
  BACK_TO_LOGIN: "Back to Login",
  LOADING: "Loading...",
  
  // Status Messages
  SUCCESS: "Password reset successfully!",
  
  // Password Requirements
  PASSWORD_REQUIREMENTS_TITLE: "Password Requirements:",
  REQUIREMENT_LENGTH: "At least 8 characters",
  REQUIREMENT_UPPERCASE: "One uppercase letter (A-Z)",
  REQUIREMENT_LOWERCASE: "One lowercase letter (a-z)",
  REQUIREMENT_NUMBER: "One number (0-9)",
  REQUIREMENT_SPECIAL: "One special character (!@#$%^&*)",
  
  // Icons
  CHECK_ICON: "✓",
  CROSS_ICON: "✗",
  
  // Remember Password Text
  REMEMBER_PASSWORD: "Remember your password?",
} as const;

export const VALIDATION_MESSAGES = {
  NEW_PASSWORD_REQUIRED: "New password is required",
  CONFIRM_PASSWORD_REQUIRED: "Please confirm your password",
  PASSWORDS_MATCH: "Passwords must match",
  PASSWORD_MIN_LENGTH: "Password must be at least 8 characters",
  PASSWORD_UPPERCASE: "Password must contain at least one uppercase letter (A-Z)",
  PASSWORD_LOWERCASE: "Password must contain at least one lowercase letter (a-z)",
  PASSWORD_NUMBER: "Password must contain at least one number (0-9)",
  PASSWORD_SPECIAL: "Password must contain at least one special character (!@#$%^&*)",
  PASSWORD_PATTERN: "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
} as const;

export const TOAST_MESSAGES = {
  RESET_SUCCESS: "Password reset successfully! You can now login with your new password.",
  RESET_FAILED: "Password reset failed",
  INTERNAL_SERVER_ERROR: "Internal server error",
} as const;

export const FORM_FIELDS = {
  NEW_PASSWORD: "newPassword",
  CONFIRM_PASSWORD: "confirmPassword",
} as const;

export const FORM_IDS = {
  NEW_PASSWORD: "newPassword",
  CONFIRM_PASSWORD: "confirmPassword",
} as const;

export const ACCESSIBILITY = {
  HIDE_PASSWORD: "Hide password",
  SHOW_PASSWORD: "Show password",
  DENSY_AI_LOGO: "Densy AI Logo",
  RESET_PASSWORD: "Reset password",
  NEW_PASSWORD_INPUT: "New password input",
  CONFIRM_PASSWORD_INPUT: "Confirm password input",
} as const;

export const ASSETS = {
  LOGO_SRC: "/Densy%20AI.svg",
  LOGO_WIDTH: 150,
  LOGO_HEIGHT: 40,
} as const;

export const ANIMATION = {
  INITIAL_OPACITY: 0,
  INITIAL_SCALE: 0.96,
  FINAL_OPACITY: 1,
  FINAL_SCALE: 1,
  DURATION: 0.5,
} as const;

export const INPUT_TYPES = {
  PASSWORD: "password",
  TEXT: "text",
} as const;

export const BUTTON_TYPES = {
  BUTTON: "button",
  SUBMIT: "submit",
} as const;

export const FORM_ATTRIBUTES = {
  NEW_PASSWORD_AUTOCOMPLETE: "new-password",
  CONFIRM_PASSWORD_AUTOCOMPLETE: "new-password",
} as const;

export const ROUTES = {
  LOGIN: "/login",
} as const;

export const ICONS = {
  LOCK: "18",
  EYE: "18",
  EYE_OFF: "18",
  LOADER: "18",
} as const;

export const CSS_CLASSES = {
  // Container and Layout
  CHANGE_CONTAINER: "changeContainer",
  CHANGE_MOTION: "changeMotion",
  CHANGE_CARD: "changeCard",
  CHANGE_HEADER: "changeHeader",
  CHANGE_LOGO: "changeLogo",
  CHANGE_TITLE: "changeTitle",
  CHANGE_SUBTITLE: "changeSubtitle",
  
  // Form
  CHANGE_FORM: "changeForm",
  FORM_FIELD: "formField",
  FORM_LABEL: "formLabel",
  CHANGE_INPUT: "changeInput",
  CHANGE_ERROR: "changeError",
  
  // Icons
  CHANGE_ICON: "changeIcon",
  CHANGE_PASSWORD_TOGGLE: "changePasswordToggle",
  
  // Buttons
  CHANGE_BUTTON: "changeButton",
  
  // Password Strength
  PASSWORD_STRENGTH: "passwordStrength",
  PASSWORD_STRENGTH_TITLE: "passwordStrengthTitle",
  PASSWORD_CHECKS: "passwordChecks",
  PASSWORD_CHECK: "passwordCheck",
  CHECK_ICON: "checkIcon",
  VALID: "valid",
  INVALID: "invalid",
  
  // Back to Login
  CHANGE_BACK_TO_LOGIN: "changeBackToLogin",
  CHANGE_BACK_TO_LOGIN_LINK: "changeBackToLoginLink",
  
  // Text Center
  TEXT_CENTER: "text-center",
} as const;

export const ERROR_MESSAGES = {
  RESET_FAILED: "Failed to reset password",
  INVALID_TOKEN: "Invalid reset token. Please request a new password reset.",
} as const;

export const SUCCESS_MESSAGES = {
  PASSWORD_RESET: "Password reset successfully",
} as const; 