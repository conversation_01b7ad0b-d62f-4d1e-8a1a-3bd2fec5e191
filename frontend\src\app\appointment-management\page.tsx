'use client';

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "lucide-react";
import AppointmentFilters from '@/components/AppointmentManagement/AppointmentFilters';
import AppointmentList from '@/components/AppointmentManagement/AppointmentList';
import AppointmentDetailsSheet from '@/components/AppointmentManagement/AppointmentDetailsSheet';
import ScheduleAppointmentForm from '@/components/forms/ScheduleAppointmentForm';
import Pagination from '@/components/CommonComponents/Pagination';
import PageSection from '@/components/CommonComponents/PageSection';
import {
  TITLE_APPOINTMENT_MANAGEMENT,
  BUTTON_SCHEDULE_NEW,
  TITLE_TODAYS_APPOINTMENTS,
  BUTTON_RETRY
} from '@/Constants/Appointment';
import { useAppointment, Appointment } from "@/hooks/useAppointment";

const AppointmentManagement = () => {
  const [filterDoctor, setFilterDoctor] = useState("all");
  const [selectedAppointment, setSelectedAppointment] =
    useState<Appointment | null>(null);
  const [searchValue, setSearchValue] = useState("");
  const [isScheduleFormOpen, setIsScheduleFormOpen] = useState(false);
  const [appointmentToEdit, setAppointmentToEdit] = useState<Appointment | null>(null);
  
  const { appointments, loading, error, getAllAppointments } = useAppointment();

  useEffect(() => {
    getAllAppointments();
  }, [getAllAppointments]);

  const doctorList = [
    "all",
    ...Array.from(
      new Set(appointments.map((a) => a.doctor?.doctor_name).filter((name): name is string => Boolean(name)))
    ),
  ];

  const filteredAppointments = appointments
    .filter((appointment) => {
      const matchesDoctor =
        filterDoctor === "all" || appointment.doctor?.doctor_name === filterDoctor;
      const matchesSearch =
        !searchValue ||
        (appointment.patient?.first_name + " " + appointment.patient?.last_name)?.toLowerCase().includes(searchValue.toLowerCase());
      // Add date filtering logic here later
      return matchesDoctor && matchesSearch;
    })
    .sort((a, b) => {
      const dateA = new Date(a.appointment_date).getTime();
      const dateB = new Date(b.appointment_date).getTime();

      if (dateA !== dateB) {
        return dateB - dateA;
      }

      const timeA = new Date(a.appointment_time).getTime();
      const timeB = new Date(b.appointment_time).getTime();

      return timeB - timeA;
    });

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const totalPages = Math.ceil(filteredAppointments.length / pageSize);
  const paginatedAppointments = filteredAppointments.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  const handleScheduleAppointment = () => {
    // Refresh the appointments list after creating a new appointment
    getAllAppointments();
  };

  const handleEditAppointment = (appointment: Appointment) => {
    setAppointmentToEdit(appointment);
    setIsScheduleFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsScheduleFormOpen(false);
    setAppointmentToEdit(null);
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center h-full">
        {/* Replace Loader2 with a simple spinner or text */}
        <span className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full inline-block"></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-500">{error}</p>
        <Button
          onClick={() => window.location.reload()}
          className="mt-4"
          variant="outline"
        >
          {BUTTON_RETRY}
        </Button>
      </div>
    );
  }

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold text-gray-900">
          {TITLE_APPOINTMENT_MANAGEMENT}
        </h2>
        <Button 
          variant="main"
          onClick={() => setIsScheduleFormOpen(true)}
        >
          <Calendar className="h-4 w-4 mr-2" />
          {BUTTON_SCHEDULE_NEW}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <AppointmentFilters
            filterDoctor={filterDoctor}
            setFilterDoctor={setFilterDoctor}
            doctorList={doctorList}
            searchValue={searchValue}
            setSearchValue={setSearchValue}
          />
        </CardContent>
      </Card>

      {/* Appointments List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-blue-600" />
            {TITLE_TODAYS_APPOINTMENTS} ({filteredAppointments.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AppointmentList
            appointments={paginatedAppointments}
            onSelect={setSelectedAppointment}
            onEdit={handleEditAppointment}
          />
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            className="mt-6"
          />
        </CardContent>
      </Card>

      <AppointmentDetailsSheet appointment={selectedAppointment} onClose={() => setSelectedAppointment(null)} />
      
      <ScheduleAppointmentForm
        isOpen={isScheduleFormOpen}
        onClose={handleCloseForm}
        onSubmit={handleScheduleAppointment}
        appointmentToEdit={appointmentToEdit}
      />
    </PageSection>
  );
};

export default AppointmentManagement;
