// PatientFilters.tsx
// Renders search and filter controls for the Patient Registry page.
// Allows filtering patients by status and searching by MRN or name.

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SearchBar from "@/components/CommonComponents/SearchBar";
import {
  BUTTON_ALL,
  BUTTON_ACTIVE,
  BUTTON_INACTIVE,
  FILTER_ALL,
  FILTER_STATUS_ACTIVE,
  FILTER_STATUS_INACTIVE,
  PLACEHOLDER_SEARCH_PATIENT,
  PLACEHOLDER_FILTER_BY_STATUS,
  PLACEHOLDER_FILTER_BY_CLINIC,
} from "@/Constants/PatientRegistry";

/**
 * Props for the PatientFilters component
 * @property searchTerm - Current search term
 * @property setSearchTerm - Handler to update search term
 * @property filterStatus - Current filter status
 * @property setFilterStatus - Handler to update filter status
 */
interface ClinicOption {
  id: number;
  clinic_name: string;
}

interface PatientFiltersProps {
  searchTerm: string;
  setSearchTerm: (val: string) => void;
  filterStatus: string;
  setFilterStatus: (val: string) => void;
  filterClinic: string;
  setFilterClinic: (val: string) => void;
  clinics: ClinicOption[];
}

/**
 * Renders search and filter controls for the Patient Registry page.
 * Allows filtering by status and searching by MRN or name.
 */
const PatientFilters: React.FC<PatientFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  filterStatus,
  setFilterStatus,
  filterClinic,
  setFilterClinic,
  clinics,
}) => (
  <div className="flex flex-col md:flex-row gap-4">
    <SearchBar
      placeholder={PLACEHOLDER_SEARCH_PATIENT}
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      className="flex-1"
    />
    <div className="flex gap-2">
      <Select value={filterStatus} onValueChange={setFilterStatus}>
        <SelectTrigger className="w-32">
          <SelectValue placeholder={PLACEHOLDER_FILTER_BY_STATUS} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={FILTER_ALL}>{BUTTON_ALL}</SelectItem>
          <SelectItem value={FILTER_STATUS_ACTIVE}>{BUTTON_ACTIVE}</SelectItem>
          <SelectItem value={FILTER_STATUS_INACTIVE}>
            {BUTTON_INACTIVE}
          </SelectItem>
        </SelectContent>
      </Select>
      <Select value={filterClinic} onValueChange={setFilterClinic}>
        <SelectTrigger className="w-54">
          {/* Increased width */}
          <SelectValue placeholder={PLACEHOLDER_FILTER_BY_CLINIC} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={FILTER_ALL}>All Clinics</SelectItem>
          {clinics.map((clinic) => (
            <SelectItem key={clinic.id} value={String(clinic.id)}>
              {clinic.clinic_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  </div>
);

export default PatientFilters;
