export const TEMPLATE_FORM_FIELDS = {
  TEMPLATE_NAME: "Template Name",
  TYPE: "Type",
  CONTENT: "Content",
};

export const TEMPLATE_FORM_PLACEHOLDERS = {
  TEMPLATE_NAME: "Enter template name",
  CONTENT: "Enter template content...",
};

export const TEMPLATE_FORM_DEFAULTS = {
  TYPE: "sms",
  CONTENT: "",
};

export const TEMPLATE_TYPE_OPTIONS = [
  { value: "sms", label: "SMS" },
  { value: "email", label: "Email" },
  { value: "call_prompt", label: "Call Prompt" },
];

export const TEMPLATE_MASTER_TITLE = "Template Management";
export const TEMPLATE_MASTER_SUBTITLE = "Manage communication templates";

export const TEMPLATE_ACTIONS = {
  ADD_TEMPLATE: "Add Template",
  EDIT_TEMPLATE: "Edit Template",
  UPDATE_TEMPLATE: "Update Template",
  DELETE_TEMPLATE: "Delete Template",
  VIEW_TEMPLATE: "View Template",
};

export const TEMPLATE_MODAL_TITLES = {
  ADD_TEMPLATE: "Add New Template",
  EDIT_TEMPLATE: "Edit Template",
  VIEW_TEMPLATE: "Template Details",
};

export const TEMPLATE_TABLE_HEADERS = {
  TEMPLATE_NAME: "Template Name",
  TYPE: "Type",
  CREATED_DATE: "Created Date",
  ACTIONS: "Actions",
};

export const TEMPLATE_STATS = {
  TOTAL_TEMPLATES: "Total Templates",
  SMS_TEMPLATES: "SMS Templates",
  EMAIL_TEMPLATES: "Email Templates",
  AGENT_PROMPTS: "Agent Prompts",
};

export const TEMPLATE_SEARCH = {
  PLACEHOLDER: "Search templates...",
  FILTER_ALL: "All Types",
  FILTER_SMS: "SMS",
  FILTER_EMAIL: "Email", 
  FILTER_CALL_PROMPT: "Call Prompt",
};

// API Response Messages
export const TEMPLATE_CREATE_SUCCESS = "Template created successfully";
export const TEMPLATE_CREATE_FAILED = "Failed to create template";
export const TEMPLATE_UPDATE_SUCCESS = "Template updated successfully";
export const TEMPLATE_UPDATE_FAILED = "Failed to update template";
export const TEMPLATE_DELETE_SUCCESS = "Template deleted successfully";
export const TEMPLATE_DELETE_FAILED = "Failed to delete template";
export const TEMPLATE_FETCH_FAILED = "Failed to fetch template";
export const TEMPLATES_FETCH_FAILED = "Failed to fetch templates";

// Detailed Template Card Messages
export const TEMPLATE_DETAILED_CARD = {
  TEMPLATE_TYPE: "Template Type",
  CREATED_DATE: "Created Date",
  TEMPLATE_CONTENT: "Template Content",
  TEMPLATE_VARIABLES: "Template Variables",
  NO_VARIABLES_TITLE: "Template Variables",
  NO_VARIABLES_MESSAGE: "This template doesn't contain any dynamic variables.",
  VARIABLES_DESCRIPTION: "This template uses the following dynamic variables (format: {format}):",
  VARIABLES_FOOTNOTE: "These variables will be replaced with actual values when the template is used.",
  NOT_AVAILABLE: "N/A",
}; 