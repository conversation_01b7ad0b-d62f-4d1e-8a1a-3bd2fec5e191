// Dashboard Data Service
// Provides dynamic data for all dashboard components

export interface DashboardStats {
  title: string;
  value: string | number;
  icon: string;
}

export interface PatientData {
  day: string;
  enters: number;
  exits: number;
}

export interface CallData {
  name: string;
  value: number;
  color: string;
}

export interface AppointmentData {
  id: number;
  name: string;
  detail: string;
  timeAgo: string;
  status: 'confirmed' | 'completed' | 'reminder' | 'scheduled';
  avatar: string;
}

export interface ScheduleData {
  day: string;
  appointments: {
    id: number;
    doctor: string;
    startTime: string;
    endTime: string;
    color: string;
  }[];
}

// Helper function to generate random data
const generateRandomData = () => {
  const minutes = Math.floor(Math.random() * 30) + 1;
  const timeAgo = `${minutes} min ago`;
  
  return {
    appointments: Math.floor(Math.random() * 50) + 10,
    aiCalls: Math.floor(Math.random() * 100) + 20,
    reactivationSuccess: Math.floor(Math.random() * 30) + 60,
    patientReturns: Math.floor(Math.random() * 200) + 100,
    timeAgo
  };
};

// Mock data - replace with actual API calls
export const getDashboardStats = async (): Promise<DashboardStats[]> => {
  console.log('Fetching dashboard stats...');
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const randomData = generateRandomData();
  
  const stats = [
    {
      title: "Today's Appointments",
      value: randomData.appointments,
      icon: "Calendar"
    },
    {
      title: "AI Calls Handled",
      value: randomData.aiCalls,
      icon: "Phone"
    },
    {
      title: "Reactivation Success",
      value: `${randomData.reactivationSuccess}%`,
      icon: "RefreshCw"
    },
    {
      title: "Patient Returns",
      value: randomData.patientReturns,
      icon: "Users"
    }
  ];
  
  console.log('Dashboard stats loaded:', stats);
  return stats;
};

export const getPatientOverviewData = async (): Promise<PatientData[]> => {
  console.log('Fetching patient overview data...');
  
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const days = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
  const data = days.map(day => ({
    day,
    enters: Math.floor(Math.random() * 400) + 300,
    exits: Math.floor(Math.random() * 300) + 250
  }));
  
  console.log('Patient overview data loaded:', data);
  return data;
};

export const getCallData = async (): Promise<CallData[]> => {
  console.log('Fetching call data...');
  
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const data = [
    { 
      name: 'Total Calls', 
      value: Math.floor(Math.random() * 1000) + 1000, 
      color: '#3b82f6' 
    },
    { 
      name: 'Total minute use', 
      value: Math.floor(Math.random() * 3000) + 3000, 
      color: '#10b981' 
    },
    { 
      name: 'Avg. call duration', 
      value: Math.random() * 5 + 2, 
      color: '#6b7280' 
    }
  ];
  
  console.log('Call data loaded:', data);
  return data;
};

export const getAppointmentsData = async (): Promise<AppointmentData[]> => {
  console.log('Fetching appointments data...');
  
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const names = ['Rita Book', 'Peg Legge', 'Jack Tompshon', 'Lucas Graham', 'Phil Turner'];
  const details = [
    'Appt with Dr. Carter confirmed.',
    'Call completed - Interested',
    'Missed appointment reminder sent',
    'Follow-up call scheduled',
    'Appt with Dr. Martin confirmed.'
  ];
  const statuses: AppointmentData['status'][] = ['confirmed', 'completed', 'reminder', 'scheduled', 'confirmed'];
  
  const data = names.map((name, index) => ({
    id: index + 1,
    name,
    detail: details[index],
    timeAgo: `${Math.floor(Math.random() * 30) + 1} min ago`,
    status: statuses[index],
    avatar: `/api/placeholder/32/32`
  }));
  
  console.log('Appointments data loaded:', data);
  return data;
};

export const getScheduleData = async (): Promise<ScheduleData[]> => {
  console.log('Fetching schedule data...');
  
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const days = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
  const doctors = ['Dr. Carter', 'Dr. Martin', 'Dr. Johnson', 'Dr. Smith'];
  const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'];
  
  const data = days.map((day, index) => ({
    day,
    appointments: index < 2 ? [{
      id: index + 1,
      doctor: doctors[index],
      startTime: index === 0 ? '09:00' : '10:30',
      endTime: index === 0 ? '12:00' : '12:30',
      color: colors[index]
    }] : []
  }));
  
  console.log('Schedule data loaded:', data);
  return data;
};

// Property information for components (based on design specs)
export const componentProperties = {
  patientsOverview: {
    width: '360px',
    height: '396px',
    radius: '8px',
    border: '1px',
    padding: '24px',
    gap: '24px'
  },
  totalCalls: {
    width: '360px',
    height: '396px',
    radius: '8px',
    border: '1px',
    padding: '24px'
  },
  appointments: {
    width: '360px',
    height: '396px',
    radius: '8px',
    border: '1px',
    padding: '24px'
  }
};
