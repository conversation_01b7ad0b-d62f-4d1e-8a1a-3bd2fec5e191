/**
 * Campaign Audit Log Service: Handles audit logging for campaign operations.
 * Tracks all CRUD operations with user context and change history.
 * <AUTHOR>
 */
import { CampaignAuditLog } from '../models/index.js';

/**
 * Log an entry to the campaign audit log
 * @param {Object} logData - Audit log fields (action, record_id, user_id, old_value, new_value, description, error_details, timestamp)
 * @returns {Promise<Object>} The created audit log entry
 * <AUTHOR>
 */
export const logCampaignAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return CampaignAuditLog.create(logData);
}; 