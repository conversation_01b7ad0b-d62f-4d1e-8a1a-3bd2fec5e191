/**
 * Appointment Validators: express-validator schemas for appointment create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

export const createAppointmentSchema = [
  body('clinic_id').isInt().withMessage(constants.CLINIC_ID_INVALID).notEmpty().withMessage(constants.CLINIC_ID_INVALID),
  body('patient_id').optional().isInt().withMessage(constants.PATIENT_ID_INVALID_APPOINTMENT),
  body('doctor_id').optional().isInt().withMessage(constants.DOCTOR_ID_INVALID),
  body('appointment_date').isISO8601().withMessage(constants.APPOINTMENT_DATE_INVALID).notEmpty().withMessage(constants.APPOINTMENT_DATE_REQUIRED),
  body('appointment_time').isISO8601().withMessage(constants.APPOINTMENT_TIME_INVALID).notEmpty().withMessage(constants.APPOINTMENT_TIME_REQUIRED),
  body('status').optional().isIn(['booked', 'cancelled', 'rescheduled', 'no-show']).withMessage(constants.APPOINTMENT_STATUS_INVALID),
  body('source').optional().isIn(['ai-agent', 'user', 'manual']).withMessage(constants.APPOINTMENT_SOURCE_INVALID),
];

export const createElevenLabAppointmentSchema = [
  body('clinic_id').isInt().withMessage(constants.CLINIC_ID_INVALID).notEmpty().withMessage(constants.CLINIC_ID_INVALID),
  body('phone').optional().isString().withMessage(constants.PHONE_NUMBER_REQUIRED),
  body('doctor_id').optional().isInt().withMessage(constants.DOCTOR_ID_INVALID),
  body('appointment_date').isISO8601().withMessage(constants.APPOINTMENT_DATE_INVALID).notEmpty().withMessage(constants.APPOINTMENT_DATE_REQUIRED),
  body('appointment_time').isISO8601().withMessage(constants.APPOINTMENT_TIME_INVALID).notEmpty().withMessage(constants.APPOINTMENT_TIME_REQUIRED),
  body('status').optional().isIn(['booked', 'cancelled', 'rescheduled', 'no-show']).withMessage(constants.APPOINTMENT_STATUS_INVALID),
  body('source').optional().isIn(['ai-agent', 'user', 'manual']).withMessage(constants.APPOINTMENT_SOURCE_INVALID),
];

export const updateAppointmentSchema = [
  body('clinic_id').optional().isInt().withMessage(constants.CLINIC_ID_INVALID),
  body('patient_id').optional().isInt().withMessage(constants.PATIENT_ID_INVALID_APPOINTMENT),
  body('doctor_id').optional().isInt().withMessage(constants.DOCTOR_ID_INVALID),
  body('appointment_date').optional().isISO8601().withMessage(constants.APPOINTMENT_DATE_INVALID),
  body('appointment_time').optional().isISO8601().withMessage(constants.APPOINTMENT_TIME_INVALID),
  body('status').optional().isIn(['booked', 'cancelled', 'rescheduled', 'no-show']).withMessage(constants.APPOINTMENT_STATUS_INVALID),
  body('source').optional().isIn(['ai-agent', 'user', 'manual']).withMessage(constants.APPOINTMENT_SOURCE_INVALID),
]; 