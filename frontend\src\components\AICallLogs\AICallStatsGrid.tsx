import React from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { Phone, TrendingUp, Clock, CheckCircle } from "lucide-react";
import {
  AI_CALL_LOGS_STAT_TOTAL_CALLS,
  AI_CALL_LOGS_STAT_SUCCESS_RATE,
  AI_CALL_LOGS_STAT_AVG_DURATION,
  AI_CALL_LOGS_STAT_APPOINTMENTS,
} from "@/Constants/AICallLogs";

// Props for the AICallStatsGrid component
interface AICallStatsGridProps {
  totalCalls: number; // Total number of calls
  successRate: string; // Success rate percentage (e.g., '85%')
  avgDuration: string; // Average call duration (e.g., '2m 30s')
  appointments: number; // Number of appointments booked
}

/**
 * Renders a grid of stat cards for AI call logs.
 * Shows total calls, success rate, average duration, and appointments.
 */
const AICallStatsGrid: React.FC<AICallStatsGridProps> = ({
  totalCalls,
  successRate,
  avgDuration,
  appointments,
}) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
    {/* Total Calls Stat */}
    <StatCard
      title={AI_CALL_LOGS_STAT_TOTAL_CALLS}
      value={totalCalls}
      icon={<Phone className="h-8 w-8 text-blue-600" />}
    />
    {/* Success Rate Stat */}
    <StatCard
      title={AI_CALL_LOGS_STAT_SUCCESS_RATE}
      value={successRate}
      icon={<TrendingUp className="h-8 w-8 text-green-600" />}
    />
    {/* Average Duration Stat */}
    <StatCard
      title={AI_CALL_LOGS_STAT_AVG_DURATION}
      value={avgDuration}
      icon={<Clock className="h-8 w-8 text-purple-600" />}
    />
    {/* Appointments Stat */}
    <StatCard
      title={AI_CALL_LOGS_STAT_APPOINTMENTS}
      value={appointments}
      icon={<CheckCircle className="h-8 w-8 text-blue-600" />}
    />
  </div>
);

export default AICallStatsGrid;
