// DashboardScheduleOverview.tsx
// Renders a card showing an overview of the earliest 5 appointments for the dashboard.
// Uses the same design layout as AppointmentCard but simplified for dashboard view.

import React, { useEffect } from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Clock, User, Calendar } from "lucide-react";
import { DASHBOARD_SCHEDULE_OVERVIEW_TITLE } from "@/Constants/Dashboard";
import { useAppointment } from "@/hooks/useAppointment";
import { Badge } from "@/components/ui/badge";
import {
  getAppointmentStatusColor,
  capitalizeFirstLetter,
  UNKNOWN_PATIENT,
  UNASSIGNED_DOCTOR,
  NA_VALUE,
} from "@/Constants/Appointment";

/**
 * Renders a card with an overview of the earliest 5 appointments.
 */
const DashboardScheduleOverview: React.FC = () => {
  const { appointments, loading, getAllAppointments } = useAppointment();

  useEffect(() => {
    getAllAppointments();
  }, [getAllAppointments]);

  // Get the earliest 5 appointments sorted by date and time
  const earliestAppointments = appointments
    .filter(appointment => {
      const appointmentDate = new Date(appointment.appointment_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return appointmentDate >= today; // Only show future appointments
    })
    .sort((a, b) => {
      const dateA = new Date(a.appointment_date).getTime();
      const dateB = new Date(b.appointment_date).getTime();

      if (dateA !== dateB) {
        return dateA - dateB; // Sort by date ascending
      }

      const timeA = new Date(a.appointment_time).getTime();
      const timeB = new Date(b.appointment_time).getTime();

      return timeA - timeB; // Sort by time ascending
    })
    .slice(0, 5); // Get only the first 5

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2 text-blue-600" />
            {DASHBOARD_SCHEDULE_OVERVIEW_TITLE}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, idx) => (
              <div key={idx} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2 text-blue-600" />
          {DASHBOARD_SCHEDULE_OVERVIEW_TITLE}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {earliestAppointments.length > 0 ? (
            earliestAppointments.map((appointment) => (
              <div
                key={appointment.id}
                className="flex items-center justify-between p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200"
              >
                {/* Left: Patient avatar and info */}
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                    <User className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {appointment.patient ? `${appointment.patient.first_name} ${appointment.patient.last_name}` : UNKNOWN_PATIENT}
                    </p>
                    <div className="flex items-center space-x-3 text-sm text-gray-600">
                      {/* Appointment date */}
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {appointment.appointment_date
                          ? new Date(appointment.appointment_date).toLocaleDateString("en-US", {
                              month: "short",
                              day: "numeric",
                            })
                          : NA_VALUE}
                      </span>
                      {/* Appointment time */}
                      <span className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {appointment.appointment_time
                          ? new Date(appointment.appointment_time).toLocaleTimeString("en-US", {
                              hour: "numeric",
                              minute: "numeric",
                              hour12: true,
                            })
                          : NA_VALUE}
                      </span>
                      {/* Doctor name */}
                      <span>{appointment.doctor?.doctor_name || UNASSIGNED_DOCTOR}</span>
                    </div>
                  </div>
                </div>
                {/* Right: Status badge */}
                <div className="flex items-center">
                  <Badge
                    variant="outline"
                    className={
                      getAppointmentStatusColor(appointment.status || "") +
                      " transition-colors duration-200"
                    }
                  >
                    {capitalizeFirstLetter(appointment.status || "Unknown")}
                  </Badge>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p>No upcoming appointments</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardScheduleOverview;
