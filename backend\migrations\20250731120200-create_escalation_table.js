import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('escalations', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    clinic_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'clinics',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    patient_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'patients',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    summary: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    transcript: {
      type: Sequelize.ARRAY(Sequelize.TEXT),
      allowNull: true,
    },
    status: {
      type: Sequelize.ENUM('open', 'assigned', 'in_progress', 'resolved'),
      allowNull: false,
      defaultValue: 'open',
    },
    tags: {
      type: Sequelize.TEXT,
      allowNull: false,
    },
    assignee_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('escalations');
};