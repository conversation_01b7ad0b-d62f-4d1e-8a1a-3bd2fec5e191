// GENERAL CONSTANTS
export const HELLO_WORLD = 'Hello, World!';
export const INTERNAL_SERVER_ERROR = 'Internal server error';
export const FIELD_REQUIRED = 'Field is required';
export const NAME_REQUIRED = 'Name is required';

// AUTHENTICATION & TOKEN CONSTANTS
export const REFRESH_TOKEN_COOKIE_NAME = 'refreshToken';
export const ACCESS_TOKEN_COOKIE_NAME = 'accessToken';
export const ACCESS_TOKEN_EXPIRY_MS = 60 * 60 * 1000; // 1 hour
export const REFRESH_TOKEN_EXPIRY_MS = 7 * 24 * 60 * 60 * 1000; // 7 days
export const RESET_TOKEN_EXPIRY_MS = 60 * 60 * 1000; // 1 hour
export const TOKEN_BYTE_LENGTH = 64; // default for secure token
export const REFRESH_TOKEN_BYTE_LENGTH = 48;
export const RESET_TOKEN_BYTE_LENGTH = 48;
export const COOKIE_SAME_SITE = 'strict';

// Token validation messages
export const TOKEN_REQUIRED = 'Token Required';
export const INVALID_TOKEN = 'Invalid token.';
export const TOKEN_EXPIRY = '1h'; // 1 hour for production
export const TOKEN_EXPIRED = 'Token Expired';
export const REFRESH_TOKEN_REQUIRED = 'Refresh token required';
export const INVALID_OR_EXPIRED_REFRESH_TOKEN =
  'Invalid or expired refresh token';
export const INVALID_REFRESH_TOKEN = 'Invalid refresh token';
export const TOKEN_EXPIRED_ERROR = 'TokenExpiredError';
export const JSON_WEB_TOKEN_ERROR = 'JsonWebTokenError';

// Authentication messages
export const SIGNUP_SUCCESSFULLY = 'Signup successfully';
export const LOGIN_SUCCESSFULLY = 'Login successfully';
export const LOGGED_OUT_SUCCESSFULLY = 'Logged out successfully';
export const PASSWORD_INCORRECT = 'Incorrect Password';
export const PROTECTED_ROUTE_SUCCESS = 'You have accessed a protected route!';
export const ERROR_GENERATING_TOKENS_SIGNUP =
  'Error generating tokens during signup';
export const SIGNUP_FAILED_PREFIX = 'Signup failed: ';
export const TOKEN_REFRESHED_SUCCESSFULLY = 'Token refreshed successfully';

// USER-RELATED CONSTANTS
export const USER_NOT_FOUND_MESSAGE = 'User not found';
export const USER_EXIST_EMAIL = 'User with this email already exists';
export const USER_EXIST_PHONE = 'User with this phone number already exists';
export const USER_NOT_FOUND = 'User not found';
export const USER_DOES_NOT_EXIST = 'Invalid credentials';
export const USER_ALREADY_EXISTS_ERROR =
  'User with the same email already exists.';
export const USERS_FETCHED_SUCCESSFULLY = 'Users fetched successfully';
export const USER_FETCHED_SUCCESSFULLY = 'User fetched successfully';
export const USER_CREATED_SUCCESSFULLY = 'User created successfully';
export const USER_UPDATED_SUCCESSFULLY = 'User updated successfully';
export const USER_DELETED_SUCCESSFULLY = 'User deleted successfully';

// Profile related constants
export const PROFILE_FETCHED_SUCCESSFULLY = 'Profile fetched successfully';
export const PROFILE_UPDATED_SUCCESSFULLY = 'Profile updated successfully';

// ROLE-RELATED CONSTANTS
export const ADMIN_ROLE = 'admin';
export const EMPLOYEE_ROLE = 'employee';
export const ACCESS_DENIED =
  'Access denied. User does not have sufficient permissions.';
export const INVALID_ROLE_PROVIDED = 'Invalid role provided';
export const ROLE_NOT_FOUND = 'Role not found';
export const ROLE_FETCHED_SUCCESSFULLY = 'Role fetched successfully';
export const ERROR_FETCHING_ROLE = 'Error fetching role';
export const ROLE_REQUIRED = 'Role is required';
export const INVALID_ROLE = 'Invalid role';

// PATIENT-RELATED CONSTANTS
// Allowed patient fields for upload (should match Patient model columns)
export const PATIENT_ALLOWED_FIELDS = [
  'id',
  'clinic_id',
  'first_name',
  'last_name',
  'email',
  'phone_number',
  'country_code',
  'last_visit',
  'tags',
  'doctors',
  'preferences',
  'dob',
  'gender',
  'address',
  'is_active',
  'is_deleted',
  'created_by',
  'updated_by',
  'created_at',
  'updated_at',
  'custom',
];

// Patient success messages
export const PATIENT_CREATED_SUCCESSFULLY = 'Patient created successfully';
export const PATIENT_UPDATED_SUCCESSFULLY = 'Patient updated successfully';
export const PATIENT_DELETED_SUCCESSFULLY = 'Patient deleted successfully';
export const PATIENT_FETCHED_SUCCESSFULLY = 'Patient fetched successfully';
export const PATIENTS_FETCHED_SUCCESSFULLY = 'Patients fetched successfully';
export const PATIENT_LIST_UPLOADED_SUCCESSFULLY =
  'Patient list uploaded and inserted successfully.';
export const BATCH_CALL_JOB_SUBMITTED_SUCCESSFULLY = 'Batch call job submitted successfully';

// Patient error messages
export const PATIENT_NOT_FOUND = 'Patient not found';
export const PATIENT_ALREADY_EXISTS_ERROR =
  'Patient with the same email or phone number already exists.';
export const PATIENT_FILE_REQUIRED = 'Patient file is required.';
export const PATIENT_FILE_TYPE_INVALID =
  'Invalid file type. Only CSV or Excel files are allowed.';

// Patient validation messages
export const PATIENT_DOCTORS_ARRAY = 'Doctors must be an array of doctor IDs';
export const PATIENT_FIRST_NAME_REQUIRED = 'First name is required';
export const PATIENT_LAST_NAME_REQUIRED = 'Last name is required';
export const PATIENT_TAGS_ARRAY = 'Tags must be an array';
export const PATIENT_PREFERENCES_OBJECT = 'Preferences must be an object';
export const PATIENT_GENDER_STRING = 'Gender must be a string';
export const PATIENT_ADDRESS_STRING = 'Address must be a string';

// CLINIC-RELATED CONSTANTS
// Clinic success messages
export const CLINIC_CREATED_SUCCESSFULLY = 'Clinic created successfully';
export const CLINICS_FETCHED_SUCCESSFULLY = 'Clinics fetched successfully';
export const CLINIC_FETCHED_SUCCESSFULLY = 'Clinic fetched successfully';
export const CLINIC_UPDATED_SUCCESSFULLY = 'Clinic updated successfully';
export const CLINIC_DELETED_SUCCESSFULLY = 'Clinic deleted successfully';

// Clinic error messages
export const CLINIC_NOT_FOUND = 'Clinic not found';
export const CLINIC_ALREADY_EXISTS =
  'Clinic with this email or phone number already exists';
export const CLINIC_EMAIL_OR_PHONE_EXISTS =
  'Clinic with the same email or phone number already exists.';

// Clinic validation messages
export const CLINIC_NAME_REQUIRED = 'Clinic name is required';
export const CLINIC_TIMEZONE_REQUIRED = 'Clinic timezone is required';
export const INVALID_CLINIC_EMAIL = 'Invalid clinic email address';
export const INVALID_CLINIC_PHONENUMBER = 'Invalid clinic phone number';
export const CLINIC_NAME_CANNOT_BE_EMPTY = 'Clinic name cannot be empty';
export const CLINIC_TIMEZONE_CANNOT_BE_EMPTY =
  'Clinic timezone cannot be empty';
export const CLINIC_REACTIVATION_DAYS_CANNOT_BE_EMPTY = 'Clinic reactivation days cannot be empty or negative';
export const REACTIVATION_TIME_CANNOT_BE_EMPTY = 'Time must be in HH:mm format (e.g., "10:00")';
export const CLINIC_IS_ACTIVE_BOOLEAN = 'Clinic is_active must be boolean';
export const CLINIC_ID_INVALID = 'clinic_id must be an integer or null';

// Reactivation-related constants
export const REACTIVATIONS_FETCHED_SUCCESSFULLY = 'Reactivations fetched successfully';
export const REACTIVATION_STATS_FETCHED_SUCCESSFULLY = 'Reactivation statistics fetched successfully';

// DOCTOR-RELATED CONSTANTS
// Doctor success messages
export const DOCTOR_CREATED_SUCCESSFULLY = 'Doctor created successfully';
export const DOCTORS_FETCHED_SUCCESSFULLY = 'Doctors fetched successfully';
export const DOCTOR_FETCHED_SUCCESSFULLY = 'Doctor fetched successfully';
export const DOCTOR_UPDATED_SUCCESSFULLY = 'Doctor updated successfully';
export const DOCTOR_DELETED_SUCCESSFULLY = 'Doctor deleted successfully';

// Doctor error messages
export const DOCTOR_NOT_FOUND = 'Doctor not found';
export const DOCTOR_ALREADY_EXISTS = 'Doctor already exists';
export const DOCTOR_EMAIL_OR_PHONE_EXISTS =
  'Doctor with the same email or phone number already exists.';

// Doctor validation messages
export const DOCTOR_NAME_REQUIRED = 'Doctor name is required';
export const DOCTOR_CLINIC_ID_REQUIRED =
  'Clinic ID is required and must be an integer';
export const INVALID_DOCTOR_EMAIL = 'Invalid doctor email';
export const INVALID_DOCTOR_PHONENUMBER = 'Invalid doctor phone number';
export const DOCTOR_NAME_CANNOT_BE_EMPTY = 'Doctor name cannot be empty';
export const DOCTOR_IS_ACTIVE_BOOLEAN = 'is_active must be boolean';

// VALIDATION CONSTANTS
export const INVALID_EMAIL = 'Invalid email address';
export const INVALID_PHONE_NUMBER = 'Invalid phone number';
export const PHONE_NUMBER_MIN_LENGTH =
  'Phone number must be at least 10 digits';
export const INVALID_PASSWORD_LENGTH =
  'Password must be between 8 and 15 characters';
export const INVALID_PASSWORD_FORMAT =
  'Password must contain at least one alphabet, one number, and one special character';
export const WORKING_HOURS_STRING = 'Working hours must be a string';
export const WORKING_DAYS_STRING = 'Working days must be a string';
export const EXPERIENCE_YEARS_INTEGER = 'Experience years must be an integer';

// DATE FORMAT CONSTANTS
export const DATE_FORMAT = 'DD-MM-YYYY';
export const LOG_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

// PASSWORD-RELATED CONSTANTS
// Password reset messages
export const PASSWORD_RESET_LINK_SENT =
  'Password reset link sent to your email.';
export const PASSWORD_RESET_EMAIL_SENT =
  'Password reset instructions sent to your email.';
export const PASSWORD_CHANGED_SUCCESSFULLY = 'Password changed successfully';
export const PASSWORD_CONFIRM_PASSWORD_MISMATCH =
  'New password and Confirm password do not match.';
export const NEW_PASSWORD_REQUIRED = 'New password is required.';
export const CURRENT_AND_NEW_PASSWORD_REQUIRED =
  'Current and new password are required.';
export const CURRENT_PASSWORD_INCORRECT = 'Current password is incorrect.';
export const INVALID_REQUEST_TOKEN_OR_AUTH =
  'Invalid request. Token or authentication required.';

// Password reset errors
export const INVALID_OR_EXPIRED_RESET_TOKEN = 'Invalid or expired reset token.';
export const INVALID_RESET_TOKEN = 'Invalid reset token';
export const RESET_PASSWORD_TOKEN_EXPIRED = 'Reset password token expired';
export const ERROR_GENERATING_RESET_PASSWORD_TOKEN =
  'Error generating reset password token';
export const ERROR_PASSWORD_RESET_EMAIL_SENT =
  'Error sending password reset email';
export const ERROR_PASSWORD_RESET_FAILED = 'Failed to reset password.';
export const ERROR_VERIFYING_RESET_TOKEN = 'Error in verifying the reset token';

// Password reset configuration
export const RESET_PASSWORD_TOKEN_EXPIRY = '1h';

// EMAIL-RELATED CONSTANTS
export const EMAIL_SEND_FAILED = 'Email send failed';
export const EMAIL_SENT_SUCCESS = 'Email sent successfully';

// SMS-RELATED CONSTANTS
export const SMS_SEND_FAILED = 'SMS send failed';
export const SMS_SENT_SUCCESS = 'SMS sent successfully';

// APPOINTMENT EMAIL CONSTANTS
export const APPOINTMENT_CONFIRMATION_EMAIL_SENT = (patientData, appointmentData) => `Appointment confirmation email sent to ${patientData.email} for appointment ${appointmentData.id}`;
export const APPOINTMENT_CONFIRMATION_EMAIL_FAILED = (appointmentData) => `Failed to send appointment confirmation email for appointment ${appointmentData.id}:`;
export const APPOINTMENT_UPDATE_EMAIL_SENT = (patientData, appointmentData) => `Appointment update email sent to ${patientData.email} for appointment ${appointmentData.id}`;
export const APPOINTMENT_UPDATE_EMAIL_FAILED = (appointmentData) => `Failed to send appointment update email for appointment ${appointmentData.id}:`;
export const APPOINTMENT_CANCELLATION_EMAIL_SENT = (patientData, appointmentData) => `Appointment cancellation email sent to ${patientData.email} for appointment ${appointmentData.id}`;
export const APPOINTMENT_CANCELLATION_EMAIL_FAILED = (appointmentData) => `Failed to send appointment cancellation email for appointment ${appointmentData.id}:`;

// APPOINTMENT SMS CONSTANTS
export const APPOINTMENT_CONFIRMATION_SMS_SENT = (patientData, appointmentData) => `Appointment confirmation SMS sent to ${patientData.phone_number} for appointment ${appointmentData.id}`;
export const APPOINTMENT_CONFIRMATION_SMS_FAILED = (appointmentData) => `Failed to send appointment confirmation SMS for appointment ${appointmentData.id}:`;
export const APPOINTMENT_UPDATE_SMS_SENT = (patientData, appointmentData) => `Appointment update SMS sent to ${patientData.phone_number} for appointment ${appointmentData.id}`;
export const APPOINTMENT_UPDATE_SMS_FAILED = (appointmentData) => `Failed to send appointment update SMS for appointment ${appointmentData.id}:`;
export const APPOINTMENT_CANCELLATION_SMS_SENT = (patientData, appointmentData) => `Appointment cancellation SMS sent to ${patientData.phone_number} for appointment ${appointmentData.id}`;
export const APPOINTMENT_CANCELLATION_SMS_FAILED = (appointmentData) => `Failed to send appointment cancellation SMS for appointment ${appointmentData.id}:`;

// APPOINTMENT NOTIFICATION CONSTANTS
export const PATIENT_NOT_FOUND_FOR_NOTIFICATION = 'Patient not found for notification';
export const DOCTOR_NOT_FOUND_FOR_NOTIFICATION = 'Doctor not found for notification';
export const CLINIC_NOT_FOUND_FOR_NOTIFICATION = 'Clinic not found for notification';
export const FAILED_TO_FETCH_NOTIFICATION_DATA = 'Failed to fetch required data for notification';
export const EMAIL_SERVICE_DISABLED_FOR_CLINIC = 'Email service disabled for this clinic';
export const SMS_SERVICE_DISABLED_FOR_CLINIC = 'SMS service disabled for this clinic';

// APPOINTMENT NOTIFICATION RESULTS
export const NOTIFICATION_EMAIL_SENT = 'Email notification sent successfully';
export const NOTIFICATION_EMAIL_FAILED = 'Email notification failed';
export const NOTIFICATION_SMS_SENT = 'SMS notification sent successfully';
export const NOTIFICATION_SMS_FAILED = 'SMS notification failed';

// PASSWORD GENERATION CONSTANTS
export const UPPERCASE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
export const LOWERCASE = 'abcdefghijklmnopqrstuvwxyz';
export const NUMBERS = '**********';
export const SYMBOLS = '@$!%*?&';

// TEMPLATE-RELATED CONSTANTS
// Template success messages
export const TEMPLATE_CREATED_SUCCESSFULLY = 'Template created successfully';
export const TEMPLATE_UPDATED_SUCCESSFULLY = 'Template updated successfully';
export const TEMPLATE_DELETED_SUCCESSFULLY = 'Template deleted successfully';
export const TEMPLATE_FETCHED_SUCCESSFULLY = 'Template fetched successfully';
export const TEMPLATES_FETCHED_SUCCESSFULLY = 'Templates fetched successfully';

// Template error messages
export const TEMPLATE_NOT_FOUND = 'Template not found';
export const TEMPLATE_ALREADY_EXISTS_ERROR =
  'Template with the same name and type already exists.';

// Template validation messages
export const TEMPLATE_TYPE_REQUIRED = 'Template type is required';
export const TEMPLATE_TYPE_INVALID =
  'Template type must be one of: sms, email, call_prompt';
export const TEMPLATE_NAME_REQUIRED = 'Template name is required';
export const TEMPLATE_NAME_STRING = 'Template name must be a string';
export const TEMPLATE_CONTENT_REQUIRED = 'Template content is required';
export const TEMPLATE_CONTENT_STRING = 'Template content must be a string';

// CAMPAIGN-RELATED CONSTANTS
// Campaign success messages
export const CAMPAIGN_CREATED_SUCCESSFULLY = 'Campaign created successfully';
export const CAMPAIGN_UPDATED_SUCCESSFULLY = 'Campaign updated successfully';
export const CAMPAIGN_DELETED_SUCCESSFULLY = 'Campaign deleted successfully';
export const CAMPAIGN_FETCHED_SUCCESSFULLY = 'Campaign fetched successfully';
export const CAMPAIGNS_FETCHED_SUCCESSFULLY = 'Campaigns fetched successfully';

// Campaign error messages
export const CAMPAIGN_NOT_FOUND = 'Campaign not found';
export const CAMPAIGN_ALREADY_EXISTS_ERROR =
  'Campaign with the same name and clinic already exists.';

// Campaign validation messages
export const CAMPAIGN_NAME_REQUIRED = 'Campaign name is required';
export const CAMPAIGN_NAME_STRING = 'Campaign name must be a string';
export const CAMPAIGN_DESCRIPTION_STRING =
  'Campaign description must be a string';
export const CAMPAIGN_TYPE_REQUIRED = 'Campaign type is required';
export const CAMPAIGN_TYPE_INVALID =
  'Campaign type must be one of: call, sms, email';
export const CAMPAIGN_FREQUENCY_INTERVAL_STRING =
  'Campaign frequency interval must be a string';
export const CAMPAIGN_DURATION_DAYS_INTEGER =
  'Campaign duration days must be a positive integer';
export const CAMPAIGN_FILTERS_OBJECT = 'Campaign filters must be an object';

// ESCALATION-RELATED CONSTANTS
// Escalation success messages
export const ESCALATION_CREATED_SUCCESSFULLY = 'Escalation created successfully';
export const ESCALATION_CREATED_WITH_TRANSCRIPT_SUCCESSFULLY = 'Escalation created successfully with transcript';
export const ESCALATION_UPDATED_SUCCESSFULLY = 'Escalation updated successfully';
export const ESCALATION_DELETED_SUCCESSFULLY = 'Escalation deleted successfully';
export const ESCALATION_FETCHED_SUCCESSFULLY = 'Escalation fetched successfully';
export const ESCALATIONS_FETCHED_SUCCESSFULLY = 'Escalations fetched successfully';

// AI CALL LOG RELATED CONSTANTS
export const AI_CALL_LOG_CREATED_SUCCESSFULLY = 'AI call log created successfully';
export const AI_CALL_LOG_UPDATED_SUCCESSFULLY = 'AI call log updated successfully';
export const AI_CALL_LOG_DELETED_SUCCESSFULLY = 'AI call log deleted successfully';
export const AI_CALL_LOG_FETCHED_SUCCESSFULLY = 'AI call log fetched successfully';
export const AI_CALL_LOGS_FETCHED_SUCCESSFULLY = 'AI call logs fetched successfully';
export const AI_CALL_LOG_NOT_FOUND = 'AI call log not found';

// AI Call Log validation messages
export const DIRECTION_REQUIRED = 'Direction is required';
export const DIRECTION_INVALID = 'Direction must be either inbound or outbound';
export const CALL_TIME_DATE_REQUIRED = 'Call time and date is required';
export const CALL_TIME_DATE_INVALID = 'Call time and date must be a valid ISO 8601 date';
export const CALL_DURATION_REQUIRED = 'Call duration is required';
export const CALL_DURATION_INVALID = 'Call duration must be a positive integer (in seconds)';
export const CALL_STATUS_REQUIRED = 'Call status is required';
export const CALL_STATUS_INVALID = 'Call status must be one of: successful, no_answer, missed_connection, busy, failed, voicemail';
export const CONVERSATION_SUMMARY_INVALID = 'Conversation summary must be a string';
export const CONV_ID_INVALID = 'Conversation ID must be a string';
export const CALL_SUMMARY_TITLE_INVALID = 'Call summary title must be a string';
export const CLINIC_ID_INVALID_AI_CALL_LOG = 'Clinic ID must be an integer';
export const PATIENT_ID_INVALID_AI_CALL_LOG = 'Patient ID must be an integer';

// AI Call Log enums
export const AI_CALL_LOG_DIRECTIONS = ['inbound', 'outbound'];
export const AI_CALL_LOG_STATUSES = ['successful', 'no_answer', 'missed_connection', 'busy', 'failed', 'voicemail'];

// AI Call Log error messages
export const ERROR_CREATING_AI_CALL_LOG = 'Error creating AI call log';
export const ERROR_FETCHING_AI_CALL_LOGS = 'Error fetching AI call logs';

// Escalation error messages
export const ESCALATION_NOT_FOUND = 'Escalation not found';
export const CONVERSATION_ID_REQUIRED = 'Conversation ID is required for transcript integration';
export const ELEVENLABS_API_KEY_REQUIRED = 'ElevenLabs API key is required for transcript integration';
export const FAILED_TO_FETCH_TRANSCRIPT = 'Failed to fetch transcript';

// Escalation validation messages
export const PATIENT_ID_INVALID = 'Patient ID must be a valid integer';
export const PATIENT_ID_REQUIRED = 'Patient ID is required';
export const SUMMARY_REQUIRED = 'Summary is required';
export const SUMMARY_STRING = 'Summary must be a string';
export const TRANSCRIPT_ARRAY = 'Transcript must be an array';
export const STATUS_INVALID = 'Status must be one of: open, assigned, in_progress, resolved';
export const STATUS_REQUIRED = 'Status is required';
export const TAGS_INVALID = 'Tags must be one of: ai_rejection, busy';
export const TAGS_REQUIRED = 'Tags is required';
export const ASSIGNEE_ID_INVALID = 'Assignee ID must be a valid integer';
export const CONVERSATION_ID_STRING = 'Conversation ID must be a string';
export const API_KEY_STRING = 'API key must be a string';
export const API_KEY_REQUIRED = 'ElevenLabs API key is required';
export const TRANSCRIPT_STRING = 'Transcript must be a string';
// APPOINTMENT-RELATED CONSTANTS
// Appointment success messages
export const APPOINTMENT_CREATED_SUCCESSFULLY = 'Appointment created successfully';
export const APPOINTMENT_UPDATED_SUCCESSFULLY = 'Appointment updated successfully';
export const APPOINTMENT_DELETED_SUCCESSFULLY = 'Appointment deleted successfully';
export const APPOINTMENT_FETCHED_SUCCESSFULLY = 'Appointment fetched successfully';
export const APPOINTMENTS_FETCHED_SUCCESSFULLY = 'Appointments fetched successfully';

// Appointment error messages
export const APPOINTMENT_NOT_FOUND = 'Appointment not found';
export const APPOINTMENT_ALREADY_EXISTS_ERROR = 'Appointment already exists';

// Appointment validation messages
export const APPOINTMENT_DATE_REQUIRED = 'Appointment date is required';
export const APPOINTMENT_TIME_REQUIRED = 'Appointment time is required';
export const APPOINTMENT_STATUS_INVALID = 'Appointment status must be one of: booked, cancelled, rescheduled, no-show';
export const APPOINTMENT_SOURCE_INVALID = 'Appointment source must be one of: ai-agent, user, manual';
export const APPOINTMENT_DATE_INVALID = 'Appointment date must be a valid date';
export const APPOINTMENT_TIME_INVALID = 'Appointment time must be a valid date/time';
export const DOCTOR_ID_INVALID = 'Doctor ID must be a valid integer';
export const PHONE_NUMBER_REQUIRED = 'Phone nummber is required'

// Time slot availability constants
export const TIME_SLOT_CONFLICT = 'Time slot is not available';
export const TIME_SLOT_AVAILABLE = 'Time slot is available';
export const TIME_SLOT_CHECK_SUCCESS = 'Time slot availability checked successfully';
export const TIME_SLOT_CONFLICT_DETAILS = 'Time slot conflicts with existing appointment';
export const TIME_SLOT_NOT_AVAILABLE = 'Time slot is not available';
export const MISSING_REQUIRED_PARAMETERS = 'Missing required parameters: doctor_id, appointment_date, appointment_time';

// Appointment audit log constants
export const FAILED_TO_LOG_APPOINTMENT_AUDIT = 'Failed to log appointment audit';
export const FAILED_TO_FETCH_APPOINTMENT_AUDIT_LOGS = 'Failed to fetch appointment audit logs';

// Available time slots constants
export const AVAILABLE_SLOTS_RETRIEVED_SUCCESSFULLY = 'Available time slots retrieved successfully';
export const MISSING_DOCTOR_DATE_PARAMETERS = 'Missing required parameters: doctor_id, date';

// Server constants
export const SERVER_STARTED = 'Server is running on port';
export const SERVER_START_FAILED = 'Failed to start server';

// elevenlabs constants
export const BATCH_CALL_URL = 'https://api.elevenlabs.io/v1/convai/batch-calling/submit'

// CRON SERVICE CONSTANTS
export const CRON_DAILY_DESCRIPTION = 'Daily outbound calls for clinics with reactivation settings';
export const CRON_CONTINUOUS_DESCRIPTION = 'Continuous outbound calls every 6 hours';
export const CRON_API_CALL_DESCRIPTION = 'API calls to /outbound endpoint every 2 hours';
export const CRON_OVERALL_DESCRIPTION = 'Automated outbound calls for all clinics with configurable schedules (US Eastern Time)';
export const CRON_NEXT_RUN_DAILY = 'Daily at midnight US Eastern Time';
export const CRON_NEXT_RUN_CONTINUOUS = 'Every 6 hours (US Eastern Time)';
export const CRON_NEXT_RUN_API_CALL = 'Every 2 hours (US Eastern Time)';
export const CRON_STATUS_RUNNING = 'running';
export const CRON_TIMEZONE = 'America/New_York';

// CRON SERVICE SUCCESS MESSAGES
export const CRON_SUCCESS_PROCESSED_CLINIC = (clinicId) => `Successfully processed clinic ${clinicId}`;
export const CRON_SUCCESS_PROCESSED_ALL_PATIENTS = (clinicId) => `Successfully processed all patients for clinic ${clinicId}`;
export const CRON_SUCCESS_CALLED_OUTBOUND_API = (clinicId) => `Successfully called /outbound API for clinic ${clinicId}`;
export const CRON_SUCCESS_TRIGGERED_OUTBOUND_CALLS = (clinicId) => `Successfully triggered outbound calls for clinic ${clinicId}`;
export const CRON_SUCCESS_NO_INACTIVE_PATIENTS = (clinicId) => `No inactive patients found for clinic ${clinicId}`;
export const CRON_SUCCESS_NO_PATIENTS_FOUND = (clinicId) => `No patients found for clinic ${clinicId}`;

// PATIENT CONTROLLER CONSTANTS
export const CLINIC_ID_REQUIRED = 'clinic_id is required';
export const CLINIC_NOT_FOUND_PATIENT_CONTROLLER = 'Clinic not found';
export const OUTBOUND_CALLS_TRIGGERED_SUCCESSFULLY = 'Outbound calls triggered successfully';
export const CRON_JOB_STATUS_RETRIEVED_SUCCESSFULLY = 'Cron job status retrieved successfully';
export const API_CALL_TRIGGERED_SUCCESSFULLY = 'API call triggered successfully';
export const NO_VALID_PATIENT_RECORDS = 'No valid patient records found in the file.';

// TIMEZONE CONSTANTS
export const INVALID_DATE_FORMAT = 'Invalid date format provided';
export const ERROR_CONVERTING_TIMEZONE = 'Error converting timezone';

// WEBHOOK-RELATED CONSTANTS
export const WEBHOOK_PROCESSED_SUCCESSFULLY = 'Webhook processed successfully';
export const WEBHOOK_SIGNATURE_VERIFICATION_FAILED = 'Webhook signature verification failed';
export const WEBHOOK_INVALID_PAYLOAD = 'Invalid webhook payload';
export const WEBHOOK_MISSING_SIGNATURE = 'Webhook signature is missing';
export const WEBHOOK_SECRET_MISSING = 'Webhook secret is not configured';
export const WEBHOOK_TRANSCRIPT_FETCHED_SUCCESSFULLY = 'Transcript fetched successfully from ElevenLabs';
export const WEBHOOK_TRANSCRIPT_FETCH_FAILED = 'Failed to fetch transcript from ElevenLabs';

// Additional webhook constants
export const WEBHOOK_INVALID_JSON_PAYLOAD = 'Invalid JSON payload';
export const WEBHOOK_ELEVENLABS_API_KEY_NOT_CONFIGURED = 'ElevenLabs API key is not configured';
export const WEBHOOK_NO_CALL_LOG_FOUND = 'No call log found with conversation_id: {id}, cannot update transcript';
export const WEBHOOK_TRANSCRIPT_UPDATED_IN_DB = 'Transcript updated in DB for conversation_id: {id}';
export const WEBHOOK_FAILED_TO_UPDATE_TRANSCRIPT = 'Failed to update transcript in DB: {error}';
export const WEBHOOK_TRANSCRIPT_EMPTY = 'Transcript empty for conversation_id: {id}';
export const WEBHOOK_TRANSCRIPT_EMPTY_OR_NULL = 'Transcript is empty or null';
export const WEBHOOK_SERVICE_HEALTHY = 'Webhook service is healthy';
export const WEBHOOK_SIGNATURE_VERIFICATION_SUCCESSFUL = 'Webhook signature verification successful';
export const WEBHOOK_SECRET_CONFIGURED_NO_SIGNATURE = 'WEBHOOK_SECRET configured but no signature found, continuing without verification';
export const WEBHOOK_SECRET_NOT_CONFIGURED = 'WEBHOOK_SECRET not configured, skipping signature verification';
export const WEBHOOK_ATTEMPTING_SIGNATURE_VERIFICATION = 'Attempting signature verification with secret';
export const WEBHOOK_SIGNATURE_VERIFICATION_FAILED_LOG = 'Signature verification failed';
export const WEBHOOK_BODY_ALREADY_PARSED = 'Body was already parsed by middleware';
export const WEBHOOK_JSON_PARSED_SUCCESSFULLY = 'JSON parsed successfully from raw body';
export const WEBHOOK_JSON_PARSING_FAILED = 'JSON parsing failed: {error}';
export const WEBHOOK_PAYLOAD_STRUCTURE = 'Payload structure:';
export const WEBHOOK_PAYLOAD_VALIDATION_SUCCESSFUL = 'Payload validation successful';
export const WEBHOOK_PAYLOAD_VALIDATION_FAILED = 'Payload validation failed';
export const WEBHOOK_STARTING_TRANSCRIPT_FETCH = 'Starting transcript fetch for conversation: {id}';
export const WEBHOOK_TRANSCRIPT_FETCH_COMPLETED = 'Transcript fetch completed:';
export const WEBHOOK_TRANSCRIPT_CONTENT = 'Transcript content:';
export const WEBHOOK_TRANSCRIPT_EMPTY_WARNING = 'Transcript is empty or null';
export const WEBHOOK_PROCESSING_WEBHOOK = 'Processing webhook';
export const WEBHOOK_RECEIVED = 'Webhook received - Body length: {length} characters';
export const WEBHOOK_HEADERS = 'Headers:';
export const WEBHOOK_HEALTH_CHECK_REQUESTED = 'Webhook health check requested';
export const WEBHOOK_HEALTH_CHECK_ERROR = 'Webhook health check error: {error}';
export const WEBHOOK_TRANSCRIPT_SUMMARY = 'Transcript summary:';
export const WEBHOOK_MISSING_CONVERSATION_ID = 'Missing';
export const WEBHOOK_NO_MESSAGE = 'No message';
export const WEBHOOK_STATUS_OK = 'ok';
export const WEBHOOK_OBJECT_TYPE = 'object';