import React, { useState, useRef } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  SEARCH_BAR,
  ACCESSIBILITY,
  CSS_CLASSES,
  ICONS,
} from "@/Constants/Header";

interface SearchBarProps {
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({ className = "" }) => {
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSearchClick = () => {
    setSearchOpen(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!e.target.value) setSearchOpen(false);
  };

  return (
    <div className={`${CSS_CLASSES.SEARCH_CONTAINER} ${className}`}>
      <div
        className={`${CSS_CLASSES.SEARCH_INPUT_CONTAINER} ${
          searchOpen ? "w-64 shadow border border-gray-200" : "w-10"
        } h-10`}
        style={{ position: "relative", overflow: "hidden" }}
      >
        <button
          onClick={handleSearchClick}
          aria-label={ACCESSIBILITY.SEARCH_BUTTON}
          className={CSS_CLASSES.SEARCH_BUTTON}
          tabIndex={searchOpen ? -1 : 0}
          style={{ cursor: "pointer", zIndex: 2 }}
        >
          <Search
            className={`${ICONS.SEARCH} transition-colors ${
              searchOpen ? "text-gray-400" : ""
            }`}
          />
        </button>
        <Input
          ref={inputRef}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onBlur={handleBlur}
          placeholder={SEARCH_BAR.PLACEHOLDER}
          className={`${CSS_CLASSES.SEARCH_INPUT} ${
            searchOpen
              ? "pl-2 pr-2 w-full opacity-100"
              : "w-0 opacity-0 pointer-events-none"
          }`}
          style={{
            height: 40,
            fontSize: 14,
            minWidth: 0,
            boxShadow: "none",
          }}
          tabIndex={searchOpen ? 0 : -1}
        />
      </div>
    </div>
  );
};

export default SearchBar; 