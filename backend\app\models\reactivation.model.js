import { DataTypes } from 'sequelize';

const ReactivationModel = (sequelize) => {
  const Reactivation = sequelize.define(
    'Reactivation',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      clinic_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'clinics',
          key: 'id',
        },
      },
      batch_name: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: 'Name of the reactivation batch (e.g., reactivation-2025-01-09-batch-1)',
      },
      status: {
        type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
      },
      patient_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Total number of patients in this batch',
      },
      scheduled_time_unix: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: 'Unix timestamp when the batch should be executed',
      },
      scheduled_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Human readable scheduled time',
      },
      executed_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'When the batch was actually executed',
      },
      completed_time: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'When the batch completed (success or failure)',
      },
      elevenlabs_batch_id: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'External ID from ElevenLabs for tracking',
      },
      call_results: {
        type: DataTypes.JSONB,
        allowNull: true,
        comment: 'Detailed results of the batch call including success/failure counts',
      },
      error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Error message if the batch failed',
      },
      reactivation_days: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Number of days since last visit used to determine inactive patients',
      },
      created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      created_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      updated_by: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    },
    {
      tableName: 'reactivations',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      underscored: true,
      indexes: [
        {
          fields: ['clinic_id'],
        },
        {
          fields: ['status'],
        },
        {
          fields: ['scheduled_time_unix'],
        },
        {
          fields: ['created_at'],
        },
      ],
    }
  );

  return Reactivation;
};

export default ReactivationModel;
