export const LAYOUT_CLINIC_NAME = "DownTown Family Clinic";

export const LAYOUT_CLINIC_SUBTITLE = "Smart Front Desk Assistant";

export const LAYOUT_SEARCH_PLACEHOLDER = "Search patients, appointments...";
export const LAYOUT_USER_NAME = "Dr. <PERSON>";

export const HEADER_NOTIFICATIONS = "Notifications";
export const HEADER_SETTINGS = "Settings";
export const HEADER_PROFILE = "Profile";

export const SIDEBAR_MAIN_MENU = "MAIN MENU";

export const SIDEBAR_MENU_ITEMS = [
  { id: "dashboard", label: "Dashboard", icon: "LayoutDashboard" },
  { id: "appointments", label: "Appointments", icon: "Calendar" },
  { id: "templates", label: "Templates", icon: "FileText" },
  { id: "ai-calls", label: "AI Call Logs", icon: "Phone" },
  { id: "reactivation", label: "Reactivation Program", icon: "RefreshCw" },
  { id: "escalation", label: "Escalation", icon: "AlertTriangle" },
  // { id: "analytics", label: "Analytics", icon: "BarChart3" },
  { 
    id: "settings", 
    label: "Settings", 
    icon: "Settings",
    hasSubmenu: true,
    submenu: [
      { id: "settings-clinics", label: "Clinics", icon: "Building2" },
      { id: "settings-patients", label: "Patients", icon: "Users" },
      { id: "settings-doctors", label: "Doctors", icon: "Stethoscope" },
    ]
  },
];

export const TAB_TO_ROUTE = {
  dashboard: "/dashboard",
  appointments: "/appointment-management",
  templates: "/template-master",
  "ai-calls": "/ai-call-logs",
  reactivation: "/reactivation-program",
  escalation: "/escalation",
  analytics: "/analytics",
  settings: "/settings",
  "settings-clinics": "/clinic-master",
  "settings-patients": "/patient-registry",
  "settings-doctors": "/doctor-directory",
};
