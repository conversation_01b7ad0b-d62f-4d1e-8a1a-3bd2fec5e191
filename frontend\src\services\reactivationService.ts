import { apiRequest } from '@/utils/axios.utils';

// Define the API response structure
interface ApiResponse<T = unknown> {
  status: boolean;
  data: T;
  message?: string;
}

export interface ReactivationRecord {
  id: number;
  clinic_id: number;
  batch_name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  patient_count: number;
  scheduled_time_unix: number | null;
  scheduled_time: string | null;
  executed_time: string | null;
  completed_time: string | null;
  elevenlabs_batch_id: string | null;
  call_results: Record<string, unknown> | null;
  error_message: string | null;
  reactivation_days: number | null;
  created_at: string;
  updated_at: string;
  created_by: number | null;
  updated_by: number | null;
  is_deleted: boolean;
  is_active: boolean;
  Clinic?: {
    id: number;
    clinic_name: string;
  };
  Creator?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface ReactivationStats {
  status: string;
  count: number;
  total_patients: number;
}

export interface CreateReactivationData {
  clinic_id: number;
  batch_name: string;
  patient_count: number;
  scheduled_time_unix?: number;
  scheduled_time?: string;
  reactivation_days?: number;
}

export interface UpdateReactivationData {
  status?: string;
  executed_time?: string;
  completed_time?: string;
  elevenlabs_batch_id?: string;
  call_results?: Record<string, unknown>;
  error_message?: string;
}

export interface ReactivationFilters {
  clinic_id?: number;
  status?: string;
  start_date?: string;
  end_date?: string;
}

class ReactivationService {


  /**
   * Get reactivation records for a specific clinic
   */
  async getReactivationsByClinic(clinicId: number): Promise<ReactivationRecord[]> {
    try {
      const response = await apiRequest.get(`/patient/reactivations/${clinicId}`) as ApiResponse<ReactivationRecord[]>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to fetch reactivations for clinic');
      }
    } catch (error) {
      console.error('Error fetching reactivations by clinic:', error);
      throw error;
    }
  }

  /**
   * Get all reactivations with optional filters
   */
  async getReactivations(filters?: ReactivationFilters): Promise<ReactivationRecord[]> {
    try {
      const queryParams = new URLSearchParams();
      if (filters?.clinic_id) queryParams.append('clinic_id', filters.clinic_id.toString());
      if (filters?.status) queryParams.append('status', filters.status);
      if (filters?.start_date) queryParams.append('start_date', filters.start_date);
      if (filters?.end_date) queryParams.append('end_date', filters.end_date);

      const response = await apiRequest.get(`/reactivations?${queryParams.toString()}`) as ApiResponse<ReactivationRecord[]>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to fetch reactivations');
      }
    } catch (error) {
      console.error('Error fetching reactivations:', error);
      throw error;
    }
  }

  /**
   * Get reactivation statistics for a clinic
   */
  async getReactivationStats(clinicId: number, dateRange?: { start_date?: string; end_date?: string }): Promise<ReactivationStats[]> {
    try {
      const queryParams = new URLSearchParams();
      if (dateRange?.start_date) queryParams.append('start_date', dateRange.start_date);
      if (dateRange?.end_date) queryParams.append('end_date', dateRange.end_date);

      const response = await apiRequest.get(`/patient/reactivations/${clinicId}/stats?${queryParams.toString()}`) as ApiResponse<ReactivationStats[]>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to fetch reactivation statistics');
      }
    } catch (error) {
      console.error('Error fetching reactivation stats:', error);
      throw error;
    }
  }

  /**
   * Get a single reactivation record by ID
   */
  async getReactivationById(id: number): Promise<ReactivationRecord> {
    try {
      const response = await apiRequest.get(`/reactivation/${id}`) as ApiResponse<ReactivationRecord>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to fetch reactivation');
      }
    } catch (error) {
      console.error('Error fetching reactivation by ID:', error);
      throw error;
    }
  }

  /**
   * Create a new reactivation record
   */
  async createReactivation(data: CreateReactivationData): Promise<ReactivationRecord> {
    try {
      const response = await apiRequest.post('/reactivation', data) as ApiResponse<ReactivationRecord>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to create reactivation');
      }
    } catch (error) {
      console.error('Error creating reactivation:', error);
      throw error;
    }
  }

  /**
   * Update a reactivation record
   */
  async updateReactivation(id: number, data: UpdateReactivationData): Promise<ReactivationRecord> {
    try {
      const response = await apiRequest.put(`/reactivation/${id}`, data) as ApiResponse<ReactivationRecord>;
      
      if (response && response.status) {
        return response.data;
      } else {
        throw new Error(response?.message || 'Failed to update reactivation');
      }
    } catch (error) {
      console.error('Error updating reactivation:', error);
      throw error;
    }
  }

  /**
   * Update reactivation status with automatic timestamp handling
   */
  async updateReactivationStatus(id: number, status: string, additionalData: UpdateReactivationData = {}): Promise<ReactivationRecord> {
    const updateData: UpdateReactivationData = {
      status,
      ...additionalData,
    };

    // Set timestamps based on status
    if (status === 'in_progress') {
      updateData.executed_time = new Date().toISOString();
    } else if (['completed', 'failed'].includes(status)) {
      updateData.completed_time = new Date().toISOString();
    }

    return this.updateReactivation(id, updateData);
  }

  /**
   * Delete a reactivation record (soft delete)
   */
  async deleteReactivation(id: number): Promise<boolean> {
    try {
      const response = await apiRequest.delete(`/reactivation/${id}`) as ApiResponse<unknown>;
      
      if (response && response.status) {
        return true;
      } else {
        throw new Error(response?.message || 'Failed to delete reactivation');
      }
    } catch (error) {
      console.error('Error deleting reactivation:', error);
      throw error;
    }
  }

  /**
   * Get reactivation summary for dashboard
   */
  async getReactivationSummary(clinicId?: number): Promise<{
    total_campaigns: number;
    active_campaigns: number;
    completed_campaigns: number;
    failed_campaigns: number;
    total_patients_contacted: number;
    success_rate: number;
  }> {
    try {
      if (!clinicId) {
        // If no clinic ID provided, return empty summary
        return {
          total_campaigns: 0,
          active_campaigns: 0,
          completed_campaigns: 0,
          failed_campaigns: 0,
          total_patients_contacted: 0,
          success_rate: 0,
        };
      }

      const reactivations = await this.getReactivationsByClinic(clinicId);
      
      const total_campaigns = reactivations.length;
      const active_campaigns = reactivations.filter(r => r.status === 'in_progress').length;
      const completed_campaigns = reactivations.filter(r => r.status === 'completed').length;
      const failed_campaigns = reactivations.filter(r => r.status === 'failed').length;
      
      const total_patients_contacted = reactivations.reduce((sum, r) => sum + r.patient_count, 0);
      
      const successful_campaigns = reactivations.filter(r => r.status === 'completed');
      const success_rate = total_campaigns > 0 ? (successful_campaigns.length / total_campaigns) * 100 : 0;

      return {
        total_campaigns,
        active_campaigns,
        completed_campaigns,
        failed_campaigns,
        total_patients_contacted,
        success_rate: Math.round(success_rate * 100) / 100,
      };
    } catch (error) {
      console.error('Error getting reactivation summary:', error);
      throw error;
    }
  }
}

export const reactivationService = new ReactivationService();
export default reactivationService;
