
# Node environment
NODE_ENV=development

# Backend base URL for logo and public assets
BACKEND_BASE_URL=http://localhost:3031
# Frontend base URL
FRONTEND_BASE_URL=http://localhost:3030

# To Connect the Sql
PORT=serverportnumber

# JWT Secret (for authentication)
JWT_SECRET=jwt_secret_key
JWT_REFRESH_SECRET=jwt_refresh_secret_key
ACCESS_TOKEN_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=7d

# Docker configuration
DOCKER_USERNAME=dockerUserName
DOCKER_PASSWORD=dockerPassword

# AWS RDS PostgreSQL - Sequelize Config
PG_URI=postgresql://postgres:<EMAIL>:5432/yourDatabase?sslmode=require
DB_HOST=your-db-instance-name.xxxxxxxxxxxx.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=yourDatabase
DB_USER=postgres  # or your RDS master username
DB_PASS=yourPassword
DB_DIALECT=postgres

# sendgrid configuration
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=your_sendgrid_from_email

# Encryption keys for reset token
RESET_TOKEN_KEY=your_reset_token_key

# twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_tiwlio_auth_token
TWILIO_PHONE_NUMBER=you_twilio_phone_number #with country code