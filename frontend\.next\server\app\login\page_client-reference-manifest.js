globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/login/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.tsx":{"*":{"id":"(ssr)/./src/app/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/components/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(ssr)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reactivation-program/page.tsx":{"*":{"id":"(ssr)/./src/app/reactivation-program/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/appointment-management/page.tsx":{"*":{"id":"(ssr)/./src/app/appointment-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/template-master/page.tsx":{"*":{"id":"(ssr)/./src/app/template-master/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/ai-call-logs/page.tsx":{"*":{"id":"(ssr)/./src/app/ai-call-logs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/escalation/page.tsx":{"*":{"id":"(ssr)/./src/app/escalation/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\ClientLayout.tsx":{"id":"(app-pages-browser)/./src/app/ClientLayout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\components\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/components/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\components\\ui\\toaster.tsx":{"id":"(app-pages-browser)/./src/components/ui/toaster.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":["app/login/page","static/chunks/app/login/page.js"],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\reactivation-program\\page.tsx":{"id":"(app-pages-browser)/./src/app/reactivation-program/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\appointment-management\\page.tsx":{"id":"(app-pages-browser)/./src/app/appointment-management/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\template-master\\page.tsx":{"id":"(app-pages-browser)/./src/app/template-master/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\ai-call-logs\\page.tsx":{"id":"(app-pages-browser)/./src/app/ai-call-logs/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\escalation\\page.tsx":{"id":"(app-pages-browser)/./src/app/escalation/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\":[],"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\page":[{"inlined":false,"path":"static/css/app/page.css"}],"D:\\Projects\\Densy-ai\\densy-ai\\Clinic-Appointment-AI-Agent\\frontend\\src\\app\\login\\page":[{"inlined":false,"path":"static/css/app/login/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/ClientLayout.tsx":{"*":{"id":"(rsc)/./src/app/ClientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/components/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/toaster.tsx":{"*":{"id":"(rsc)/./src/components/ui/toaster.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reactivation-program/page.tsx":{"*":{"id":"(rsc)/./src/app/reactivation-program/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/appointment-management/page.tsx":{"*":{"id":"(rsc)/./src/app/appointment-management/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/template-master/page.tsx":{"*":{"id":"(rsc)/./src/app/template-master/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/ai-call-logs/page.tsx":{"*":{"id":"(rsc)/./src/app/ai-call-logs/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/escalation/page.tsx":{"*":{"id":"(rsc)/./src/app/escalation/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}