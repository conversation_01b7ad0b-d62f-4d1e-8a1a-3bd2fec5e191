import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../app/globals.css";
import ClientLayout from "./ClientLayout";
import { AuthProvider } from "../components/contexts/AuthContext";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Clinic Appointment AI Agent",
  description: "AI-powered clinic appointment management system",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <Toaster />
          <ClientLayout>{children}</ClientLayout>
        </AuthProvider>
      </body>
    </html>
  );
}
