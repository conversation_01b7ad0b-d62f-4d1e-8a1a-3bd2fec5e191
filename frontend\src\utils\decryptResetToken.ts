import CryptoJS from 'crypto-js';

/**
 * Decrypt and verify reset password token (frontend)
 * @param {string} token - Encrypted reset token (IV:encrypted)
 * @param {string} key - Decryption key (from .env)
 * @returns {object|null} - Decrypted user data or null if error
 */
export function decryptResetPasswordToken(token: string, key: string): object | null {
  try {
    const [ivHex, encrypted] = token.split(':');
    const iv = CryptoJS.enc.Hex.parse(ivHex);
    const decrypted = CryptoJS.AES.decrypt(encrypted, CryptoJS.enc.Utf8.parse(key), {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedText);
  } catch {
    return null;
  }
}