// Layout.tsx
// Provides the main application layout with sidebar, header, and main content area.
// Used as the root wrapper for all pages.

"use client";

import React from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";

/**
 * Props for the Layout component
 * @property children - The main content to render inside the layout
 * @property defaultActiveTab - Optional default active sidebar tab
 * @property onTabChange - Optional handler for tab change events
 */
interface LayoutProps {
  children: React.ReactNode;
  activeTab: string;
}

/**
 * Renders the main application layout with sidebar, header, and content area.
 * Used as the root wrapper for all pages.
 */

const Layout: React.FC<LayoutProps> = ({ children, activeTab }) => {

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar activeTab={activeTab} />
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />
        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  );
};

export default Layout;
