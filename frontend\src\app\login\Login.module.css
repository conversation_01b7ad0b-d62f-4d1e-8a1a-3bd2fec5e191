.loginContainer {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0fdf4 100%);
  padding: 0 1rem;
}

.loginMotion {
  width: 100%;
  max-width: 400px;
}

.loginCard {
  border-radius: 1.25rem;
  box-shadow: 0 4px 32px rgba(0,0,0,0.08);
  background: #fff;
  border: 1px solid var(--color-muted, #e5e7eb);
}

.loginHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: center;
}

.loginLogo {
  margin: 0 auto 0.5rem auto;
}

.loginTitle {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(90deg, #1e40af, #22c55e, #14b8a6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  letter-spacing: -0.02em;
}

.loginSubtitle {
  color: var(--color-muted-foreground, #6b7280);
  margin-top: 0.25rem;
  font-size: 1rem;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loginInput {
  padding-left: 2.5rem;
}

.loginInputPassword {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.loginIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-muted-foreground, #6b7280);
}

.loginPasswordToggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-muted-foreground, #6b7280);
  outline: none;
}

.loginError {
  font-size: 0.875rem;
  color: #ef4444;
  margin-top: 0.25rem;
  display: block;
}

.loginButton {
  width: 100%;
  color: #fff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  padding: 0.75rem 0;
  margin-top: 0.5rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}

.loginButton:focus,
.loginButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.loginButton:disabled {
  background: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
}

.loginRegister {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-muted-foreground, #6b7280);
}

.loginRegisterLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: underline;
  transition: color 0.15s;
}

.loginForgot {
  text-align: right;
  color: #2563eb;
  font-weight: 500;
  text-decoration: underline;
  font-size: 0.875rem;
  transition: color 0.15s;
}
