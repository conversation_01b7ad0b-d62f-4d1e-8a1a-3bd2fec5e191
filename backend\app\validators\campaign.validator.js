/**
 * Campaign Validators: express-validator schemas for campaign create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating a campaign
export const createCampaignSchema = [
  body('clinic_id')
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID)
    .notEmpty()
    .withMessage(constants.CLINIC_ID_INVALID),
  body('name')
    .notEmpty()
    .withMessage(constants.CAMPAIGN_NAME_REQUIRED)
    .isString()
    .withMessage(constants.CAMPAIGN_NAME_STRING),
  body('description')
    .optional()
    .isString()
    .withMessage(constants.CAMPAIGN_DESCRIPTION_STRING),
  body('type')
    .isIn(['call', 'sms', 'email'])
    .withMessage(constants.CAMPAIGN_TYPE_INVALID)
    .notEmpty()
    .withMessage(constants.CAMPAIGN_TYPE_REQUIRED),
  body('frequency_interval')
    .optional()
    .isString()
    .withMessage(constants.CAMPAIGN_FREQUENCY_INTERVAL_STRING),
  body('duration_days')
    .optional()
    .isInt({ min: 1 })
    .withMessage(constants.CAMPAIGN_DURATION_DAYS_INTEGER),
  body('filters')
    .optional()
    .isObject()
    .withMessage(constants.CAMPAIGN_FILTERS_OBJECT),
];

// Validation schema for updating a campaign
export const updateCampaignSchema = [
  body('clinic_id')
    .optional()
    .isInt()
    .withMessage(constants.CLINIC_ID_INVALID),
  body('name')
    .optional()
    .isString()
    .withMessage(constants.CAMPAIGN_NAME_STRING),
  body('description')
    .optional()
    .isString()
    .withMessage(constants.CAMPAIGN_DESCRIPTION_STRING),
  body('type')
    .optional()
    .isIn(['call', 'sms', 'email'])
    .withMessage(constants.CAMPAIGN_TYPE_INVALID),
  body('frequency_interval')
    .optional()
    .isString()
    .withMessage(constants.CAMPAIGN_FREQUENCY_INTERVAL_STRING),
  body('duration_days')
    .optional()
    .isInt({ min: 1 })
    .withMessage(constants.CAMPAIGN_DURATION_DAYS_INTEGER),
  body('filters')
    .optional()
    .isObject()
    .withMessage(constants.CAMPAIGN_FILTERS_OBJECT),
]; 