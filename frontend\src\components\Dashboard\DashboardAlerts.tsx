// DashboardAlerts.tsx
// Renders a card displaying system alerts for the dashboard, such as warnings or notifications.
// Each alert includes an icon, title, message, and color.

import React from "react";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { AlertTriangle, Calendar } from "lucide-react";
import { DASHBOARD_SYSTEM_ALERTS_TITLE } from "@/Constants/Dashboard";

/**
 * Represents a single alert item for the dashboard alerts card.
 * @property icon - The icon name to display (e.g., 'AlertTriangle', 'Calendar')
 * @property title - The alert title
 * @property message - The alert message
 * @property color - The color theme for the alert (e.g., 'yellow', 'blue')
 */
interface AlertItem {
  icon: string;
  title: string;
  message: string;
  color: string;
}

/**
 * Props for the DashboardAlerts component
 * @property alerts - Array of alert items to display
 */
interface DashboardAlertsProps {
  alerts: AlertItem[];
}

// Maps icon names to React nodes
const ICONS: Record<string, React.ReactNode> = {
  AlertTriangle: <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3" />,
  Calendar: <Calendar className="h-5 w-5 text-blue-600 mr-3" />,
};

// Maps color names to background/border classes
const colorMap: Record<string, string> = {
  yellow: "bg-yellow-50 border border-yellow-200",
  blue: "bg-blue-50 border border-blue-200",
};

/**
 * Renders a card with a list of system alerts for the dashboard.
 */
const DashboardAlerts: React.FC<DashboardAlertsProps> = ({ alerts }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center text-orange-600">
        <AlertTriangle className="h-5 w-5 mr-2" />
        {DASHBOARD_SYSTEM_ALERTS_TITLE}
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        {alerts.map((alert, idx) => (
          <div
            key={idx}
            className={`flex items-center p-3 rounded-lg ${
              colorMap[alert.color] || ""
            }`}
          >
            {ICONS[alert.icon]}
            <div>
              <p
                className={`font-medium ${
                  alert.color === "yellow" ? "text-yellow-800" : "text-blue-800"
                }`}
              >
                {alert.title}
              </p>
              <p
                className={`text-sm ${
                  alert.color === "yellow" ? "text-yellow-600" : "text-blue-600"
                }`}
              >
                {alert.message}
              </p>
            </div>
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);

export default DashboardAlerts;
