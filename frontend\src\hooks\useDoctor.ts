import { apiRequest } from "../utils/axios.utils";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { ERROR_MESSAGES, DOCTOR_MESSAGES } from "../Constants/HooksAPI";
import { useCallback } from "react";



// Doctor API endpoint (relative, will use axiosInstance baseURL)
const DOCTOR_CREATE_ENDPOINT = "/doctor/create";

export function useDoctor() {
  // Create Doctor
  const createDoctor = async (doctorData: Record<string, unknown>) => {
    try {
      const response = (await apiRequest.post(
        DOCTOR_CREATE_ENDPOINT,
        doctorData
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || DOCTOR_MESSAGES.CREATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || DOCTOR_MESSAGES.CREATE_FAILED);
        return {
          success: false,
          error: response?.message || DOCTOR_MESSAGES.CREATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, DOCTOR_MESSAGES.CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return {
        success: false,
        error: errorMsg,
      };
    }
  };

  // Fetch all doctors
  const getDoctors = useCallback(async () => {
    try {
      const response = (await apiRequest.get("/doctor/list")) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.API_REQUEST_FAILED);
        return {
          success: false,
          error: response?.message || ERROR_MESSAGES.API_REQUEST_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.API_REQUEST_FAILED);
      compactErrorMessage(errorMsg);
      return {
        success: false,
        error: errorMsg,
      };
    }
  }, []);

  // Update Doctor
  const updateDoctor = async (
    doctorId: number | string,
    doctorData: Record<string, unknown>
  ) => {
    try {
      const response = (await apiRequest.put(
        `/doctor/${doctorId}`,
        doctorData
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || DOCTOR_MESSAGES.UPDATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || DOCTOR_MESSAGES.UPDATE_FAILED);
        return {
          success: false,
          error: response?.message || DOCTOR_MESSAGES.UPDATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, DOCTOR_MESSAGES.UPDATE_FAILED);
      compactErrorMessage(errorMsg);
      return {
        success: false,
        error: errorMsg,
      };
    }
  };

  // Delete Doctor
  const deleteDoctor = async (doctorId: number | string) => {
    try {
      const response = (await apiRequest.delete(`/doctor/${doctorId}`)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || DOCTOR_MESSAGES.DELETE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || DOCTOR_MESSAGES.DELETE_FAILED);
        return {
          success: false,
          error: response?.message || DOCTOR_MESSAGES.DELETE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, DOCTOR_MESSAGES.DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return {
        success: false,
        error: errorMsg,
      };
    }
  };

  return {
    createDoctor,
    getDoctors,
    updateDoctor,
    deleteDoctor,
  };
}
