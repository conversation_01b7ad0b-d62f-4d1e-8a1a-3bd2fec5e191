import React from "react";
import {
  Sheet,
  SheetContent,
  Sheet<PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { formatPhoneNumber } from "@/utils/commonFunctions";
import {
  getAppointmentStatusColor,
  getAppointmentTypeColor,
  capitalizeFirstLetter,
  UNKNOWN_PATIENT,
  UNASSIGNED_DOCTOR,
  NA_VALUE,
  LABEL_APPOINTMENT_DETAILS,
  LABEL_APPOINTMENT_DETAILS_DESC,
  LABEL_PATIENT,
  LABEL_DOCTOR,
  LABEL_DATE,
  LABEL_TIME,
  LABEL_STATUS,
  LABEL_BOOKED_VIA,
  LABEL_PATIENT_PHONE,
  LABEL_PATIENT_EMAIL,
  LABEL_CLINIC,
} from "@/Constants/Appointment";
import { Appointment } from "@/hooks/useAppointment";

// Props for the AppointmentDetailsSheet component
interface AppointmentDetailsSheetProps {
  appointment: Appointment | null; // Appointment to show details for
  onClose: () => void; // Handler to close the sheet
}

/**
 * Renders a side sheet with detailed information about an appointment.
 * Shows patient, doctor, date, time, status, contact info, and clinic.
 */
const AppointmentDetailsSheet: React.FC<AppointmentDetailsSheetProps> = ({
  appointment,
  onClose,
}) => {
  if (!appointment) return null;

  // Helper function to format time with AM/PM
  const formatTimeWithAMPM = (timeString: string) => {
    if (!timeString) return NA_VALUE;
    
    try {
      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        // If it's not a valid date, try to parse it as a time string
        const timeMatch = timeString.match(/(\d{1,2}):(\d{2})/);
        if (timeMatch) {
          const hour = parseInt(timeMatch[1], 10);
          const minute = timeMatch[2];
          const ampm = hour >= 12 ? 'PM' : 'AM';
          const displayHour = hour % 12 || 12;
          return `${displayHour}:${minute} ${ampm}`;
        }
        return timeString;
      }
      
      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      });
    } catch (error) {
      console.error("Error formatting time:", error);
      return timeString;
    }
  };

  return (
    <Sheet
      open={!!appointment}
      onOpenChange={(isOpen) => {
        if (!isOpen) onClose();
      }}
    >
      <SheetContent className="w-[400px] sm:w-[540px] overflow-y-auto bg-white">
        <SheetHeader>
          <SheetTitle>{LABEL_APPOINTMENT_DETAILS}</SheetTitle>
          <SheetDescription>{LABEL_APPOINTMENT_DETAILS_DESC}</SheetDescription>
        </SheetHeader>
        <div className="py-4 space-y-4">
          {/* Appointment details grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <div className="font-semibold text-gray-600 text-sm">{LABEL_PATIENT}</div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.patient ? `${appointment.patient.first_name} ${appointment.patient.last_name}` : UNKNOWN_PATIENT}
            </div>
            <div className="font-semibold text-gray-600 text-sm">{LABEL_DOCTOR}</div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.doctor?.doctor_name || UNASSIGNED_DOCTOR}
            </div>
            <div className="font-semibold text-gray-600 text-sm">{LABEL_DATE}</div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.appointment_date
                ? new Date(appointment.appointment_date).toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })
                : NA_VALUE}
            </div>
            <div className="font-semibold text-gray-600 text-sm">{LABEL_TIME}</div>
            <div className="text-gray-900 text-sm break-words">
              {formatTimeWithAMPM(appointment.appointment_time)}
            </div>
            <div className="font-semibold text-gray-600 text-sm">{LABEL_STATUS}</div>
            <div>
              <Badge
                variant="outline"
                className={`text-xs ${getAppointmentStatusColor(appointment.status || "")}`}
              >
                {capitalizeFirstLetter(appointment.status || NA_VALUE)}
              </Badge>
            </div>
            <div className="font-semibold text-gray-600 text-sm">
              {LABEL_BOOKED_VIA}
            </div>
            <div>
              <Badge
                variant="outline"
                className={`text-xs ${getAppointmentTypeColor(
                  appointment.source || ""
                )}`}
              >
                {capitalizeFirstLetter(appointment.source || NA_VALUE)}
              </Badge>
            </div>
            <div className="font-semibold text-gray-600 text-sm">
              {LABEL_PATIENT_PHONE}
            </div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.patient?.phone_number ? formatPhoneNumber(appointment.patient.phone_number) : NA_VALUE}
            </div>
            <div className="font-semibold text-gray-600 text-sm">
              {LABEL_PATIENT_EMAIL}
            </div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.patient?.email || NA_VALUE}
            </div>
            <div className="font-semibold text-gray-600 text-sm">{LABEL_CLINIC}</div>
            <div className="text-gray-900 text-sm break-words">
              {appointment.clinic?.clinic_name || NA_VALUE}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default AppointmentDetailsSheet;
