import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Users, 
  Calendar, 
  Phone, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Play,
  Pause,
  Trash2,
  Eye
} from 'lucide-react';
import { useReactivationManagement, ReactivationRecord } from '@/hooks/useReactivationManagement';
import { formatDistanceToNow, format } from 'date-fns';
import { REACTIVATION_CAMPAIGNS_LIST } from '@/Constants/ReactivationProgram';

interface ReactivationCampaignsListProps {
  clinicId: number;
  onViewDetails?: (reactivation: ReactivationRecord) => void;
  onStatusUpdate?: (id: number, status: string) => void;
  onDelete?: (id: number) => void;
}

const statusConfig = {
  pending: {
    label: REACTIVATION_CAMPAIGNS_LIST.PENDING_LABEL,
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock,
  },
  in_progress: {
    label: REACTIVATION_CAMPAIGNS_LIST.IN_PROGRESS_LABEL,
    color: 'bg-blue-100 text-blue-800',
    icon: Play,
  },
  completed: {
    label: REACTIVATION_CAMPAIGNS_LIST.COMPLETED_LABEL,
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle,
  },
  failed: {
    label: REACTIVATION_CAMPAIGNS_LIST.FAILED_LABEL,
    color: 'bg-red-100 text-red-800',
    icon: XCircle,
  },
  cancelled: {
    label: REACTIVATION_CAMPAIGNS_LIST.CANCELLED_LABEL,
    color: 'bg-gray-100 text-gray-800',
    icon: Pause,
  },
};

const ReactivationCampaignsList: React.FC<ReactivationCampaignsListProps> = ({
  clinicId,
  onViewDetails,
  onStatusUpdate,
  onDelete,
}) => {
  const {
    reactivations,
    loading,
    error,
    getReactivationsByClinic,
    updateReactivationStatus,
    deleteReactivation,
  } = useReactivationManagement();

  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  useEffect(() => {
    if (clinicId) {
      getReactivationsByClinic(clinicId);
    }
  }, [clinicId, getReactivationsByClinic]);

  const handleStatusUpdate = async (id: number, newStatus: string) => {
    const result = await updateReactivationStatus(id, newStatus);
    if (result.success && onStatusUpdate) {
      onStatusUpdate(id, newStatus);
    }
  };

  const handleDelete = async (id: number) => {
    const result = await deleteReactivation(id);
    if (result.success && onDelete) {
      onDelete(id);
    }
  };

  const filteredReactivations = selectedStatus === 'all' 
    ? reactivations 
    : reactivations.filter(r => r.status === selectedStatus);

  const getStatusIcon = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    return config ? React.createElement(config.icon, { className: 'h-4 w-4' }) : null;
  };

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <Badge className={config.color}>
        {getStatusIcon(status)}
        <span className="ml-1">{config.label}</span>
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">{REACTIVATION_CAMPAIGNS_LIST.LOADING_MESSAGE}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center text-red-600">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>{REACTIVATION_CAMPAIGNS_LIST.ERROR_PREFIX} {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (reactivations.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>{REACTIVATION_CAMPAIGNS_LIST.NO_CAMPAIGNS_TITLE}</p>
            <p className="text-sm">{REACTIVATION_CAMPAIGNS_LIST.NO_CAMPAIGNS_SUBTITLE}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Status Filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedStatus === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedStatus('all')}
        >
          {REACTIVATION_CAMPAIGNS_LIST.ALL_FILTER} ({reactivations.length})
        </Button>
        {Object.keys(statusConfig).map((status) => {
          const count = reactivations.filter(r => r.status === status).length;
          return (
            <Button
              key={status}
              variant={selectedStatus === status ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedStatus(status)}
            >
              {getStatusIcon(status)}
              <span className="ml-1">{statusConfig[status as keyof typeof statusConfig].label}</span>
              <span className="ml-1">({count})</span>
            </Button>
          );
        })}
      </div>

      {/* Campaigns List */}
      <div className="grid gap-4">
        {filteredReactivations.map((reactivation) => (
          <Card key={reactivation.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    {reactivation.batch_name}
                    {getStatusBadge(reactivation.status)}
                  </CardTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    {REACTIVATION_CAMPAIGNS_LIST.CREATED_PREFIX} {formatDistanceToNow(new Date(reactivation.created_at), { addSuffix: true })}
                  </p>
                </div>
                <div className="flex gap-2">
                  {onViewDetails && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewDetails(reactivation)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      {REACTIVATION_CAMPAIGNS_LIST.VIEW_BUTTON}
                    </Button>
                  )}
                  {reactivation.status === 'pending' && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleStatusUpdate(reactivation.id, 'in_progress')}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      {REACTIVATION_CAMPAIGNS_LIST.START_BUTTON}
                    </Button>
                  )}
                  {reactivation.status === 'in_progress' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusUpdate(reactivation.id, 'completed')}
                    >
                      <CheckCircle className="h-4 w-4 mr-1" />
                      {REACTIVATION_CAMPAIGNS_LIST.COMPLETE_BUTTON}
                    </Button>
                  )}
                  {['pending', 'in_progress'].includes(reactivation.status) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusUpdate(reactivation.id, 'cancelled')}
                    >
                      <Pause className="h-4 w-4 mr-1" />
                      {REACTIVATION_CAMPAIGNS_LIST.CANCEL_BUTTON}
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(reactivation.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">{reactivation.patient_count}</p>
                    <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.PATIENTS_LABEL}</p>
                  </div>
                </div>
                
                {reactivation.scheduled_time && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">
                        {format(new Date(reactivation.scheduled_time), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.SCHEDULED_LABEL}</p>
                    </div>
                  </div>
                )}

                {reactivation.executed_time && (
                  <div className="flex items-center gap-2">
                    <Play className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">
                        {format(new Date(reactivation.executed_time), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.STARTED_LABEL}</p>
                    </div>
                  </div>
                )}

                {reactivation.completed_time && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">
                        {format(new Date(reactivation.completed_time), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.COMPLETED_LABEL}</p>
                    </div>
                  </div>
                )}

                {reactivation.elevenlabs_batch_id && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium truncate">
                        {reactivation.elevenlabs_batch_id.slice(0, 8)}...
                      </p>
                      <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.BATCH_ID_LABEL}</p>
                    </div>
                  </div>
                )}

                {reactivation.reactivation_days && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium">{reactivation.reactivation_days} {REACTIVATION_CAMPAIGNS_LIST.DAYS_LABEL}</p>
                      <p className="text-xs text-gray-500">{REACTIVATION_CAMPAIGNS_LIST.INACTIVE_PERIOD_LABEL}</p>
                    </div>
                  </div>
                )}
              </div>

              {reactivation.error_message && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
                  <p className="text-sm text-red-700">
                    <AlertCircle className="h-4 w-4 inline mr-1" />
                    {REACTIVATION_CAMPAIGNS_LIST.ERROR_PREFIX_LABEL} {reactivation.error_message}
                  </p>
                </div>
              )}

              {reactivation.call_results && (
                <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm text-blue-700">
                    <CheckCircle className="h-4 w-4 inline mr-1" />
                    {REACTIVATION_CAMPAIGNS_LIST.RESULTS_PREFIX_LABEL} {JSON.stringify(reactivation.call_results)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ReactivationCampaignsList;
