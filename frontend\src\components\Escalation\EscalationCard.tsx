import React, { useState, use<PERSON><PERSON>o } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Clock, User, Phone, MessageSquare } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { EscalationData } from "@/hooks/useEscalation";
import {
  ESCALATION_VIEW_TRANSCRIPT,
} from "@/Constants/Escalation";

interface EscalationCardProps {
  escalation: EscalationData;
}

interface TranscriptMessage {
  role: string;
  message: string;
  time_in_call_secs: number;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "open":
      return "bg-red-100 text-red-800 border-red-200";
    case "in_progress":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "resolved":
      return "bg-green-100 text-green-800 border-green-200";
    case "closed":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const getTagColor = (tag: string) => {
  switch (tag) {
    case "busy":
      return "bg-orange-100 text-orange-800 border-orange-200";
    case "technical":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "complaint":
      return "bg-red-100 text-red-800 border-red-200";
    case "urgent":
      return "bg-purple-100 text-purple-800 border-purple-200";
    case "follow_up":
      return "bg-green-100 text-green-800 border-green-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const EscalationCard: React.FC<EscalationCardProps> = ({ escalation }) => {
  const [, setShowTranscript] = useState(false);

  // Parse and filter transcript
  const parsedTranscript: TranscriptMessage[] = useMemo(() => {
    if (!escalation.transcript) return [];

    if (Array.isArray(escalation.transcript)) {
      return escalation.transcript
        .map((item: unknown): TranscriptMessage => {
          if (typeof item === "string") {
            try {
              const parsed = JSON.parse(item) as Record<string, unknown>;
              return {
                role: (parsed.role as string) || "unknown",
                message: (parsed.message as string) || "",
                time_in_call_secs: (parsed.time_in_call_secs as number) || 0,
              };
            } catch {
              return {
                role: "unknown",
                message: item as string,
                time_in_call_secs: 0,
              };
            }
          }
          return item as TranscriptMessage;
        })
        .filter((message: TranscriptMessage) => 
          message.message && message.message.trim().length > 0
        );
    }

    return escalation.transcript as TranscriptMessage[];
  }, [escalation.transcript]);

  return (
    <Card className="w-full hover:shadow-md transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-2">
              {escalation.summary}
            </CardTitle>
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <Clock className="h-4 w-4" />
              <span>
                {formatDistanceToNow(new Date(escalation.created_at), {
                  addSuffix: true,
                })}
              </span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge className={`${getStatusColor(escalation.status)} border`}>
              {escalation.status.replace("_", " ").toUpperCase()}
            </Badge>
            <Badge className={`${getTagColor(escalation.tags)} border`}>
              {escalation.tags.toUpperCase()}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Patient Information */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <User className="h-5 w-5 text-gray-600" />
            <div className="flex-1">
              <p className="font-medium text-gray-900">
                {escalation.patient_name}
              </p>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Phone className="h-4 w-4" />
                <span>{escalation.patient_phone}</span>
              </div>
            </div>
          </div>

          {/* Assignee Information */}
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <User className="h-5 w-5 text-blue-600" />
            <div className="flex-1">
              <p className="font-medium text-gray-900">Assigned to</p>
              <p className="text-sm text-gray-600">
                {escalation.assignee_name}
              </p>
            </div>
          </div>

          {/* Transcript Button */}
          {parsedTranscript.length > 0 && (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full flex items-center gap-2"
                  onClick={() => setShowTranscript(true)}
                >
                  <MessageSquare className="h-4 w-4" />
                  {ESCALATION_VIEW_TRANSCRIPT}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Conversation Transcript
                  </DialogTitle>
                </DialogHeader>
                <ScrollArea className="h-[60vh] pr-4">
                  <div className="space-y-4">
                    {parsedTranscript.map((message, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge
                            variant={
                              message.role === "agent" ? "default" : "secondary"
                            }
                            className={
                              message.role === "agent"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                            }
                          >
                            {message.role === "agent" ? "AI Agent" : "Patient"}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {formatTime(message.time_in_call_secs)}
                          </span>
                        </div>
                        <div
                          className={`p-3 rounded-lg ${
                            message.role === "agent"
                              ? "bg-blue-50 border border-blue-200"
                              : "bg-gray-50 border border-gray-200"
                          }`}
                        >
                          <p className="text-sm text-gray-900 whitespace-pre-wrap">
                            {message.message}
                          </p>
                        </div>
                        {index < parsedTranscript.length - 1 && <Separator />}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EscalationCard;
