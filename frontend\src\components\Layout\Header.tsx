// Header.tsx
// Renders the main header for the application, including clinic name, search, and user actions.
// Appears at the top of every page.

import React from "react";
import HeaderBranding from "../Header/HeaderBranding";
import HeaderActions from "../Header/HeaderActions";

/**
 * Renders the main header with clinic name, search bar, and user action buttons.
 * Used at the top of every page.
 */

const Header = () => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <HeaderBranding />
        <HeaderActions />
      </div>
    </header>
  );
};

export default Header;
