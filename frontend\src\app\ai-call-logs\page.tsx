
'use client';

import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  User, 
  XCircle,
} from 'lucide-react';
import AICallStatsGrid from '@/components/AICallLogs/AICallStatsGrid';
import AI<PERSON><PERSON>LogsList from '@/components/AICallLogs/AICallLogsList';
import {
  AI_CALL_LOGS_TITLE,
  AI_CALL_LOGS_FILTER_TODAY,
  AI_CALL_LOGS_FILTER_WEEK,
  AI_CALL_LOGS_FILTER_MONTH,
  AI_CALL_LOGS_FILTER_ALL_TYPES,
  AI_CALL_LOGS_FILTER_INCOMING,
  AI_CALL_LOGS_FILTER_OUTGOING,
} from '@/Constants/AICallLogs';
import PageSection from '@/components/CommonComponents/PageSection';
import { useAICallLog } from '@/hooks/useAICallLog';
import { transformAICallLogsToComponentFormat } from '@/utils/aiCallLogUtils';

const AICallLogs = () => {
  const [selectedCall, setSelectedCall] = useState<number | null>(null);
  const { aiCallLogs, loading, getAllAICallLogs } = useAICallLog();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return User;
      case 'Missed Connection': return XCircle;
      case 'In Progress': return User;
      default: return User;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Missed Connection': return 'bg-red-100 text-red-800';
      case 'In Progress': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'neutral': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  // Fetch AI call logs on component mount
  useEffect(() => {
    getAllAICallLogs();
  }, [getAllAICallLogs]);

  // Transform API data to component format
  const transformedCallLogs = transformAICallLogsToComponentFormat(aiCallLogs);

  // Calculate stats from the data
  const totalCalls = aiCallLogs.length;
  const successfulCalls = aiCallLogs.filter(log => log.call_status === 'successful').length;
  const successRate = totalCalls > 0 ? Math.round((successfulCalls / totalCalls) * 100) : 0;
  const avgDuration = aiCallLogs.length > 0 
    ? Math.round(aiCallLogs.reduce((sum, log) => sum + log.call_duration, 0) / aiCallLogs.length)
    : 0;
  const avgDurationFormatted = `${Math.floor(avgDuration / 60)}:${(avgDuration % 60).toString().padStart(2, '0')}`;
  const appointmentsCount = aiCallLogs.filter(log => 
    log.conversation_summary?.toLowerCase().includes('appointment') ||
    log.call_summary_title?.toLowerCase().includes('appointment')
  ).length;

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold text-gray-900">{AI_CALL_LOGS_TITLE}</h2>
        <div className="flex space-x-3">
          <Select defaultValue="today">
            <SelectTrigger className="w-40 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-shadow" >
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">{AI_CALL_LOGS_FILTER_TODAY}</SelectItem>
              <SelectItem value="week">{AI_CALL_LOGS_FILTER_WEEK}</SelectItem>
              <SelectItem value="month">{AI_CALL_LOGS_FILTER_MONTH}</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-44 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-shadow" >
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{AI_CALL_LOGS_FILTER_ALL_TYPES}</SelectItem>
              <SelectItem value="incoming">{AI_CALL_LOGS_FILTER_INCOMING}</SelectItem>
              <SelectItem value="outgoing">{AI_CALL_LOGS_FILTER_OUTGOING}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Call Statistics */}
      <AICallStatsGrid
        totalCalls={totalCalls}
        successRate={`${successRate}%`}
        avgDuration={avgDurationFormatted}
        appointments={appointmentsCount}
      />

      {/* Call Logs List */}
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <AICallLogsList
          callLogs={transformedCallLogs}
          selectedCall={selectedCall}
          setSelectedCall={setSelectedCall}
          getStatusIcon={getStatusIcon}
          getStatusColor={getStatusColor}
          getSentimentColor={getSentimentColor}
        />
      )}
    </PageSection>
  );
};

export default AICallLogs;
