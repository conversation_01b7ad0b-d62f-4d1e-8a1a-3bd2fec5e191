/**
 * Patient Validators: express-validator schemas for patient create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating a patient
export const createPatientSchema = [
  body('clinic_id').isInt().withMessage(constants.CLINIC_ID_INVALID).notEmpty().withMessage(constants.CLINIC_ID_INVALID),
  body('first_name').notEmpty().withMessage(constants.PATIENT_FIRST_NAME_REQUIRED),
  body('last_name').notEmpty().withMessage(constants.PATIENT_LAST_NAME_REQUIRED),
  body('email').optional().isEmail().withMessage(constants.INVALID_EMAIL),
  body('phone_number').optional().isString().withMessage(constants.INVALID_PHONE_NUMBER),
  body('last_visit').optional().isISO8601().withMessage(constants.LOG_DATE_FORMAT),
  body('tags').optional().isArray().withMessage(constants.PATIENT_TAGS_ARRAY),
  body('doctors').optional().isArray().withMessage(constants.PATIENT_DOCTORS_ARRAY),
  body('preferences').optional().isObject().withMessage(constants.PATIENT_PREFERENCES_OBJECT),
  body('dob').optional().isISO8601().withMessage(constants.DATE_FORMAT),
  body('gender').optional().isString().withMessage(constants.PATIENT_GENDER_STRING),
  body('address').optional().isString().withMessage(constants.PATIENT_ADDRESS_STRING),
];

// Validation schema for updating a patient
export const updatePatientSchema = [
  body('clinic_id').optional().isInt().withMessage(constants.CLINIC_ID_INVALID),
  body('first_name').optional().isString().withMessage(constants.PATIENT_FIRST_NAME_REQUIRED),
  body('last_name').optional().isString().withMessage(constants.PATIENT_LAST_NAME_REQUIRED),
  body('email').optional().isEmail().withMessage(constants.INVALID_EMAIL),
  body('phone_number').optional().isString().withMessage(constants.INVALID_PHONE_NUMBER),
  body('last_visit').optional().isISO8601().withMessage(constants.LOG_DATE_FORMAT),
  body('tags').optional().isArray().withMessage(constants.PATIENT_TAGS_ARRAY),
  body('doctors').optional().isArray().withMessage(constants.PATIENT_DOCTORS_ARRAY),
  body('preferences').optional().isObject().withMessage(constants.PATIENT_PREFERENCES_OBJECT),
  body('dob').optional().isISO8601().withMessage(constants.DATE_FORMAT),
  body('gender').optional().isString().withMessage(constants.PATIENT_GENDER_STRING),
  body('address').optional().isString().withMessage(constants.PATIENT_ADDRESS_STRING),
];

// Validation schema for batch call submission
export const batchCallSchema = [
  body('clinic_id').isInt().withMessage(constants.CLINIC_ID_INVALID).notEmpty().withMessage(constants.CLINIC_ID_INVALID),
  body('patient_ids').isArray({ min: 1 }).withMessage('Patient IDs array is required and must contain at least one ID'),
  body('patient_ids.*').isInt().withMessage('Each patient ID must be a valid integer'),
  body('time').optional().isString().withMessage('Time must be a valid string'),
];
