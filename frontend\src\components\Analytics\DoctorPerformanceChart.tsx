import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  ANALYTICS_DOCTOR_PERFORMANCE,
  ANALYTICS_CHART_DOCTOR_PERFORMANCE,
  ANALYTICS_CHART_LABEL_APPOINTMENTS,
  ANALYTICS_CHART_LABEL_PATIENTS,
} from "@/Constants/Analytics";

// Props for the DoctorPerformanceChart component
interface DoctorPerformanceChartProps {
  data?: typeof ANALYTICS_DOCTOR_PERFORMANCE; // Optional custom data, defaults to ANALYTICS_DOCTOR_PERFORMANCE
}

/**
 * Renders a list of doctor performance cards.
 * Each card shows doctor name, satisfaction, appointments, and patients.
 */
const DoctorPerformanceChart: React.FC<DoctorPerformanceChartProps> = ({
  data = ANALYTICS_DOCTOR_PERFORMANCE,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{ANALYTICS_CHART_DOCTOR_PERFORMANCE}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Render each doctor's performance card */}
          {data.map((doctor, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{doctor.name}</h4>
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-1">★</span>
                  <span className="text-sm font-medium">
                    {doctor.satisfaction}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">
                    {ANALYTICS_CHART_LABEL_APPOINTMENTS}{" "}
                  </span>
                  <span className="font-medium">{doctor.appointments}</span>
                </div>
                <div>
                  <span className="text-gray-600">
                    {ANALYTICS_CHART_LABEL_PATIENTS}{" "}
                  </span>
                  <span className="font-medium">{doctor.patients}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default DoctorPerformanceChart;
