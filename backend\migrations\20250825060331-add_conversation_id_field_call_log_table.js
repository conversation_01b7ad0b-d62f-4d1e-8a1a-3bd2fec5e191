export const up = async (queryInterface, Sequelize) => {
  await queryInterface.addColumn('ai_call_logs', 'conversation_id', {
    type: Sequelize.STRING,
    allowNull: true,
    comment:
      'Conversation ID used to map logs with transcripts and other call data',
  });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('ai_call_logs', 'conversation_id');
};
