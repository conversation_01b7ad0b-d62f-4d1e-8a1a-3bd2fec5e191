"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toastSuccess, toastError } from "@/hooks/use-toast";
import { useUserProfile } from "@/hooks/useUserProfile";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
// import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Loader2, 
  User, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ArrowLeft,
  Settings,
  <PERSON>r<PERSON>heck,
  Edit,
  X
} from "lucide-react";
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import phoneStyles from '@/components/CommonComponents/PhoneInput.module.css';
import { formatPhoneNumber } from '@/utils/commonFunctions';
import {
  PROFILE_CONSTANTS,
  TIMEZONES,
  ROLES,
  ROLE_MAPPING,
  ROLE_BADGE_VARIANTS,
  FORM_FIELDS,
  PREVIEW_LABELS,
  ROUTES,
  ERROR_MESSAGES,
  ICONS,
  CSS_CLASSES,
} from "@/Constants/Profile";
import styles from "./Profile.module.css";
import { normalizePhoneForAPI } from "@/utils/phoneUtils";

function UserProfile() {
  const router = useRouter();
  const { profile, loading: profileLoading, error, updating, refetch, updateProfile } = useUserProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [form, setForm] = useState({
    name: "",
    email: "",
    role_id: PROFILE_CONSTANTS.DEFAULT_ROLE_ID,
    user_phonenumber: "",
    timezone: PROFILE_CONSTANTS.DEFAULT_TIMEZONE,
  });
  const [originalForm, setOriginalForm] = useState({
    name: "",
    email: "",
    role_id: PROFILE_CONSTANTS.DEFAULT_ROLE_ID,
    user_phonenumber: "",
    timezone: PROFILE_CONSTANTS.DEFAULT_TIMEZONE,
  });

  useEffect(() => {
    if (profile) {
      const profileData = {
        name: profile.name || "",
        email: profile.email || "",
        role_id: profile.role_id || PROFILE_CONSTANTS.DEFAULT_ROLE_ID,
        user_phonenumber: formatPhoneNumber(String(profile.user_phonenumber || "")),
        timezone: PROFILE_CONSTANTS.DEFAULT_TIMEZONE,
      };
      setForm(profileData);
      setOriginalForm(profileData);
    }
  }, [profile]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handlePhoneChange = (phone: string) => {
    setForm({ ...form, user_phonenumber: phone });
  };

  const handleRoleChange = (value: string) => {
    setForm({ ...form, role_id: parseInt(value) });
  };

  const handleTimezoneChange = (value: string) => {
    setForm({ ...form, timezone: value });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setForm(originalForm);
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const getRoleString = (roleId: number) => {
        switch (roleId) {
          case 1: return ROLE_MAPPING.SUPER_ADMIN;
          case 2: return ROLE_MAPPING.CLINIC_ADMIN;
          case 3: return ROLE_MAPPING.FRONT_DESK;
          case 4: return ROLE_MAPPING.AGENT;
          default: return ROLE_MAPPING.AGENT;
        }
      };

      const payload = {
        name: form.name,
        email: form.email,
        role: getRoleString(form.role_id),
        clinic_id: profile?.clinic_id || null,
        user_phonenumber: normalizePhoneForAPI(form.user_phonenumber),
        timezone: form.timezone
      };

      const result = await updateProfile(payload);

      if (result.success) {
        toastSuccess(result.message);
        setIsEditing(false);
        setOriginalForm(form);
      } else {
        toastError(result.message);
      }
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : ERROR_MESSAGES.UPDATE_FAILED;
      toastError(errorMsg);
    }
  };

  const handleBackToDashboard = () => {
    router.push(ROUTES.DASHBOARD);
  };

  const getRoleLabel = (roleId: number) => {
    return ROLES.find((r) => r.value === roleId)?.label || "User";
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const getRoleBadgeVariant = (roleId: number): "default" | "outline" | "destructive" | "secondary" => {
    switch (roleId) {
      case 1: return ROLE_BADGE_VARIANTS.SUPER_ADMIN;
      case 2: return ROLE_BADGE_VARIANTS.CLINIC_ADMIN;
      case 3: return ROLE_BADGE_VARIANTS.FRONT_DESK;
      case 4: return ROLE_BADGE_VARIANTS.AGENT;
      default: return ROLE_BADGE_VARIANTS.AGENT;
    }
  };

  if (profileLoading) {
    return (
      <div className={styles[CSS_CLASSES.LOADING_CONTAINER]}>
        <div className={styles[CSS_CLASSES.LOADING_CONTENT]}>
          <Loader2 className={styles[CSS_CLASSES.LOADING_SPINNER]} />
          <p className={styles[CSS_CLASSES.LOADING_TEXT]}>{PROFILE_CONSTANTS.LOADING_MESSAGE}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles[CSS_CLASSES.ERROR_CONTAINER]}>
        <div className={styles[CSS_CLASSES.ERROR_CONTENT]}>
          <AlertCircle className={styles[CSS_CLASSES.ERROR_ICON]} />
          <p className={styles[CSS_CLASSES.ERROR_TEXT]}>{error}</p>
          <Button onClick={refetch} className={styles[CSS_CLASSES.ERROR_BUTTON]}>
            {PROFILE_CONSTANTS.RETRY}
          </Button>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className={styles[CSS_CLASSES.NO_DATA_CONTAINER]}>
        <div className={styles[CSS_CLASSES.NO_DATA_CONTENT]}>
          <User className={styles[CSS_CLASSES.NO_DATA_ICON]} />
          <p className={styles[CSS_CLASSES.NO_DATA_TEXT]}>{PROFILE_CONSTANTS.NO_DATA_MESSAGE}</p>
          <Button onClick={refetch} className={styles[CSS_CLASSES.REFRESH_BUTTON]}>
            {PROFILE_CONSTANTS.REFRESH}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles[CSS_CLASSES.PROFILE_CONTAINER]}>
      <div className={styles[CSS_CLASSES.PROFILE_WRAPPER]}>
        <div className={styles[CSS_CLASSES.PROFILE_GRID]}>
          {/* Left Column - Form (spans 2 columns on medium screens) */}
          <div className={styles[CSS_CLASSES.FORM_COLUMN]}>
            <Card className={styles[CSS_CLASSES.PROFILE_CARD]}>
              <CardHeader className={styles[CSS_CLASSES.CARD_HEADER]}>
                <div className={styles[CSS_CLASSES.CARD_HEADER_CONTENT]}>
                  <div className={styles[CSS_CLASSES.CARD_HEADER_LEFT]}>
                    <div className={styles[CSS_CLASSES.CARD_HEADER_ICON]}>
                      <Settings className={ICONS.SETTINGS} />
                    </div>
                    <div className={styles[CSS_CLASSES.CARD_HEADER_TEXT]}>
                      <CardTitle className={styles[CSS_CLASSES.CARD_TITLE]}>
                        {PROFILE_CONSTANTS.PAGE_TITLE}
                      </CardTitle>
                      <p className={styles[CSS_CLASSES.CARD_SUBTITLE]}>
                        {isEditing ? PROFILE_CONSTANTS.EDIT_SUBTITLE : PROFILE_CONSTANTS.VIEW_SUBTITLE}
                      </p>
                    </div>
                  </div>
                  {!isEditing && (
                    <Button
                      onClick={handleEdit}
                      className={styles[CSS_CLASSES.EDIT_BUTTON]}
                    >
                      <Edit className={ICONS.EDIT} />
                      {PROFILE_CONSTANTS.EDIT_PROFILE}
                    </Button>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className={styles[CSS_CLASSES.CARD_CONTENT]}>
                <form onSubmit={handleSave} className={styles[CSS_CLASSES.FORM]}>
                  {/* Personal Information Section */}
                  <div className={styles[CSS_CLASSES.FORM_SECTION]}>
                    <div className={styles[CSS_CLASSES.SECTION_HEADER]}>
                      <User className={styles[CSS_CLASSES.SECTION_ICON]} />
                      <h3 className={styles[CSS_CLASSES.SECTION_TITLE]}>{PROFILE_CONSTANTS.PERSONAL_INFO_TITLE}</h3>
                    </div>
                    
                    <div className={styles[CSS_CLASSES.FORM_GRID]}>
                      <div className={styles[CSS_CLASSES.FORM_FIELD]}>
                        <Label htmlFor={FORM_FIELDS.NAME} className={styles[CSS_CLASSES.FORM_LABEL]}>
                          {PROFILE_CONSTANTS.FULL_NAME_LABEL}
                        </Label>
                        <Input
                          id={FORM_FIELDS.NAME}
                          name={FORM_FIELDS.NAME}
                          value={form.name}
                          onChange={handleChange}
                          placeholder={PROFILE_CONSTANTS.FULL_NAME_PLACEHOLDER}
                          disabled={!isEditing}
                          className={styles[CSS_CLASSES.FORM_INPUT]}
                        />
                      </div>

                      <div className={styles[CSS_CLASSES.FORM_FIELD]}>
                        <Label htmlFor={FORM_FIELDS.EMAIL} className={styles[CSS_CLASSES.FORM_LABEL]}>
                          {PROFILE_CONSTANTS.EMAIL_LABEL}
                        </Label>
                        <Input
                          id={FORM_FIELDS.EMAIL}
                          name={FORM_FIELDS.EMAIL}
                          type="email"
                          value={form.email}
                          onChange={handleChange}
                          placeholder={PROFILE_CONSTANTS.EMAIL_PLACEHOLDER}
                          disabled={!isEditing}
                          className={styles[CSS_CLASSES.FORM_INPUT]}
                        />
                      </div>

                      <div className={styles[CSS_CLASSES.FORM_FIELD]}>
                        <Label htmlFor={FORM_FIELDS.PHONE} className={styles[CSS_CLASSES.FORM_LABEL]}>
                          {PROFILE_CONSTANTS.PHONE_LABEL}
                        </Label>
                        {isEditing ? (
                          <div className={phoneStyles.phoneInputContainer}>
                            <PhoneInput
                              country="us"
                              value={form.user_phonenumber || ''}
                              onChange={handlePhoneChange}
                              placeholder={PROFILE_CONSTANTS.PHONE_PLACEHOLDER}
                              enableAreaCodes={true}
                              inputProps={{
                                id: FORM_FIELDS.PHONE,
                                name: FORM_FIELDS.PHONE,
                                disabled: !isEditing,
                              }}
                              enableSearch={true}
                              disableSearchIcon={false}
                              searchPlaceholder="Search country..."
                              preferredCountries={['us', 'in', 'gb', 'ca', 'au']}
                            />
                          </div>
                        ) : (
                          <Input
                            id={FORM_FIELDS.PHONE}
                            name={FORM_FIELDS.PHONE}
                            value={form.user_phonenumber}
                            onChange={handleChange}
                            placeholder={PROFILE_CONSTANTS.PHONE_PLACEHOLDER}
                            disabled={!isEditing}
                            className={styles[CSS_CLASSES.FORM_INPUT]}
                          />
                        )}
                      </div>
                    </div>
                  </div>

                  <Separator className={styles[CSS_CLASSES.SEPARATOR]} />

                  {/* Account Settings Section */}
                  <div className={styles[CSS_CLASSES.FORM_SECTION]}>
                    <div className={styles[CSS_CLASSES.SECTION_HEADER]}>
                      <Shield className={styles[CSS_CLASSES.SECTION_ICON]} />
                      <h3 className={styles[CSS_CLASSES.SECTION_TITLE]}>{PROFILE_CONSTANTS.ACCOUNT_SETTINGS_TITLE}</h3>
                    </div>
                    
                    <div className={styles[CSS_CLASSES.FORM_GRID]}>
                      <div className={styles[CSS_CLASSES.FORM_FIELD]}>
                        <Label htmlFor="role" className={styles[CSS_CLASSES.FORM_LABEL]}>
                          {PROFILE_CONSTANTS.ROLE_LABEL}
                        </Label>
                        <Select 
                          value={form.role_id.toString()} 
                          onValueChange={handleRoleChange}
                          disabled={!isEditing}
                        >
                          <SelectTrigger className={styles[CSS_CLASSES.SELECT_TRIGGER]}>
                            <SelectValue placeholder={PROFILE_CONSTANTS.ROLE_PLACEHOLDER} />
                          </SelectTrigger>
                          <SelectContent>
                            {ROLES.map((role) => (
                              <SelectItem key={role.value} value={role.value.toString()}>
                                {role.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className={styles[CSS_CLASSES.FORM_FIELD]}>
                        <Label htmlFor="timezone" className={styles[CSS_CLASSES.FORM_LABEL]}>
                          {PROFILE_CONSTANTS.TIMEZONE_LABEL}
                        </Label>
                        <Select 
                          value={form.timezone} 
                          onValueChange={handleTimezoneChange}
                          disabled={!isEditing}
                        >
                          <SelectTrigger className={styles[CSS_CLASSES.SELECT_TRIGGER]}>
                            <SelectValue placeholder={PROFILE_CONSTANTS.TIMEZONE_PLACEHOLDER} />
                          </SelectTrigger>
                          <SelectContent>
                            {TIMEZONES.map((tz) => (
                              <SelectItem key={tz.value} value={tz.value}>
                                {tz.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className={styles[CSS_CLASSES.FORM_ACTIONS]}>
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleBackToDashboard}
                      className={styles[CSS_CLASSES.BACK_BUTTON]}
                    >
                      <ArrowLeft className={ICONS.ARROW_LEFT} />
                      {PROFILE_CONSTANTS.BACK_TO_DASHBOARD}
                    </Button>
                    
                    <div className={styles[CSS_CLASSES.ACTIONS_RIGHT]}>
                      {isEditing ? (
                        <>
                          <Button
                            type="button"
                            variant="destructive"
                            onClick={handleCancel}
                            className={styles[CSS_CLASSES.CANCEL_BUTTON]}
                          >
                            <X className={ICONS.X} />
                            {PROFILE_CONSTANTS.CANCEL}
                          </Button>
                          <Button
                            type="submit"
                            disabled={updating}
                            className={styles[CSS_CLASSES.SAVE_BUTTON]}
                          >
                            {updating ? (
                              <>
                                <Loader2 className={ICONS.LOADER} />
                                {PROFILE_CONSTANTS.SAVING}
                              </>
                            ) : (
                              <>
                                <CheckCircle className={ICONS.CHECK_CIRCLE} />
                                {PROFILE_CONSTANTS.SAVE_CHANGES}
                              </>
                            )}
                          </Button>
                        </>
                      ) : (
                        <div className={styles[CSS_CLASSES.EDIT_HINT]}>
                          {PROFILE_CONSTANTS.EDIT_HINT}
                        </div>
                      )}
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Profile Preview */}
          <div className={styles[CSS_CLASSES.PREVIEW_COLUMN]}>
            <Card className={styles[CSS_CLASSES.PREVIEW_CARD]}>
              <CardHeader className={styles[CSS_CLASSES.CARD_HEADER]}>
                <div className={styles[CSS_CLASSES.CARD_HEADER_LEFT]}>
                  <div className={styles[CSS_CLASSES.CARD_HEADER_ICON]}>
                    <UserCheck className={ICONS.USER_CHECK} />
                  </div>
                  <div className={styles[CSS_CLASSES.CARD_HEADER_TEXT]}>
                    <CardTitle className={styles[CSS_CLASSES.CARD_TITLE]}>
                      {PROFILE_CONSTANTS.PROFILE_PREVIEW_TITLE}
                    </CardTitle>
                    <p className={styles[CSS_CLASSES.CARD_SUBTITLE]}>{PROFILE_CONSTANTS.PREVIEW_SUBTITLE}</p>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className={styles[CSS_CLASSES.CARD_CONTENT]}>
                <div className={styles[CSS_CLASSES.PREVIEW_CONTENT]}>
                  <div className={styles[CSS_CLASSES.AVATAR]}>
                    {getInitials(form.name)}
                  </div>
                  
                  <div className={styles[CSS_CLASSES.PREVIEW_INFO]}>
                    <h3 className={styles[CSS_CLASSES.PREVIEW_NAME]}>{form.name}</h3>
                    <div className={styles[CSS_CLASSES.PREVIEW_BADGE]}>
                      <Badge variant={getRoleBadgeVariant(form.role_id)} className="text-sm">
                        {getRoleLabel(form.role_id)}
                      </Badge>
                    </div>
                    <p className={styles[CSS_CLASSES.PREVIEW_EMAIL]}>{form.email}</p>
                  </div>

                  <Separator className={styles[CSS_CLASSES.SEPARATOR]} />

                  <div className={styles[CSS_CLASSES.PREVIEW_DETAILS]}>
                    <div className={styles[CSS_CLASSES.DETAIL_ROW]}>
                      <span className={styles[CSS_CLASSES.DETAIL_LABEL]}>{PREVIEW_LABELS.PHONE}</span>
                      <span className={styles[CSS_CLASSES.DETAIL_VALUE]}>
                        {form.user_phonenumber || PROFILE_CONSTANTS.NOT_PROVIDED}
                      </span>
                    </div>
                    <div className={styles[CSS_CLASSES.DETAIL_ROW]}>
                      <span className={styles[CSS_CLASSES.DETAIL_LABEL]}>{PREVIEW_LABELS.TIMEZONE}</span>
                      <span className={styles[CSS_CLASSES.DETAIL_VALUE]}>{form.timezone}</span>
                    </div>
                    <div className={styles[CSS_CLASSES.DETAIL_ROW]}>
                      <span className={styles[CSS_CLASSES.DETAIL_LABEL]}>{PREVIEW_LABELS.STATUS}</span>
                      <Badge variant="outline" className={styles[CSS_CLASSES.STATUS_BADGE]}>
                        {PROFILE_CONSTANTS.ACTIVE_STATUS}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return <UserProfile />;
}
