export const LOGIN_CONSTANTS = {
  SUBTITLE: "Welcome back! Please sign in.",
  EMAIL_LABEL: "Email",
  EMAIL_PLACEHOLDER: "<EMAIL>",
  EMAIL_REQUIRED: "Email is required.",
  PASSWORD_LABEL: "Password",
  PASSWORD_PLACEHOLDER: "••••••••",
  PASSWORD_REQUIRED: "Password is required.",
  REMEMBER_ME: "Remember Me",
  FORGOT_PASSWORD: "Forgot Password?",
  LOGIN: "Login",
  LOADING: "Loading...",
  OR_CONTINUE_WITH: "Or continue with",
  CONTINUE_WITH_GOOGLE: "Continue with Google",
  REGISTER_PROMPT: "Don't have an account?",
  REGISTER_LINK: "Register",
  SUCCESS: "",
};

export const VALIDATION_MESSAGES = {
  EMAIL_INVALID: "Invalid email address",
  PASSWORD_REQUIRED: "Password is required",
} as const;

export const TOAST_MESSAGES = {
  LOGIN_SUCCESS: "Login successful! Welcome back.",
  AUTHENTICATION_FAILED: "Authentication failed",
  INTERNAL_SERVER_ERROR: "Internal server error",
} as const;

export const FORM_FIELDS = {
  EMAIL: "email",
  PASSWORD: "password",
} as const;

export const FORM_IDS = {
  EMAIL: "email",
  PASSWORD: "password",
} as const;

export const ACCESSIBILITY = {
  HIDE_PASSWORD: "Hide password",
  SHOW_PASSWORD: "Show password",
  DENSY_AI_LOGO: "Densy AI Logo",
  SIGN_IN: "Sign in",
} as const;

export const ASSETS = {
  LOGO_SRC: "/Densy%20AI.svg",
  LOGO_WIDTH: 150,
  LOGO_HEIGHT: 40,
} as const;

export const ANIMATION = {
  INITIAL_OPACITY: 0,
  INITIAL_SCALE: 0.96,
  FINAL_OPACITY: 1,
  FINAL_SCALE: 1,
  DURATION: 0.5,
} as const;

export const INPUT_TYPES = {
  EMAIL: "email",
  PASSWORD: "password",
  TEXT: "text",
} as const;

export const BUTTON_TYPES = {
  BUTTON: "button",
  SUBMIT: "submit",
} as const;

export const FORM_ATTRIBUTES = {
  EMAIL_AUTOCOMPLETE: "email",
  CURRENT_PASSWORD_AUTOCOMPLETE: "current-password",
} as const;

export const ROUTES = {
  FORGOT_PASSWORD: "/forgot-password",
  REGISTER: "/register",
  DASHBOARD: "/dashboard",
} as const;
