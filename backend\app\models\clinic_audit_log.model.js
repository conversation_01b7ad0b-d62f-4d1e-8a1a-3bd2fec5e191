import { DataTypes } from 'sequelize';

const ClinicAuditLogModel = (sequelize) => {
  return sequelize.define(
    'clinic_audit_logs',
    {
      id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
      record_id: { type: DataTypes.INTEGER, allowNull: false },
      action: { type: DataTypes.STRING, allowNull: false },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: { model: 'users', key: 'id' },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      },
      old_value: { type: DataTypes.TEXT, allowNull: true },
      new_value: { type: DataTypes.TEXT, allowNull: true },
      description: { type: DataTypes.TEXT, allowNull: true },
      error_details: { type: DataTypes.TEXT, allowNull: true },
      timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      tableName: 'clinic_audit_logs',
      timestamps: false,
      underscored: true,
    }
  );
};

export default ClinicAuditLogModel;
