/**
 * Appointment Notification Service: Handles sending both email and SMS notifications
 */
import { sendAppointmentConfirmationEmail, sendAppointmentCancellationEmail, sendAppointmentUpdateEmail } from './appointmentEmail.service.js';
import { sendAppointmentConfirmationSMS, sendAppointmentCancellationSMS, sendAppointmentUpdateSMS } from './sms.service.js';
import * as patientService from './patient.service.js';
import * as doctorService from './doctor.service.js';
import * as clinicService from './clinic.service.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';

/**
 * Send appointment confirmation notifications (email + SMS)
 * @param {Object} appointmentData - Appointment data
 * @param {Object} clinicData - Clinic data (must include sms_service and email_service flags)
 * @returns {Promise<Object>} Notification results
 */
export const sendAppointmentConfirmationNotifications = async (appointmentData, clinicData) => {
  const results = {
    email: { sent: false, error: null },
    sms: { sent: false, error: null }
  };

  try {
    // Fetch required data for notifications
    logger.info(loggerMessages.FETCHING_NOTIFICATION_DATA);
    const [patient, doctor] = await Promise.all([
      patientService.getPatientById(appointmentData.patient_id),
      doctorService.getDoctorById(appointmentData.doctor_id)
    ]);

    if (!patient) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      results.email.error = constants.PATIENT_NOT_FOUND_FOR_NOTIFICATION;
      results.sms.error = constants.PATIENT_NOT_FOUND_FOR_NOTIFICATION;
      return results;
    }

    if (!doctor) {
      logger.warn(loggerMessages.DOCTOR_NOT_FOUND_NOTIFICATION(appointmentData));
      results.email.error = constants.DOCTOR_NOT_FOUND_FOR_NOTIFICATION;
      results.sms.error = constants.DOCTOR_NOT_FOUND_FOR_NOTIFICATION;
      return results;
    }

    // Check if clinic has email service enabled
    if (clinicData.email_service !== false) {
      try {
        const emailResult = await sendAppointmentConfirmationEmail(appointmentData, patient, doctor, clinicData);
        results.email.sent = emailResult;
        
        if (!emailResult) {
          results.email.error = constants.APPOINTMENT_CONFIRMATION_EMAIL_FAILED(appointmentData);
        }
      } catch (error) {
        results.email.error = error.message;
        logger.error(constants.APPOINTMENT_CONFIRMATION_EMAIL_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.EMAIL_SERVICE_DISABLED(clinicData));
      results.email.error = constants.EMAIL_SERVICE_DISABLED_FOR_CLINIC;
    }

    // Check if clinic has SMS service enabled
    if (clinicData.sms_service !== false) {
      try {
        const smsResult = await sendAppointmentConfirmationSMS(appointmentData, patient, doctor, clinicData);
        results.sms.sent = smsResult;
        
        if (!smsResult) {
          results.sms.error = constants.APPOINTMENT_CONFIRMATION_SMS_FAILED(appointmentData);
        }
      } catch (error) {
        results.sms.error = error.message;
        logger.error(constants.APPOINTMENT_CONFIRMATION_SMS_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.SMS_SERVICE_DISABLED(clinicData));
      results.sms.error = constants.SMS_SERVICE_DISABLED_FOR_CLINIC;
    }

  } catch (error) {
    logger.error(`${constants.FAILED_TO_FETCH_NOTIFICATION_DATA} for appointment ${appointmentData.id}:`, error);
    results.email.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
    results.sms.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
  }
  
  logger.info(loggerMessages.APPOINTMENT_NOTIFICATIONS_SENT(appointmentData));

  return results;
};

/**
 * Send appointment cancellation notifications (email + SMS)
 * @param {Object} appointmentData - Appointment data
 * @param {Object} clinicData - Clinic data (must include sms_service and email_service flags)
 * @returns {Promise<Object>} Notification results
 */
export const sendAppointmentCancellationNotifications = async (appointmentData, clinicData) => {
  const results = {
    email: { sent: false, error: null },
    sms: { sent: false, error: null }
  };

  try {
    // Fetch required data for notifications
    logger.info(loggerMessages.FETCHING_NOTIFICATION_DATA);
    const patient = await patientService.getPatientById(appointmentData.patient_id);

    if (!patient) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      results.email.error = constants.PATIENT_NOT_FOUND_FOR_NOTIFICATION;
      results.sms.error = constants.PATIENT_NOT_FOUND_FOR_NOTIFICATION;
      return results;
    }

    // Check if clinic has email service enabled
    if (clinicData.email_service !== false) {
      try {
        const emailResult = await sendAppointmentCancellationEmail(appointmentData, patient, clinicData);
        results.email.sent = emailResult;
        
        if (!emailResult) {
          results.email.error = constants.APPOINTMENT_CANCELLATION_EMAIL_FAILED(appointmentData);
        }
      } catch (error) {
        results.email.error = error.message;
        logger.error(constants.APPOINTMENT_CANCELLATION_EMAIL_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.EMAIL_SERVICE_DISABLED(clinicData));
      results.email.error = constants.EMAIL_SERVICE_DISABLED_FOR_CLINIC;
    }

    // Check if clinic has SMS service enabled
    if (clinicData.sms_service !== false) {
      try {
        const smsResult = await sendAppointmentCancellationSMS(appointmentData, patient, clinicData);
        results.sms.sent = smsResult;
        
        if (!smsResult) {
          results.sms.error = constants.APPOINTMENT_CANCELLATION_SMS_FAILED(appointmentData);
        }
      } catch (error) {
        results.sms.error = error.message;
        logger.error(constants.APPOINTMENT_CANCELLATION_SMS_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.SMS_SERVICE_DISABLED(clinicData));
      results.sms.error = constants.SMS_SERVICE_DISABLED_FOR_CLINIC;
    }

  } catch (error) {
    logger.error(`${constants.FAILED_TO_FETCH_NOTIFICATION_DATA} for appointment ${appointmentData.id}:`, error);
    results.email.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
    results.sms.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
  }

  logger.info(loggerMessages.APPOINTMENT_NOTIFICATIONS_SENT(appointmentData));

  return results;
};

/**
 * Send appointment update notifications (email + SMS)
 * @param {Object} appointmentData - Updated appointment data
 * @param {Object} clinicData - Clinic data (must include sms_service and email_service flags)
 * @returns {Promise<Object>} Notification results
 */
export const sendAppointmentUpdateNotifications = async (appointmentData, clinicData) => {
  const results = {
    email: { sent: false, error: null },
    sms: { sent: false, error: null }
  };

  try {
    // Fetch required data for notifications
    logger.info(loggerMessages.FETCHING_NOTIFICATION_DATA);
    const [patient, doctor] = await Promise.all([
      patientService.getPatientById(appointmentData.patient_id),
      doctorService.getDoctorById(appointmentData.doctor_id)
    ]);

    if (!patient) {
      logger.warn(loggerMessages.PATIENT_NOT_FOUND_NOTIFICATION(appointmentData));
      results.email.error = constants.PATIENT_NOT_FOUND_FOR_NOTIFICATION;
      results.sms.error = constants.DOCTOR_NOT_FOUND_FOR_NOTIFICATION;
      return results;
    }

    if (!doctor) {
      logger.warn(loggerMessages.DOCTOR_NOT_FOUND_NOTIFICATION(appointmentData));
      results.email.error = constants.DOCTOR_NOT_FOUND_FOR_NOTIFICATION;
      results.sms.error = constants.DOCTOR_NOT_FOUND_FOR_NOTIFICATION;
      return results;
    }

    // Check if clinic has email service enabled
    if (clinicData.email_service !== false) {
      try {
        const emailResult = await sendAppointmentUpdateEmail(appointmentData);
        results.email.sent = emailResult;
        
        if (!emailResult) {
          results.email.error = constants.APPOINTMENT_UPDATE_EMAIL_FAILED(appointmentData);
        }
      } catch (error) {
        results.email.error = error.message;
        logger.error(constants.APPOINTMENT_UPDATE_EMAIL_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.EMAIL_SERVICE_DISABLED(clinicData));
      results.email.error = constants.EMAIL_SERVICE_DISABLED_FOR_CLINIC;
    }

    // Check if clinic has SMS service enabled
    if (clinicData.sms_service !== false) {
      try {
        const smsResult = await sendAppointmentUpdateSMS(appointmentData, patient, doctor, clinicData);
        results.sms.sent = smsResult;
        
        if (!smsResult) {
          results.sms.error = constants.APPOINTMENT_UPDATE_SMS_FAILED(appointmentData);
        }
      } catch (error) {
        results.sms.error = error.message;
        logger.error(constants.APPOINTMENT_UPDATE_SMS_FAILED(appointmentData), error);
      }
    } else {
      logger.info(loggerMessages.SMS_SERVICE_DISABLED(clinicData));
      results.sms.error = constants.SMS_SERVICE_DISABLED_FOR_CLINIC;
    }

  } catch (error) {
    logger.error(`${constants.FAILED_TO_FETCH_NOTIFICATION_DATA} for appointment ${appointmentData.id}:`, error);
    results.email.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
    results.sms.error = constants.FAILED_TO_FETCH_NOTIFICATION_DATA;
  }

  logger.info(loggerMessages.APPOINTMENT_NOTIFICATIONS_SENT(appointmentData));

  return results;
};

/**
 * Get notification summary for an appointment
 * @param {Object} results - Notification results from any of the above functions
 * @returns {string} Human-readable summary
 */
export const getNotificationSummary = (results) => {
  const emailStatus = results.email.sent ? 'Email: ✅' : `Email: ❌ (${results.email.error || 'Unknown error'})`;
  const smsStatus = results.sms.sent ? 'SMS: ✅' : `SMS: ❌ (${results.sms.error || 'Unknown error'})`;
  
  return `${emailStatus}, ${smsStatus}`;
};

/**
 * Check if clinic has notification services enabled
 * @param {Object} clinicData - Clinic data
 * @returns {Object} Service status
 */
export const getClinicNotificationStatus = (clinicData) => {
  return {
    email_enabled: clinicData.email_service !== false,
    sms_enabled: clinicData.sms_service !== false,
    has_any_service: (clinicData.email_service !== false) || (clinicData.sms_service !== false)
  };
};

