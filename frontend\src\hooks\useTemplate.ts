import { apiRequest } from "../utils/axios.utils";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import {
  TEMPLATE_CREATE_SUCCESS,
  TEMPLATE_CREATE_FAILED,
  TEMPLATE_UPDATE_SUCCESS,
  TEMPLATE_UPDATE_FAILED,
  TEMPLATE_DELETE_SUCCESS,
  TEMPLATE_DELETE_FAILED,
  TEMPLATE_FETCH_FAILED,
  TEMPLATES_FETCH_FAILED,
} from "@/Constants/TemplateMaster";

const TEMPLATE_CREATE_ENDPOINT = "/template";
const TEMPLATE_LIST_ENDPOINT = "/template";
const TEMPLATE_DETAIL_ENDPOINT = (id: number) => `/template/${id}`;

export interface AddTemplatePayload {
  type: string;
  name: string;
  content: string;
}

export const useTemplate = () => {
  const createTemplate = async (templateData: AddTemplatePayload) => {
    try {
      const response = await apiRequest.post(TEMPLATE_CREATE_ENDPOINT, templateData) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || TEMPLATE_CREATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || TEMPLATE_CREATE_FAILED);
        return {
          success: false,
          error: response?.message || TEMPLATE_CREATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, TEMPLATE_CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const getTemplates = async (params?: { is_deleted?: boolean; is_active?: boolean }) => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.is_deleted !== undefined) {
        queryParams.append('is_deleted', params.is_deleted.toString());
      }
      if (params?.is_active !== undefined) {
        queryParams.append('is_active', params.is_active.toString());
      }
      
      const endpoint = queryParams.toString() 
        ? `${TEMPLATE_LIST_ENDPOINT}?${queryParams.toString()}`
        : TEMPLATE_LIST_ENDPOINT;
        
      const response = await apiRequest.get(endpoint) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || TEMPLATES_FETCH_FAILED);
        return {
          success: false,
          error: response?.message || TEMPLATES_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, TEMPLATES_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const getTemplateById = async (id: number) => {
    try {
      const response = await apiRequest.get(TEMPLATE_DETAIL_ENDPOINT(id)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || TEMPLATE_FETCH_FAILED);
        return {
          success: false,
          error: response?.message || TEMPLATE_FETCH_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, TEMPLATE_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const updateTemplate = async (id: number, templateData: Record<string, unknown>) => {
    try {
      const response = await apiRequest.put(TEMPLATE_DETAIL_ENDPOINT(id), templateData) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || TEMPLATE_UPDATE_SUCCESS);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || TEMPLATE_UPDATE_FAILED);
        return {
          success: false,
          error: response?.message || TEMPLATE_UPDATE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, TEMPLATE_UPDATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  const deleteTemplate = async (id: number) => {
    try {
      const response = await apiRequest.delete(TEMPLATE_DETAIL_ENDPOINT(id)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        compactSuccessMessage(response.message || TEMPLATE_DELETE_SUCCESS);
        return { success: true };
      } else {
        compactErrorMessage(response?.message || TEMPLATE_DELETE_FAILED);
        return {
          success: false,
          error: response?.message || TEMPLATE_DELETE_FAILED,
        };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, TEMPLATE_DELETE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  return {
    createTemplate,
    getTemplates,
    getTemplateById,
    updateTemplate,
    deleteTemplate,
  };
}; 