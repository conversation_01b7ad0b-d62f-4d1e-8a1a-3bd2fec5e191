import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createCampaignSchema, updateCampaignSchema } from '../validators/campaign.validator.js';
import * as campaignController from '../controllers/campaign.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new campaign
// POST /v1/campaign/create
router.post(
  '',
  verifyToken,
  validate(createCampaignSchema),
  campaignController.createCampaign
);

// Get all campaigns
// GET /v1/campaign/list
router.get(
  '',
  verifyToken,
  campaignController.getAllCampaigns
);

// Get campaign by ID
// GET /v1/campaign/:id
router.get(
  '/:id',
  verifyToken,
  campaignController.getCampaignById
);

// Update campaign by ID
// PUT /v1/campaign/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateCampaignSchema),
  campaignController.updateCampaign
);

// Soft delete campaign by ID
// DELETE /v1/campaign/:id
router.delete(
  '/:id',
  verifyToken,
  campaignController.deleteCampaign
);

export default router; 