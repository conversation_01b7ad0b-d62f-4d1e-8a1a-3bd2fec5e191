// Pagination.tsx
// Renders pagination controls for navigating between pages of data.

import React from 'react';
import {
  Pagination as ShadcnPagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationPrevious,
  PaginationNext,
} from '@/components/ui/pagination';

/**
 * Props for the Pagination component
 * @property currentPage - The current active page number
 * @property totalPages - Total number of pages
 * @property onPageChange - Hand<PERSON> for changing the page
 * @property className - Optional additional class names
 */
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

/**
 * Renders pagination controls for navigating between pages.
 * Used for paginated lists or tables.
 */
const Pagination: React.FC<PaginationProps> = ({ currentPage, totalPages, onPageChange, className }) => {
  return (
    <ShadcnPagination className={className}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            href="#"
            onClick={e => { e.preventDefault(); onPageChange(Math.max(1, currentPage - 1)); }}
            aria-disabled={currentPage === 1}
          />
        </PaginationItem>
        {Array.from({ length: totalPages }, (_, i) => (
          <PaginationItem key={i}>
            <PaginationLink
              href="#"
              isActive={currentPage === i + 1}
              className={`rounded-full text-xs px-2 py-0.5 ${
                currentPage === i + 1 
                  ? 'bg-blue-100 text-blue-700 font-semibold border border-blue-300' 
                  : 'hover:bg-gray-100'
              }`}
              onClick={e => { e.preventDefault(); onPageChange(i + 1); }}
            >
              {i + 1}
            </PaginationLink>
          </PaginationItem>
        ))}
        <PaginationItem>
          <PaginationNext
            href="#"
            onClick={e => { e.preventDefault(); onPageChange(Math.min(totalPages, currentPage + 1)); }}
            aria-disabled={currentPage === totalPages}
          />
        </PaginationItem>
      </PaginationContent>
    </ShadcnPagination>
  );
};

export default Pagination; 