import React from "react";
import {
  Select,
  SelectContent,
  <PERSON>I<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { useClinic } from "@/hooks/useClinic";
import { useEffect, useState } from "react";
import { CLINIC_SELECTOR } from "@/Constants/ReactivationProgram";

interface Clinic {
  id: number;
  clinic_name: string;
  location: string;
}

interface ClinicSelectorProps {
  selectedClinicId: number | null;
  onClinicSelect: (clinicId: number) => void;
}

const ClinicSelector: React.FC<ClinicSelectorProps> = ({
  selectedClinicId,
  onClinicSelect,
}) => {
  const { getClinics } = useClinic();
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchClinics = async () => {
      setLoading(true);
      try {
        const result = await getClinics();
        if (result.success && result.data) {
          setClinics(result.data as Clinic[]);
        }
      } catch (error) {
        console.error("Failed to fetch clinics:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchClinics();
  }, [getClinics]);

  const handleClinicChange = (clinicId: string) => {
    const id = parseInt(clinicId);
    onClinicSelect(id);
  };

  return (
    <Select
      value={selectedClinicId?.toString() || ""}
      onValueChange={handleClinicChange}
      disabled={loading}
    >
      <SelectTrigger className="w-full">
        <SelectValue
          placeholder={
            loading
              ? CLINIC_SELECTOR.LOADING_PLACEHOLDER
              : CLINIC_SELECTOR.CHOOSE_PLACEHOLDER
          }
        />
      </SelectTrigger>
      <SelectContent>
        {clinics.map((clinic) => (
          <SelectItem key={clinic.id} value={clinic.id.toString()}>
            {clinic.clinic_name} - {clinic.location}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default ClinicSelector;
