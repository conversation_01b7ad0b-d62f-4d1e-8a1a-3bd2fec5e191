import { PatientAuditLog } from '../models/index.js';

/**
 * Log an entry to the patient audit log
 * @param {Object} logData - Audit log fields (action, record_id, user_id, old_value, new_value, description, error_details, timestamp)
 * @returns {Promise<Object>} The created audit log entry
 */
export const logPatientAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return PatientAuditLog.create(logData);
};
