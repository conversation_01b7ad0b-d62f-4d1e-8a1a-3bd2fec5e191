{"name": "mobio-node-init", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"server": "nodemon index.js", "start": "node index.js", "test:connection": "node test-connection.js", "debug:db": "node debug-db.js", "lint": "eslint .", "lint:fix": "eslint --fix .", "migrate": "npx sequelize-cli db:migrate", "seed": "npm run seed:roles && npm run seed:users", "seed:roles": "npx sequelize-cli db:seed --seed roles.seeder.js", "seed:users": "npx sequelize-cli db:seed --seed user.seeder.js", "format": "prettier --write .", "prepare": "husky install", "pre-commit": "lint-staged"}, "author": "Mobio Solutions", "license": "ISC", "dependencies": {"@babel/preset-env": "^7.24.7", "@sendgrid/mail": "^8.1.5", "axios": "^1.11.0", "babel-jest": "^29.7.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.6.1", "express": "^4.21.2", "express-validator": "^7.0.1", "form-data": "^4.0.4", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.2", "node-cron": "^4.2.1", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "twilio": "^5.8.0", "uuid": "^11.1.0", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/config-conventional": "^19.2.2", "@eslint/js": "^9.3.0", "commitlint": "^19.3.0", "cross-env": "^7.0.3", "eslint": "^9.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "globals": "^15.3.0", "husky": "^9.0.11", "jest": "^29.7.0", "lint-staged": "^15.2.4", "prettier": "^3.2.5", "sequelize-cli": "^6.6.3", "supertest": "^7.0.0"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write", "git add"]}}