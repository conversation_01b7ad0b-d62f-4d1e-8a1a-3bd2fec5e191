// PatientTable.tsx
// Renders a table of patient records with contact info, visit details, status, and actions.
// Used in the Patient Registry for managing and viewing patients.

import React, { useState } from "react";
import CommonView from "@/components/CommonComponents/CommonView";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Edit, Trash2, Phone, Loader2 } from "lucide-react";
import { makeElevenLabsCall } from "@/utils/axios.utils";
import {
  compactSuccessMessage,
  compactErrorMessage,
  formatPhoneNumber,
  formatPhoneNumberForCall,
} from "@/utils/commonFunctions";

import Table from "@/components/CommonComponents/Table";
import { Patient } from "@/hooks/usePatients";
import { capitalize } from "@/Constants/text";
import * as PatientConst from "@/Constants/PatientRegistry";
import ConfirmDeleteDialog from "@/components/CommonComponents/ConfirmDeleteDialog";
import { EditPatientForm } from "../forms/AddOrEditPatientForm";
import { patientTableColumns } from "@/utils/column";
import { Clinic, Doctor } from "@/app/patient-registry/page";
import { formatDateForDisplay } from "@/utils/dateUtils";

/**
 * Props for the PatientTable component
 * @property patients - Array of patient objects
 * @property formatDOB - Function to format date of birth
 * @property getPatientStatus - Function to get patient status
 */
interface PatientTableProps {
  patients: Patient[];
  getPatientStatus: (isActive: boolean) => string;
  updatePatient: (patient: Partial<Patient>, patientId: string) => void;
  deletePatient: (patientId: string) => void;
  doctors: Doctor[];
  clinics: Clinic[];
}

/**
 * Renders a table of patient records with contact info, visit details, status, and actions.
 * Used in the Patient Registry for managing and viewing patients.
 */
const PatientTable: React.FC<PatientTableProps> = ({
  patients,
  getPatientStatus,
  updatePatient,
  deletePatient,
  doctors,
  clinics,
}) => {
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [editPatientModalOpen, setEditPatientModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [callingPatientId, setCallingPatientId] = useState<string | null>(null);
  const [callDialogOpen, setCallDialogOpen] = useState(false);
  const [callTarget, setCallTarget] = useState<Patient | null>(null);
  const [disabledCallIds, setDisabledCallIds] = useState<string[]>([]);

  const handlePatientEditClick = (patient: Patient) => {
    // Create a copy of the patient with formatted phone number for editing
    const patientForEdit = {
      ...patient,
      phone_number: formatPhoneNumber(String(patient.phone_number || ''))
    };
    setSelectedPatient(patientForEdit);
    setEditPatientModalOpen(true);
  };

  const handleEditPatient = (patientData: Record<string, unknown>) => {
    // Convert the form data to the expected Patient format
    const updatedPatient: Partial<Patient> = {
      first_name: patientData.first_name as string,
      last_name: patientData.last_name as string,
      email: patientData.email as string,
      phone_number: patientData.phone_number as string,
      dob: patientData.dob as string,
      gender: patientData.gender as "male" | "female" | "other" | null,
      address: patientData.address as string,
      clinic_id: patientData.clinic_id as number,
      last_visit_summary: patientData.last_visit_summary as string | null,
      preferences: null, // Set to null as it's optional and not provided in form
      // Add other fields as needed
    };

    // Clean the data to remove null values that the backend doesn't accept
    const cleanData = Object.fromEntries(
      Object.entries(updatedPatient).filter(([, value]) => value !== null)
    );

    updatePatient(cleanData as Partial<Patient>, selectedPatient?.id as string);
    setEditPatientModalOpen(false);
  };

  const handleCloseEditPatient = () => {
    setEditPatientModalOpen(false);
  };

  const getFullName = (patient: Patient) => {
    return patient ? `${patient.first_name} ${patient.last_name}` : "";
  };

  const handleView = (patient: Patient) => {
    setSelectedPatient(patient);
    setViewModalOpen(true);
  };

  const handleCloseModal = () => {
    setViewModalOpen(false);
    setSelectedPatient(null);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return PatientConst.NA;

    try {
      // Use utility function to format date for display
      const formattedDate = formatDateForDisplay(dateString, "DD/MM/YYYY");
      return formattedDate || PatientConst.NA;
    } catch {
      return PatientConst.NA;
    }
  };

  const handleDeleteClick = (patient: Patient) => {
    setSelectedPatient(patient);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (selectedPatient) {
      deletePatient(selectedPatient.id);
      setDeleteDialogOpen(false);
      setSelectedPatient(null);
    }
  };

  const handleCallPatient = async (patient: Patient) => {
    if (!patient.phone_number) {
      compactErrorMessage(PatientConst.NO_PHONE_NUMBER);
      return;
    }

    setCallingPatientId(patient.id);

    try {
      // Format the phone number for API call with dynamic country code
      const phoneForCall = formatPhoneNumberForCall(patient.phone_number);

      const result = await makeElevenLabsCall(phoneForCall, {
        first_name: patient.first_name,
        last_name: patient.last_name,
        last_visit: patient.last_visit,
        last_visit_summary: patient.last_visit_summary,
        phone_number: patient.phone_number,
      });
      if (result.success) {
        const formattedPhone = formatPhoneNumber(patient.phone_number);
        compactSuccessMessage(
          `${PatientConst.CALL_INITIATED_SUCCESS} to ${patient.first_name} ${patient.last_name} (${formattedPhone})`
        );
      } else {
        compactErrorMessage(
          `${PatientConst.CALL_INITIATED_FAILED}: ${result.error}`
        );
      }
    } catch {
      compactErrorMessage(PatientConst.CALL_ERROR);
    } finally {
      setCallingPatientId(null);
    }
  };

  const handleCallClick = (patient: Patient) => {
    setCallTarget(patient);
    setCallDialogOpen(true);
  };

  const handleConfirmCall = async () => {
    if (callTarget) {
      await handleCallPatient(callTarget);
     
      setDisabledCallIds((prev) => [...prev, callTarget.id]);
      setTimeout(() => {
        setDisabledCallIds((prev) => prev.filter((id) => id !== callTarget.id));
      }, PatientConst.BUTTON_DISABLE_TIMEOUT); // 5 minutes
    }
    setCallDialogOpen(false);
    setCallTarget(null);
  };

  const getClinicName = (clinicId: number) => {
    const clinic = clinics.find((c) => c.id === clinicId);
    return clinic ? clinic.clinic_name : PatientConst.NA;
  };

  const formatLastVisit = (lastVisit: string | null) => {
    return lastVisit ? formatDate(lastVisit) : PatientConst.NA;
  };

  const formattedViewData = (data: Patient) => {
    // Check if data exists before accessing its properties
    if (!data) {
      return {};
    }

    // Get doctor names from selected doctor IDs
    const getDoctorNames = (doctorIds: number[] | null) => {
      if (!doctorIds || !Array.isArray(doctorIds) || doctorIds.length === 0) {
        return PatientConst.NA;
      }

      const doctorNames = doctorIds.map((id) => {
        const doctor = doctors.find((d) => d.id === id);
        return doctor ? doctor.doctor_name : `Doctor ID: ${id}`;
      });

      return doctorNames.join(", ");
    };

    return {
      [PatientConst.PATIENT_FORM_FIELDS.FULL_NAME]: getFullName(data),
      [PatientConst.PATIENT_FORM_FIELDS.EMAIL]: data.email,
      [PatientConst.PATIENT_FORM_FIELDS.PHONE]: formatPhoneNumber(
        data.phone_number
      ),
      [PatientConst.PATIENT_FORM_FIELDS.DOB]: data.dob
        ? formatDate(data.dob)
        : PatientConst.NA,
      [PatientConst.PATIENT_FORM_FIELDS.GENDER]: data.gender
        ? capitalize(data.gender)
        : PatientConst.NA,
      [PatientConst.LABEL_DOCTORS]: getDoctorNames(
        (data as unknown as Record<string, unknown>).doctors as number[] | null
      ),
      [PatientConst.TABLE_HEADER_STATUS]: getPatientStatus(data.is_active),
      [PatientConst.TABLE_HEADER_CLINIC]: getClinicName(data.clinic_id),
      [PatientConst.TABLE_HEADER_LAST_VISIT]: formatLastVisit(data.last_visit),
      [PatientConst.PATIENT_FORM_FIELDS.LAST_VISIT_SUMMARY]:
        data.last_visit_summary || PatientConst.NA,
    };
  };

  const handleActions = (patient: Record<string, unknown>) => {
    const patientData = patient as unknown as Patient;
    const isCalling = callingPatientId === patientData.id;
    const isCallDisabled = disabledCallIds.includes(patientData.id);

    return (
      <div className="flex space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleCallClick(patientData)}
          title={PatientConst.CALL_PATIENT_TITLE}
          disabled={isCalling || isCallDisabled}
        >
          {isCalling ? (
            <Loader2 className="h-4 w-4 text-green-600 animate-spin" />
          ) : (
            <Phone className="h-4 w-4 text-green-600" />
          )}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleView(patientData)}
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePatientEditClick(patientData)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleDeleteClick(patientData)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  if (!patients.length) return null;

  const formattedPatients = patients.map((patient) => ({
    ...patient,
    name: getFullName(patient),
    lastVisit: formatLastVisit(patient.last_visit),
    status: getPatientStatus(patient.is_active),
    dob: formatDate(patient.dob as string),
  }));

  return (
    <>
      <Table
        columns={patientTableColumns}
        data={formattedPatients}
        variant="striped"
        onActions={handleActions}
      />
      <CommonView
        open={viewModalOpen}
        onClose={handleCloseModal}
        data={formattedViewData(selectedPatient as Patient) || {}}
        title={PatientConst.PATIENT_DETAILS_TITLE}
      />
      <EditPatientForm
        isOpen={editPatientModalOpen}
        onClose={handleCloseEditPatient}
        initialValues={selectedPatient as unknown as Record<string, string>}
        onSubmit={handleEditPatient}
        doctors={doctors}
        clinics={clinics}
      />
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title={PatientConst.DELETE_PATIENT_TITLE}
        description={
          <span>
            {PatientConst.DELETE_PATIENT_DESCRIPTION_START}
            <b>{selectedPatient ? getFullName(selectedPatient) : ""}</b>
            {PatientConst.DELETE_PATIENT_DESCRIPTION_END}
          </span>
        }
        onConfirm={handleConfirmDelete}
        confirmText={PatientConst.DELETE_PATIENT_CONFIRM}
      />
      <ConfirmDeleteDialog
        open={callDialogOpen}
        confirmButtonVariant={PatientConst.BUTTON_TYPE.PRIMARY}
        onOpenChange={setCallDialogOpen}
        title={PatientConst.CALL_PATIENT_TITLE}
        description={<span>{PatientConst.DIALOG_CALL_PATIENT}</span>}
        onConfirm={handleConfirmCall}
        confirmText={PatientConst.BUTTON_TEXT.CONFIRM}
      />
    </>
  );
};

export default PatientTable;
