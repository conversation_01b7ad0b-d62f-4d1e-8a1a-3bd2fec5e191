import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import { MoreVertical } from 'lucide-react';
import { useAICallLog } from '@/hooks/useAICallLog';
import { DASHBOARD_TOTAL_CALLS } from '@/Constants/Dashboard';

const DashboardTotalCalls = () => {
  const { aiCallLogs, getAllAICallLogs } = useAICallLog();
  const [loading, setLoading] = useState(true);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      await getAllAICallLogs();
    } catch (error) {
      console.error(DASHBOARD_TOTAL_CALLS.ERROR_FETCHING, error);
    } finally {
      setLoading(false);
    }
  }, [getAllAICallLogs]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Calculate dynamic data from AI call logs
  const calculateCallData = () => {
    const totalCalls = aiCallLogs.length;
    
    // Calculate total duration in minutes
    const totalDuration = aiCallLogs.reduce((sum, log) => sum + log.call_duration, 0);
    const avgDuration = totalCalls > 0 ? (totalDuration / totalCalls) / 60 : 0; // Convert seconds to minutes
    
    return [
      {
        name: DASHBOARD_TOTAL_CALLS.DATA_LABELS.TOTAL_CALLS,
        value: totalCalls,
        color: DASHBOARD_TOTAL_CALLS.COLORS.TOTAL_CALLS
      },
      {
        name: DASHBOARD_TOTAL_CALLS.DATA_LABELS.TOTAL_MINUTE_USE,
        value: Math.round(totalDuration / 60), // Convert seconds to minutes
        color: DASHBOARD_TOTAL_CALLS.COLORS.TOTAL_MINUTE_USE
      },
      {
        name: DASHBOARD_TOTAL_CALLS.DATA_LABELS.AVG_CALL_DURATION,
        value: Math.round(avgDuration * 100) / 100, // Round to 2 decimal places
        color: DASHBOARD_TOTAL_CALLS.COLORS.AVG_CALL_DURATION
      }
    ];
  };

  const data = calculateCallData();
  const successRate = aiCallLogs.length > 0 
    ? Math.round((aiCallLogs.filter(log => log.call_status === 'successful').length / aiCallLogs.length) * 100)
    : 0;

  if (loading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm p-6" style={{ width: '360px', height: '396px' }}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="bg-white rounded-xl border border-gray-200 shadow-sm p-6"
      style={{ 
        width: '360px', 
        height: '396px',
        borderRadius: '8px',
        border: '1px solid #e5e7eb',
        padding: '24px'
      }}
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{DASHBOARD_TOTAL_CALLS.TITLE}</h3>
        <div className="flex items-center space-x-2">
          <div className="cursor-pointer p-1 hover:bg-gray-100 rounded-lg transition-colors">
            <MoreVertical className="h-5 w-5 text-gray-500" />
          </div>
        </div>
      </div>
      
      <div className="flex items-center justify-center mb-6">
        <div className="relative">
          <div className="h-36 w-36">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={70}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{successRate}%</div>
              <div className="text-xs text-gray-500">{DASHBOARD_TOTAL_CALLS.SUCCESS_RATE_LABEL}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="text-sm text-gray-600">{item.name}</span>
            </div>
            <span className="text-sm font-semibold text-gray-900">
              {typeof item.value === 'number' && item.value > 1000 
                ? item.value.toLocaleString() 
                : item.value.toFixed(2)}
              {item.name === DASHBOARD_TOTAL_CALLS.DATA_LABELS.AVG_CALL_DURATION ? DASHBOARD_TOTAL_CALLS.UNITS.MINUTES : ''}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DashboardTotalCalls;
