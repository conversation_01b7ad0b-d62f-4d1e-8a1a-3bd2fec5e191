import * as appointmentService from '../services/appointment.service.js';
import * as patientService from '../services/patient.service.js'
import * as clinicService from '../services/clinic.service.js';
import { logAppointmentAudit } from '../services/appointmentAuditLog.service.js';
import { sendAppointmentConfirmationNotifications, sendAppointmentUpdateNotifications, sendAppointmentCancellationNotifications } from '../services/appointmentNotification.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import { convertIndianTimeToUTC } from '../utils/timezone.util.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { AuditActions, AppointmentAuditDescriptions } from '../utils/auditlog_messages.utils.js';

/**
 * Create a new appointment
 * @route POST /v1/appointment/create
 */
export const createAppointment = async (req, res) => {
  logger.info(loggerMessages.CREATING_APPOINTMENT);
  try {
    const appointmentData = req.body;
    if (req.user && req.user.id) {
      appointmentData.created_by = req.user.id;
      appointmentData.updated_by = req.user.id;
    }

    let patient = null;
    
    if(appointmentData.phone){
      patient = await patientService.findPatientByEmailOrPhone({phone_number:appointmentData.phone})
    }
    if (patient){
    appointmentData.patient_id = patient.id
    }

    // Convert Indian time to UTC before saving
    if (appointmentData.appointment_time) {
      appointmentData.appointment_time = convertIndianTimeToUTC(appointmentData.appointment_time);
    }
    
    // Create the appointment
    const newAppointment = await appointmentService.createAppointment(appointmentData);
    
    // Log the appointment creation
    await logAppointmentAudit({
      action: AuditActions.CREATE,
      record_id: newAppointment.id,
      user_id: req.user?.id || null,
      new_value: JSON.stringify(newAppointment),
      description: AppointmentAuditDescriptions.APPOINTMENT_CREATED,
    });

    // Send confirmation notifications (email + SMS) to patient (non-blocking)
    try {
      // Get clinic data to check notification service settings
      const clinic = await clinicService.getClinicById(newAppointment.clinic_id);
      if (clinic) {
        await sendAppointmentConfirmationNotifications(newAppointment, clinic);
      } else {
        logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(newAppointment));
      }
    } catch (notificationError) {
      logger.warn(loggerMessages.APPOINTMENT_NOTIFICATIONS_FAILED(newAppointment));
      // Don't fail the appointment creation if notifications fail
    }

    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.APPOINTMENT_CREATED_SUCCESSFULLY, newAppointment)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_CREATING_APPOINTMENT);
    console.log(error);
    
    // Handle time slot conflict specifically
    if (error.message.includes(constants.TIME_SLOT_CONFLICT)) {
      return res.status(status.STATUS_CODE_CONFLICT).json(
        errorResponse(constants.TIME_SLOT_CONFLICT, error.message)
      );
    }
    
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all appointments
 * @route GET /v1/appointment/list
 */
export const getAllAppointments = async (req, res) => {
  logger.info(loggerMessages.FETCHING_APPOINTMENTS);
  try {
    const filters = req.query;
    const appointments = await appointmentService.getAllAppointments(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.APPOINTMENTS_FETCHED_SUCCESSFULLY, appointments)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_APPOINTMENTS);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get a single appointment by ID
 * @route GET /v1/appointment/:id
 */
export const getAppointmentById = async (req, res) => {
  logger.info(loggerMessages.FETCHING_APPOINTMENT_BY_ID);
  try {
    const { id } = req.params;
    const appointment = await appointmentService.getAppointmentById(id);
    
    if (!appointment) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.APPOINTMENT_NOT_FOUND)
      );
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.APPOINTMENT_FETCHED_SUCCESSFULLY, appointment)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_APPOINTMENT_BY_ID);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Update an appointment by ID
 * @route PUT /v1/appointment/:id
 */
export const updateAppointment = async (req, res) => {
  logger.info(loggerMessages.UPDATING_APPOINTMENT);
  try {
    const { id } = req.params;
    const appointmentData = req.body;
    
    if (req.user && req.user.id) {
      appointmentData.updated_by = req.user.id;
    }

    // Convert Indian time to UTC before updating
    if (appointmentData.appointment_time) {
      appointmentData.appointment_time = convertIndianTimeToUTC(appointmentData.appointment_time);
    }
    
    const existingAppointment = await appointmentService.getAppointmentById(id);
    if (!existingAppointment) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.APPOINTMENT_NOT_FOUND)
      );
    }
    
    const updatedAppointment = await appointmentService.updateAppointment(id, appointmentData);
    
    // Log the appointment update
    await logAppointmentAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(existingAppointment),
      new_value: JSON.stringify(updatedAppointment),
      description: AppointmentAuditDescriptions.APPOINTMENT_UPDATED,
    });

    // Send update notifications (email + SMS) to patient if it's a reschedule (non-blocking)
    try {
      // Get clinic data to check notification service settings
      const clinic = await clinicService.getClinicById(updatedAppointment.clinic_id);
      if (clinic) {
        await sendAppointmentUpdateNotifications(updatedAppointment, clinic);
      } else {
        logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(updatedAppointment));
      }
    } catch (notificationError) {
      logger.warn(loggerMessages.APPOINTMENT_NOTIFICATIONS_FAILED(updatedAppointment));
      // Don't fail the appointment update if notifications fail
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.APPOINTMENT_UPDATED_SUCCESSFULLY, updatedAppointment)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_APPOINTMENT);
    
    // Handle time slot conflict specifically
    if (error.message.includes(constants.TIME_SLOT_CONFLICT)) {
      return res.status(status.STATUS_CODE_CONFLICT).json(
        errorResponse(constants.TIME_SLOT_CONFLICT, error.message)
      );
    }
    
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Soft delete an appointment by ID
 * @route DELETE /v1/appointment/:id
 */
export const deleteAppointment = async (req, res) => {
  logger.info(loggerMessages.DELETING_APPOINTMENT);
  try {
    const { id } = req.params;
    
    const existingAppointment = await appointmentService.getAppointmentById(id);
    if (!existingAppointment) {
      return res.status(status.STATUS_CODE_NOT_FOUND).json(
        errorResponse(constants.APPOINTMENT_NOT_FOUND)
      );
    }
    
    const deletedAppointment = await appointmentService.deleteAppointment(id);
    
    // Log the appointment deletion
    await logAppointmentAudit({
      action: AuditActions.DELETE,
      record_id: id,
      user_id: req.user?.id || null,
      old_value: JSON.stringify(existingAppointment),
      description: AppointmentAuditDescriptions.APPOINTMENT_DELETED,
    });

    // Send cancellation notifications (email + SMS) to patient (non-blocking)
    try {
      // Get clinic data to check notification service settings
      const clinic = await clinicService.getClinicById(existingAppointment.clinic_id);
      if (clinic) {
        const notificationResults = await sendAppointmentCancellationNotifications(existingAppointment, clinic);
        logger.info(loggerMessages.APPOINTMENT_NOTIFICATIONS_SENT(existingAppointment));
      } else {
        logger.warn(loggerMessages.CLINIC_NOT_FOUND_NOTIFICATION(existingAppointment));
      }
    } catch (notificationError) {
      logger.warn(loggerMessages.APPOINTMENT_NOTIFICATIONS_FAILED(existingAppointment));
      // Don't fail the appointment deletion if notifications fail
    }
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.APPOINTMENT_DELETED_SUCCESSFULLY, deletedAppointment)
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_DELETING_APPOINTMENT);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Check if a time slot is available for booking
 */
export const checkTimeSlotAvailability = async (req, res) => {
  logger.info(loggerMessages.CHECKING_TIME_SLOT_AVAILABILITY);
  try {
    const { doctor_id, appointment_date, appointment_time } = req.query;
    
    if (!doctor_id || !appointment_date || !appointment_time) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.MISSING_REQUIRED_PARAMETERS)
      );
    }
    
    const appointmentData = { doctor_id, appointment_date, appointment_time };
    const isAvailable = await appointmentService.isTimeSlotAvailable(appointmentData);
    
    if (isAvailable) {
      return res.status(status.STATUS_CODE_SUCCESS).json(
        successResponse(constants.TIME_SLOT_AVAILABLE, { available: true })
      );
    } else {
      const conflicts = await appointmentService.getConflictingAppointments(appointmentData);
      return res.status(status.STATUS_CODE_SUCCESS).json(
        successResponse(constants.TIME_SLOT_NOT_AVAILABLE, { 
          available: false, 
          conflicts: conflicts 
        })
      );
    }
  } catch (error) {
    logger.error(loggerMessages.ERROR_CHECKING_TIME_SLOT_AVAILABILITY);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all available time slots for a doctor on a specific date
 */
export const getAvailableTimeSlots = async (req, res) => {
  logger.info(loggerMessages.GETTING_AVAILABLE_TIME_SLOTS);
  try {
    const { doctor_id, date } = req.query;
    let { start_time = "10:00", end_time = "16:00" } = req.query;

    if (!doctor_id || !date) {
      return res.status(status.STATUS_CODE_BAD_REQUEST).json(
        errorResponse(constants.MISSING_DOCTOR_DATE_PARAMETERS)
      );
    }

    const now = new Date();
    const todayStr = now.toISOString().split("T")[0];
    const requestedDate = new Date(date);

    // Reject past dates (before today)
    if (requestedDate < new Date(todayStr)) {
      return res.status(status.STATUS_CODE_SUCCESS).json(
        successResponse(constants.AVAILABLE_SLOTS_RETRIEVED_SUCCESSFULLY, {
          doctor_id,
          date,
          start_time,
          end_time,
          available_slots: [],
          total_slots: 0
        })
      );
    }

    // Adjust start_time if today
    if (date === todayStr) {
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();

      // Round up to the next 30-minute interval
      let roundedHour = currentHour;
      let roundedMinute = currentMinute <= 30 ? 30 : 0;
      if (currentMinute > 30) {
        roundedHour += 1;
      }

      const roundedTime = `${String(roundedHour).padStart(2, '0')}:${String(roundedMinute).padStart(2, '0')}`;

      if (roundedTime > start_time) {
        start_time = roundedTime;
      }
    }

    const availableSlots = await appointmentService.getAvailableTimeSlots(
      doctor_id, 
      date, 
      start_time, 
      end_time
    );
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.AVAILABLE_SLOTS_RETRIEVED_SUCCESSFULLY, {
        doctor_id,
        date,
        start_time,
        end_time,
        available_slots: availableSlots,
        total_slots: availableSlots.length
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_GETTING_AVAILABLE_TIME_SLOTS);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
}; 