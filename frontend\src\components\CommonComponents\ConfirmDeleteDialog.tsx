import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>ert<PERSON><PERSON>og<PERSON><PERSON>er,
  AlertDialog<PERSON>ooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alert-dialog";
import { BUTTON_TEXT, BUTTON_TYPE } from "@/Constants/PatientRegistry";

interface ConfirmDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: React.ReactNode;
  onConfirm: () => void;
  confirmText?: string;
  confirmButtonVariant?: typeof BUTTON_TYPE[keyof typeof BUTTON_TYPE] ;
}

const ConfirmDeleteDialog: React.FC<ConfirmDeleteDialogProps> = ({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  confirmText = BUTTON_TEXT.DELETE,
  confirmButtonVariant = BUTTON_TYPE.DESTRUCTIVE,
}) => {
  const buttonColor = confirmButtonVariant === BUTTON_TYPE.DESTRUCTIVE ? "red" : "blue";
  const buttonClass = `bg-${buttonColor}-600 hover:bg-${buttonColor}-700 text-white`;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={() => onOpenChange(false)}>
            {BUTTON_TEXT.CANCEL}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={() => {
              onConfirm();
              onOpenChange(false);
            }}
            className={buttonClass}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
export default ConfirmDeleteDialog;