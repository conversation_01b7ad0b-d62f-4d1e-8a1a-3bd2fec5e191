/**
 * Extracts dynamic variables from template content
 * Supports various variable formats: {variable}, {{variable}}, ${variable}, etc.
 * @param content - The template content string
 * @returns Array of unique variable names
 */
export const extractTemplateVariables = (content: string): string[] => {
  if (!content || typeof content !== 'string') {
    return [];
  }

  // Regex patterns to match different variable formats
  const variablePatterns = [
    /\{([^}]+)\}/g,        // {variable}
    /\{\{([^}]+)\}\}/g,    // {{variable}}
    /\$([a-zA-Z_][a-zA-Z0-9_]*)/g,  // $variable
    /\$\{([^}]+)\}/g,      // ${variable}
    /%([a-zA-Z_][a-zA-Z0-9_]*)%/g,  // %variable%
  ];

  const variables = new Set<string>();

  variablePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      // Extract the variable name from the match
      const variableName = match[1] || match[0];
      
      // Clean up the variable name (remove any remaining braces, etc.)
      const cleanVariable = variableName
        .replace(/[{}%$]/g, '') // Remove braces, %, and $
        .trim();
      
      // Only add if it's not empty and looks like a valid variable name
      if (cleanVariable && /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(cleanVariable)) {
        variables.add(cleanVariable);
      }
    }
  });

  return Array.from(variables).sort();
};

/**
 * Formats variable names for display
 * @param variableName - The raw variable name
 * @returns Formatted display name
 */
export const formatVariableName = (variableName: string): string => {
  return variableName
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
    .trim();
};

/**
 * Gets the variable format used in the template
 * @param content - The template content
 * @returns The detected variable format
 */
export const getVariableFormat = (content: string): string => {
  if (!content) return '{variable}';
  
  if (/\{\{([^}]+)\}\}/.test(content)) return '{{variable}}';
  if (/\$([a-zA-Z_][a-zA-Z0-9_]*)/.test(content)) return '$variable';
  if (/\$\{([^}]+)\}/.test(content)) return '${variable}';
  if (/%([a-zA-Z_][a-zA-Z0-9_]*)%/.test(content)) return '%variable%';
  
  return '{variable}'; // Default format
}; 