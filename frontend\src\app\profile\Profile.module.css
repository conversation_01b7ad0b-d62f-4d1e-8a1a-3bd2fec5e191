/* Profile Page Styles */

/* Container and Layout */
.profileContainer {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 2rem 0;
}

.profileWrapper {
  max-width: 72rem;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .profileWrapper {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .profileWrapper {
    padding: 0 2rem;
  }
}

.profileGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .profileGrid {
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
}

.formColumn {
  grid-column: 1;
  order: 1;
}

.previewColumn {
  grid-column: 2;
  order: 2;
}

@media (max-width: 767px) {
  .formColumn {
    grid-column: 1;
    order: 1;
  }
  
  .previewColumn {
    grid-column: 1;
    order: 2;
  }
}

/* Card Styles */
.profileCard {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 0.5rem;
  background: white;
  width: 100%;
}

.previewCard {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 0.5rem;
  background: white;
  height: fit-content;
  width: 100%;
}

/* Card Header */
.cardHeader {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.cardHeaderContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cardHeaderLeft {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.cardHeaderIcon {
  padding: 0.5rem;
  background-color: #dbeafe;
  border-radius: 0.5rem;
}

.cardHeaderText {
  display: flex;
  flex-direction: column;
}

.cardTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.cardSubtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* Edit Button - Matching Change Password styling */
.editButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.editButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.editButton:hover::before {
  left: 100%;
}

.editButton:hover {
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.editButton:focus,
.editButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

/* Card Content */
.cardContent {
  padding: 1.5rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Form Sections */
.formSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sectionIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: #2563eb;
}

.sectionTitle {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .formGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Form Fields */
.formField {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formLabel {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
  transition: color 0.3s ease;
}

.formLabel:hover {
  color: #2563eb;
}

.formInput {
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.formInput:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.formInput:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

/* Select Styles */
.selectTrigger {
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
}

.selectTrigger:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.selectTrigger:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

/* Separator */
.separator {
  margin: 1.5rem 0;
  border: none;
  height: 1px;
  background-color: #e5e7eb;
}

/* Form Actions */
.formActions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

@media (min-width: 640px) {
  .formActions {
    flex-direction: row;
  }
}

.backButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  background: transparent;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: color 0.2s;
}

.backButton:hover {
  color: #374151;
}

.actionsRight {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-left: auto;
}

@media (min-width: 640px) {
  .actionsRight {
    flex-direction: row;
  }
}

/* Cancel Button - Matching Change Password styling */
.cancelButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cancelButton:hover {
  background-color: #dc2626;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.15);
}

/* Save Button - Matching Change Password styling */
.saveButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.saveButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.saveButton:hover::before {
  left: 100%;
}

.saveButton:hover {
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.saveButton:focus,
.saveButton:active {
  outline: none;
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

.saveButton:disabled {
  background: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
  box-shadow: none;
}

.saveButton:disabled::before {
  display: none;
}

.editHint {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Preview Card */
.previewContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.avatar {
  width: 5rem;
  height: 5rem;
  border: 4px solid #dbeafe;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dbeafe 0%, #c7d2fe 100%);
  color: #1d4ed8;
  font-size: 1.5rem;
  font-weight: 700;
}

.previewInfo {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.previewName {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.previewBadge {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.previewEmail {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.previewDetails {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detailLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

.detailValue {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.statusBadge {
  font-size: 0.75rem;
  background-color: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

/* Loading and Error States */
.loadingContainer {
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loadingContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.loadingSpinner {
  width: 3rem;
  height: 3rem;
  color: #2563eb;
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

.loadingText {
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
}

.errorContainer {
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.errorContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.errorIcon {
  width: 3rem;
  height: 3rem;
  color: #ef4444;
  margin: 0 auto;
}

.errorText {
  font-size: 1.125rem;
  font-weight: 500;
  color: #dc2626;
}

/* Error Button - Matching Change Password styling */
.errorButton {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.08);
}

.errorButton:hover {
  background: linear-gradient(90deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.15);
}

.noDataContainer {
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: center;
}

.noDataContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.noDataIcon {
  width: 3rem;
  height: 3rem;
  color: #60a5fa;
  margin: 0 auto;
}

.noDataText {
  font-size: 1.125rem;
  font-weight: 500;
  color: #6b7280;
}

/* Refresh Button - Matching Change Password styling */
.refreshButton {
  color: #ffffff;
  background: linear-gradient(90deg, #2563eb 0%, #22c55e 100%);
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.refreshButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.refreshButton:hover::before {
  left: 100%;
}

.refreshButton:hover {
  background: linear-gradient(90deg, #1e40af 0%, #22c55e 100%);
  box-shadow: 0 4px 16px rgba(37, 99, 235, 0.15);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus styles for accessibility */
.formInput:focus-visible,
.selectTrigger:focus-visible,
.saveButton:focus-visible,
.cancelButton:focus-visible,
.editButton:focus-visible,
.errorButton:focus-visible,
.refreshButton:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cardHeaderContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .actionsRight {
    margin-left: 0;
  }
  
  .saveButton,
  .cancelButton {
    width: 100%;
    justify-content: center;
  }
} 