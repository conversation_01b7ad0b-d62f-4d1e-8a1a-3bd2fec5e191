/**
 * Webhook Service: Handles business logic for webhook operations.
 * Manages webhook signature verification and ElevenLabs integration.
 * <AUTHOR>
 */
import crypto from 'crypto';
import { fetchTranscriptFromElevenLabs } from './escalation.service.js';
import { AICallLog } from '../models/index.js';
import logger from '../config/logger.config.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';

/**
 * Verify webhook signature using HMAC
 * @param {string} payload - Raw request body
 * @param {string} signature - Webhook signature from headers
 * @param {string} secret - Webhook secret key
 * @returns {boolean} True if signature is valid
 * <AUTHOR>
 */
export const verifyWebhookSignature = (payload, signature, secret) => {
  try {
    if (!secret) {
      logger.error(logMessages.WEBHOOK_SECRET_MISSING);
      throw new Error(constants.WEBHOOK_SECRET_MISSING);
    }

    if (!signature) {
      logger.error(logMessages.WEBHOOK_MISSING_SIGNATURE);
      throw new Error(constants.WEBHOOK_MISSING_SIGNATURE);
    }

    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );

    if (isValid) {
      logger.info(logMessages.WEBHOOK_SIGNATURE_VERIFIED);
    } else {
      logger.error(logMessages.WEBHOOK_SIGNATURE_VERIFICATION_FAILED);
    }

    return isValid;
  } catch (error) {
    logger.error(`${logMessages.ERROR_PROCESSING_WEBHOOK}: ${error.message}`);
    throw error;
  }
};

/**
 * Process ElevenLabs webhook and fetch transcript
 * @param {Object} webhookData - Parsed webhook payload
 * @param {string} apiKey - ElevenLabs API key
 * @returns {Promise<Object>} Transcript data and processing result
 * <AUTHOR>
 */
export const processElevenLabsWebhook = async (webhookData, apiKey) => {
  try {
    if (!webhookData.data || !webhookData.data.conversation_id) {
      throw new Error(constants.WEBHOOK_INVALID_PAYLOAD);
    }

    const conversationId = webhookData.data.conversation_id;
    
    if (!apiKey) {
      throw new Error(constants.WEBHOOK_ELEVENLABS_API_KEY_NOT_CONFIGURED);
    }

    const transcript = await fetchTranscriptFromElevenLabs(conversationId, apiKey);

    // 🔹 Update transcript in DB directly
    if (transcript && transcript.length > 0) {
      try {
        // First check if the call log exists
        const existingCallLog = await AICallLog.findOne({
          where: { conversation_id: conversationId }
        });
        
        if (existingCallLog) {
          // Store the complete transcript structure with role and message
          await AICallLog.update(
            { transcript: transcript },
            { where: { conversation_id: conversationId } }
          );
          logger.info(constants.WEBHOOK_TRANSCRIPT_UPDATED_IN_DB.replace('{id}', conversationId));
        } else {
          logger.warn(constants.WEBHOOK_NO_CALL_LOG_FOUND.replace('{id}', conversationId));
        }
      } catch (updateError) {
        logger.error(constants.WEBHOOK_FAILED_TO_UPDATE_TRANSCRIPT.replace('{error}', updateError.message));
        // Don't throw error here, just log it - the transcript was fetched successfully
      }
    } else {
      logger.warn(constants.WEBHOOK_TRANSCRIPT_EMPTY.replace('{id}', conversationId));
    }

  
    
    if (transcript && transcript.length > 0) {
      logger.info(constants.WEBHOOK_TRANSCRIPT_SUMMARY, {
        totalEntries: transcript.length,
        roles: [...new Set(transcript.map(entry => entry.role))],
        totalMessageLength: transcript.reduce((sum, entry) => sum + (entry.message?.length || 0), 0)
      });
    } else {
      logger.warn(constants.WEBHOOK_TRANSCRIPT_EMPTY_OR_NULL);
    }

    logger.info(logMessages.TRANSCRIPT_FETCHED_SUCCESSFULLY);

    return {
      success: true,
      conversationId,
      transcript,
      message: constants.WEBHOOK_TRANSCRIPT_FETCHED_SUCCESSFULLY,
      metadata: {
        transcriptEntries: transcript ? transcript.length : 0,
        fetchedAt: new Date().toISOString(),
        hasContent: transcript && transcript.length > 0
      }
    };
  } catch (error) {
    throw new Error(`${constants.WEBHOOK_TRANSCRIPT_FETCH_FAILED}: ${error.message}`);
  }
};

/**
 * Validate webhook payload structure
 * @param {Object} payload - Parsed webhook payload
 * @returns {boolean} True if payload is valid
 * <AUTHOR>
 */
export const validateWebhookPayload = (payload) => {
  try {
    if (!payload || typeof payload !== constants.WEBHOOK_OBJECT_TYPE) {
      return false;
    }

    if (!payload.data || !payload.data.conversation_id) {
      return false;
    }

    return true;
  } catch (error) {
    logger.error(constants.WEBHOOK_PAYLOAD_VALIDATION_FAILED + `: ${error.message}`);
    return false;
  }
};
