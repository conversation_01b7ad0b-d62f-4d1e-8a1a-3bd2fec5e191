/**
 * Doctor Validators: express-validator schemas for doctor create and update endpoints.
 */
import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';

// Validation schema for creating a doctor
export const createDoctorSchema = [
  body('doctor_name').notEmpty().withMessage(constants.DOCTOR_NAME_REQUIRED),
  body('clinic_id').notEmpty().isInt().withMessage(constants.DOCTOR_CLINIC_ID_REQUIRED),
  body('specialization').optional(),
  body('doctor_email').optional().isEmail().withMessage(constants.INVALID_DOCTOR_EMAIL),
  body('doctor_phonenumber').optional().isString().withMessage(constants.INVALID_DOCTOR_PHONENUMBER),
  body('experience_years').optional().isInt().withMessage(constants.EXPERIENCE_YEARS_INTEGER),
];

// Validation schema for updating a doctor
export const updateDoctorSchema = [
  body('doctor_name').optional().notEmpty().withMessage(constants.DOCTOR_NAME_CANNOT_BE_EMPTY),
  body('specialization').optional(),
  body('doctor_email').optional().isEmail().withMessage(constants.INVALID_DOCTOR_EMAIL),
  body('doctor_phonenumber').optional().isString().withMessage(constants.INVALID_DOCTOR_PHONENUMBER),
  body('is_active').optional().isBoolean().withMessage(constants.DOCTOR_IS_ACTIVE_BOOLEAN),
  body('experience_years').optional().isInt().withMessage(constants.EXPERIENCE_YEARS_INTEGER),
]; 