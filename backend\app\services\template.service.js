import { Op } from 'sequelize';
import { Template } from '../models/index.js';

/**
 * Check if a template exists by name and type
 * @param {Object} params - { name, type }
 * @returns {Promise<Object|null>} The template if found, else null
 * <AUTHOR>
 */
export const findTemplateByNameAndType = async ({ name, type }) => {
  return await Template.findOne({
    where: {
      name,
      type,
      is_deleted: false,
    },
  });
};

/**
 * Get all templates, with optional filters for is_active, is_deleted, and type
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of templates
 * <AUTHOR>
 */
export const getAllTemplates = async (filters = {}) => {
  try {
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.type) where.type = filters.type;
    
    const templates = await Template.findAll({ where });
    return templates;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get a template by its ID
 * @param {number} id - Template ID
 * @returns {Promise<Object|null>} The template or null if not found
 * <AUTHOR>
 */
export const getTemplateById = async (id) => {
  try {
    const template = await Template.findByPk(id);
    return template;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create a new template
 * @param {Object} templateData - Data for new template
 * @returns {Promise<Object>} The created template
 * <AUTHOR>
 */
export const createTemplate = async (templateData) => {
  try {
    const newTemplate = await Template.create(templateData);
    return newTemplate;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update a template by its ID
 * @param {number} id - Template ID
 * @param {Object} templateData - Data to update
 * @returns {Promise<Object|null>} The updated template or null if not found
 * <AUTHOR>
 */
export const updateTemplate = async (id, templateData) => {
  try {
    await Template.update(templateData, { where: { id } });
    const updatedTemplate = await Template.findByPk(id);
    return updatedTemplate;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Soft delete a template by its ID (sets is_deleted to true)
 * @param {number} id - Template ID
 * @returns {Promise<Object|null>} The deleted template or null if not found
 * <AUTHOR>
 */
export const deleteTemplate = async (id) => {
  try {
    await Template.update({ is_deleted: true }, { where: { id } });
    const deletedTemplate = await Template.findByPk(id);
    return deletedTemplate;
  } catch (error) {
    throw new Error(error.message);
  }
}; 