"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, BarChart3, Users, Phone, CheckCircle } from "lucide-react";
import AddCampaignForm from "@/components/forms/AddCampaignForm";
import ReactivationStatsGrid from "@/components/ReactivationProgram/ReactivationStatsGrid";
import ClinicSelector from "@/components/ReactivationProgram/ClinicSelector";
import PatientList from "@/components/ReactivationProgram/PatientList";
import Batch<PERSON>allSubmission from "@/components/ReactivationProgram/BatchCallSubmission";
import BatchCallResults from "@/components/ReactivationProgram/BatchCallResults";
import ReactivationStatsTable from "@/components/ReactivationProgram/ReactivationStatsTable";
import {
  REACTIVATION_PROGRAM_TITLE,
  REACTIVATION_PROGRAM_SUBTI<PERSON><PERSON>,
  REACTIVATION_PROGRAM_ADD_NEW,
  CAMPAIGNS_DATA,
  REACTIVATION_CAMPAIGNS_TITLE,
  PATIENT_MANAGEMENT_TITLE,
  STATISTICS_TITLE,
  STATISTICS_SELECT_CLINIC,
  COMING_SOON_TITLE,
  COMING_SOON_DESCRIPTION,
  LOADING_STATISTICS,
  ERROR_LOADING_STATISTICS,
  STAT_TOTAL_CAMPAIGNS,
  STAT_SUCCESS_RATE,
  STAT_PATIENTS_CONTACTED,
  STAT_ACTIVE_CAMPAIGNS,
  STAT_COMPLETED_CAMPAIGNS,
  STAT_FAILED_CAMPAIGNS,
  STAT_CAMPAIGN_EFFICIENCY,
  STAT_PATIENTS_PER_CAMPAIGN,
  PERFORMANCE_METRICS_TITLE,
  CAMPAIGN_STATUS_BREAKDOWN_TITLE,
} from "@/Constants/ReactivationProgram";
import {
  usePatientManagement,
  BatchCallResponse,
} from "@/hooks/usePatientManagement";
import { useReactivationManagement } from "@/hooks/useReactivationManagement";
import PageSection from "@/components/CommonComponents/PageSection";
import { useAppointment } from "@/hooks/useAppointment";
import { useAICallLog } from "@/hooks/useAICallLog";
import AddReactivationForm from "@/components/ReactivationProgram/AddReactivationForm";

const ReactivationProgram = () => {
  const [isAddReactivationOpen, setIsAddReactivationOpen] = useState(false);
  const [campaigns, setCampaigns] = useState(CAMPAIGNS_DATA);

  // Patient management state
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);
  const [selectedPatientIds, setSelectedPatientIds] = useState<number[]>([]);
  const [batchCallResults, setBatchCallResults] =
    useState<BatchCallResponse | null>(null);
  const [showResults, setShowResults] = useState(false);

  // View state
  const [activeView, setActiveView] = useState<
    "campaigns" | "patients" | "stats"
  >("campaigns");

  const {
    patients,
    loading,
    getPatientsByClinic,
    submitBatchCall,
    clearPatients,
  } = usePatientManagement();

  const { aiCallLogs, getAllAICallLogs } = useAICallLog();

  const { appointments, getAllAppointments } = useAppointment();

  const {
    summary,
    reactivations,
    error,
    getReactivationSummary,
    getReactivationsByClinic,
  } = useReactivationManagement();

  type Campaign = (typeof CAMPAIGNS_DATA)[number];
  const handleAddCampaign = (data: Record<string, unknown>) => {
    setCampaigns([...campaigns, data as Campaign]);
  };

  // Handle clinic selection
  const handleClinicSelect = useCallback(
    async (clinicId: number) => {
      setSelectedClinicId(clinicId);
      setSelectedPatientIds([]);
      setBatchCallResults(null);
      setShowResults(false);

      // Fetch both patients and reactivation summary
      const [patientsResult, summaryResult] = await Promise.all([
        getPatientsByClinic(clinicId),
        getReactivationSummary(clinicId),
      ]);

      if (!patientsResult.success) {
        console.error("Failed to fetch patients:", patientsResult.error);
      }

      if (!summaryResult.success) {
        console.error(
          "Failed to fetch reactivation summary:",
          summaryResult.error
        );
      }
    },
    [getPatientsByClinic, getReactivationSummary]
  );

  // Handle patient selection change
  const handlePatientSelectionChange = useCallback((patientIds: number[]) => {
    setSelectedPatientIds(patientIds);
  }, []);

  // Handle batch call submission
  const handleBatchCallSubmit = useCallback(
    async (time?: string) => {
      if (!selectedClinicId || selectedPatientIds.length === 0) {
        return;
      }

      const result = await submitBatchCall(
        selectedClinicId,
        selectedPatientIds,
        time
      );
      if (result.success && result.data) {
        setBatchCallResults(result.data as BatchCallResponse);
        setShowResults(true);
      }
    },
    [selectedClinicId, selectedPatientIds, submitBatchCall]
  );

  // Clear results and reset
  const handleCloseResults = useCallback(() => {
    setShowResults(false);
    setBatchCallResults(null);
    setSelectedPatientIds([]);
    clearPatients();
  }, [clearPatients]);

  useEffect(() => {
    getAllAppointments();
  }, [getAllAppointments]);

  // Fetch reactivations when stats tab is active and clinic is selected
  useEffect(() => {
    if (selectedClinicId) {
      console.log("Fetching data for stats tab, clinic ID:", selectedClinicId);
      Promise.all([
        getReactivationSummary(selectedClinicId),
        getReactivationsByClinic(selectedClinicId),
      ]).catch((error) => {
        console.error("Error fetching stats data:", error);
      });
    }
  }, [
    activeView,
    selectedClinicId,
    getReactivationSummary,
    getReactivationsByClinic,
  ]);

  useEffect(() => {
    getAllAICallLogs();
  }, [getAllAICallLogs]);
  const today = new Date().toISOString().split("T")[0]; // "2025-09-15" format

  const appointmentCount = Array.isArray(appointments)
    ? appointments.filter(
        (appointment) => appointment.appointment_date === today
      ).length
    : 0;

  // Fetch data when component mounts if clinic is already selected
  useEffect(() => {
    if (selectedClinicId) {
      console.log("Initial data fetch for stats, clinic ID:", selectedClinicId);
      Promise.all([
        getReactivationSummary(selectedClinicId),
        getReactivationsByClinic(selectedClinicId),
      ]).catch((error) => {
        console.error("Error in initial stats fetch:", error);
      });
    }
  }, [
    selectedClinicId,
    activeView,
    getReactivationSummary,
    getReactivationsByClinic,
  ]);

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            {REACTIVATION_PROGRAM_TITLE}
          </h2>
          <p className="text-gray-600">{REACTIVATION_PROGRAM_SUBTITLE}</p>
        </div>
        <Button variant="main" onClick={() => setIsAddReactivationOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {REACTIVATION_PROGRAM_ADD_NEW}
        </Button>
      </div>

      {/* Stats */}
      <ReactivationStatsGrid
        appointments={appointmentCount}
        calls={aiCallLogs?.length || 0}
        successRate={`${summary?.success_rate || 0}%`}
        newBookings={summary?.completed_campaigns || 0}
      />

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveView('campaigns')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeView === 'campaigns'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart3 className="h-4 w-4 inline mr-2" />
            Campaigns
          </button>
          <button
            onClick={() => setActiveView('patients')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeView === 'patients'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Users className="h-4 w-4 inline mr-2" />
            Patient Management
          </button>
          <button
            onClick={async () => {
              setActiveView('stats');
              // Fetch data immediately when stats tab is clicked
              if (selectedClinicId) {
                await Promise.all([
                  getReactivationSummary(selectedClinicId),
                  getReactivationsByClinic(selectedClinicId)
                ]);
              }
            }}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeView === 'stats'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Phone className="h-4 w-4 inline mr-2" />
            Call Statistics
          </button>
        </nav>
      </div>

      {/* Content Sections */}
      {activeView === 'campaigns' && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-gray-900">{REACTIVATION_CAMPAIGNS_TITLE}</h3>
          </div>
          
          <div className="text-center py-16 text-gray-500">
            <div className="max-w-md mx-auto">
              <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <Plus className="h-8 w-8 text-gray-400" />
              </div>
              <h4 className="text-xl font-semibold text-gray-700 mb-2">{COMING_SOON_TITLE}</h4>
              <p className="text-gray-500">{COMING_SOON_DESCRIPTION}</p>
            </div>
          </div>
        </div>
      )}

      {activeView === "patients" && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-900">{PATIENT_MANAGEMENT_TITLE}</h3>
            </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <ClinicSelector
                selectedClinicId={selectedClinicId}
                onClinicSelect={handleClinicSelect}
              />

              <PatientList
                patients={patients}
                loading={loading}
                onPatientSelectionChange={handlePatientSelectionChange}
              />
            </div>

            <div className="space-y-6">
              <BatchCallSubmission
                selectedPatients={patients.filter(p => selectedPatientIds.includes(p.id))}
                onSubmit={handleBatchCallSubmit}
                loading={loading}
              />
            </div>
          </div>

          {showResults && batchCallResults && (
            <BatchCallResults
              results={batchCallResults}
              onClose={handleCloseResults}
            />
          )}
        </div>
      )}

      {activeView === "stats" && (
        // <></>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold text-gray-900">
              {STATISTICS_TITLE}
            </h3>
          </div>

          {selectedClinicId ? (
            <>
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">
                    {LOADING_STATISTICS}
                  </span>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center text-red-700">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    <span>
                      {ERROR_LOADING_STATISTICS} {error}
                    </span>
                  </div>
                </div>
              )}

              {!loading && !error && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <BarChart3 className="h-6 w-6 text-blue-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">
                              {STAT_TOTAL_CAMPAIGNS}
                            </p>
                            <p className="text-2xl font-bold text-gray-900">
                              {summary?.total_campaigns || 0}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-green-100 rounded-lg">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">
                              {STAT_SUCCESS_RATE}
                            </p>
                            <p className="text-2xl font-bold text-gray-900">
                              {summary?.success_rate || 0}%
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center">
                          <div className="p-2 bg-purple-100 rounded-lg">
                            <Users className="h-6 w-6 text-purple-600" />
                          </div>
                          <div className="ml-4">
                            <p className="text-sm font-medium text-gray-600">
                              {STAT_PATIENTS_CONTACTED}
                            </p>
                            <p className="text-2xl font-bold text-gray-900">
                              {summary?.total_patients_contacted || 0}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Detailed Batch Breakdown */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Campaign Status Breakdown */}
                    <Card>
                      <CardContent className="p-6">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          {CAMPAIGN_STATUS_BREAKDOWN_TITLE}
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_ACTIVE_CAMPAIGNS}
                            </span>
                            <span className="text-lg font-semibold text-blue-600">
                              {summary?.active_campaigns || 0}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_COMPLETED_CAMPAIGNS}
                            </span>
                            <span className="text-lg font-semibold text-green-600">
                              {summary?.completed_campaigns || 0}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_FAILED_CAMPAIGNS}
                            </span>
                            <span className="text-lg font-semibold text-red-600">
                              {summary?.failed_campaigns || 0}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Performance Metrics */}
                    <Card>
                      <CardContent className="p-6">
                        <h4 className="text-lg font-semibold text-gray-900 mb-4">
                          {PERFORMANCE_METRICS_TITLE}
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_PATIENTS_CONTACTED}
                            </span>
                            <span className="text-lg font-semibold text-purple-600">
                              {summary?.total_patients_contacted || 0}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_SUCCESS_RATE}
                            </span>
                            <span className="text-lg font-semibold text-green-600">
                              {summary?.success_rate || 0}%
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              {STAT_CAMPAIGN_EFFICIENCY}
                            </span>
                            <span className="text-lg font-semibold text-blue-600">
                              {summary && summary.total_campaigns > 0
                                ? Math.round(
                                    (summary.total_patients_contacted || 0) /
                                      summary.total_campaigns
                                  )
                                : 0}{" "}
                              {STAT_PATIENTS_PER_CAMPAIGN}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Reactivation Campaigns Data */}
                  <ReactivationStatsTable
                    reactivations={reactivations || []}
                    loading={loading}
                  />
                </>
              )}
            </>
          ) : (
            <div className="text-center py-12 text-gray-500">
              <Phone className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>{STATISTICS_SELECT_CLINIC}</p>
            </div>
          )}
        </div>
      )}


      <ClinicSelector
        selectedClinicId={selectedClinicId}
        onClinicSelect={handleClinicSelect}
      />
      <ReactivationStatsTable
        reactivations={reactivations || []}
        loading={loading}
      />

     
      <AddReactivationForm
        isOpen={isAddReactivationOpen}
        onClose={() => setIsAddReactivationOpen(false)}
        onSubmit={handleAddCampaign}
      />

      <div
        dangerouslySetInnerHTML={{
          __html:
            '<elevenlabs-convai agent-id="agent_01jybn5qtwfnd8twmvjffcb0h3"></elevenlabs-convai>',
        }}
      />
    </PageSection>
  );
};

export default ReactivationProgram;
