"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import ReactivationStatsGrid from "@/components/ReactivationProgram/ReactivationStatsGrid";
import ClinicSelector from "@/components/ReactivationProgram/ClinicSelector";

import {
  REACTIVATION_PROGRAM_TITLE,
  REACTIVATION_PROGRAM_SUBTITLE,
  REACTIVATION_PROGRAM_ADD_NEW,
  CAMPAIGNS_DATA,
} from "@/Constants/ReactivationProgram";

import PageSection from "@/components/CommonComponents/PageSection";
import { useAppointment } from "@/hooks/useAppointment";
import { useAICallLog } from "@/hooks/useAICallLog";
import AddReactivationForm from "@/components/ReactivationProgram/AddReactivationForm";
import ReactivationStatsTable from "@/components/ReactivationProgram/ReactivationStatsTable";
import { useReactivationManagement } from "@/hooks/useReactivationManagement";

const ReactivationProgram = () => {
  const [isAddReactivationOpen, setIsAddReactivationOpen] = useState(false);
  const [campaigns, setCampaigns] = useState(CAMPAIGNS_DATA);
  const [selectedClinicId, setSelectedClinicId] = useState<number | null>(null);

  const { aiCallLogs, getAllAICallLogs } = useAICallLog();

  const { appointments, getAllAppointments } = useAppointment();

  const { reactivations, loading, getReactivationsByClinic } =
    useReactivationManagement();

  type Campaign = (typeof CAMPAIGNS_DATA)[number];
  const handleAddCampaign = (data: Record<string, unknown>) => {
    setCampaigns([...campaigns, data as Campaign]);
  };

  const handleClinicSelect = async (clinicId: number) => {
    setSelectedClinicId(clinicId);
  };

  useEffect(() => {
    getAllAppointments();
  }, [getAllAppointments]);

  // Fetch reactivations when stats tab is active and clinic is selected
  useEffect(() => {
    if (selectedClinicId) {
      console.log("Fetching data for stats tab, clinic ID:", selectedClinicId);
      Promise.all([getReactivationsByClinic(selectedClinicId)]).catch(
        (error) => {
          console.error("Error fetching stats data:", error);
        }
      );
    }
  }, [selectedClinicId, getReactivationsByClinic]);

  useEffect(() => {
    getAllAICallLogs();
  }, [getAllAICallLogs]);
  const today = new Date().toISOString().split("T")[0]; // "2025-09-15" format

  const appointmentCount = Array.isArray(appointments)
    ? appointments.filter(
        (appointment) => appointment.appointment_date === today
      ).length
    : 0;

  return (
    <PageSection>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            {REACTIVATION_PROGRAM_TITLE}
          </h2>
          <p className="text-gray-600">{REACTIVATION_PROGRAM_SUBTITLE}</p>
        </div>
        <Button variant="main" onClick={() => setIsAddReactivationOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          {REACTIVATION_PROGRAM_ADD_NEW}
        </Button>
      </div>

      {/* Stats */}
      <ReactivationStatsGrid
        appointments={appointmentCount}
        calls={aiCallLogs?.length || 0}
        successRate="0%"
        newBookings={0}
      />

      <ClinicSelector
        selectedClinicId={selectedClinicId}
        onClinicSelect={handleClinicSelect}
      />
      <ReactivationStatsTable
        reactivations={reactivations || []}
        loading={loading}
      />
      <AddReactivationForm
        isOpen={isAddReactivationOpen}
        onClose={() => setIsAddReactivationOpen(false)}
        onSubmit={handleAddCampaign}
      />

      <div
        dangerouslySetInnerHTML={{
          __html:
            '<elevenlabs-convai agent-id="agent_01jybn5qtwfnd8twmvjffcb0h3"></elevenlabs-convai>',
        }}
      />
    </PageSection>
  );
};

export default ReactivationProgram;
