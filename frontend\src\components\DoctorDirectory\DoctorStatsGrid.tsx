// DoctorStatsGrid.tsx
// Renders a grid of StatCard components to display doctor-related statistics in the Doctor Directory.
// Shows total doctors, available doctors, total patients, and specialties.

import React, { useEffect, useState } from "react";
import StatCard from "@/components/CommonComponents/StatCard";
import { Stethoscope, Clock, Calendar, MapPin } from "lucide-react";
import {
  DOCTOR_DIRECTORY_STAT_TOTAL_DOCTORS,
  DOCTOR_DIRECTORY_STAT_AVAILABLE_NOW,
  DOCTOR_DIRECTORY_STAT_TOTAL_PATIENTS,
  DOCTOR_DIRECTORY_STAT_SPECIALTIES,
} from "@/Constants/DoctorDirectory";
import { useAppointment } from "@/hooks/useAppointment";
import { usePatients } from "@/hooks/usePatients";

// Use local Doctor type from doctor-directory page
export interface Doctor {
  id: number;
  doctor_name: string;
  specialization?: string;
  qualification?: string;
  clinic_id: number;
  doctor_phonenumber?: string;
  doctor_email?: string;
  experience_years?: number;
  is_deleted?: boolean;
  created_at?: string | number;
  consultationHours?: string;
  working_days?: string;
  patients?: number;
  [key: string]: unknown;
}

interface DoctorStatsGridProps {
  doctors: Doctor[];
  specialties: string[];
  getDoctorStatus: (doctor: Doctor) => string;
}

/**
 * Renders a grid of StatCard components for doctor statistics.
 * Used in the Doctor Directory page.
 */
const DoctorStatsGrid: React.FC<DoctorStatsGridProps> = ({
  doctors,
  specialties,
  getDoctorStatus,
}) => {
  const { appointments, getAllAppointments } = useAppointment();
  const { patients, getAllPatients } = usePatients();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await Promise.all([
        getAllAppointments(),
        getAllPatients(),
      ]);
      setLoading(false);
    };

    fetchData();
  }, [getAllAppointments, getAllPatients]);

  // Calculate patient count for each doctor from appointments
  const getAppointmentPatientCount = (doctorId: number) => {
    return appointments.filter(appointment => appointment.doctor_id === doctorId).length;
  };

  // Calculate patient count for each doctor from patients data
  const getAssignedPatientCount = (doctorId: number) => {
    return patients.filter(patient => 
      patient.doctors && patient.doctors.includes(doctorId)
    ).length;
  };

  // Calculate total patient count for all doctors
  const getTotalPatientCount = () => {
    return doctors.reduce((total, doctor) => {
      const appointmentPatients = getAppointmentPatientCount(doctor.id);
      const assignedPatients = getAssignedPatientCount(doctor.id);
      return total + appointmentPatients + assignedPatients;
    }, 0);
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
        {[...Array(4)].map((_, idx) => (
          <div key={idx} className="animate-pulse">
            <div className="h-24 bg-gray-200 rounded-lg"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
      <StatCard
        title={DOCTOR_DIRECTORY_STAT_TOTAL_DOCTORS}
        value={doctors.length}
        icon={<Stethoscope className="h-8 w-8 text-blue-600" />}
      />
      <StatCard
        title={DOCTOR_DIRECTORY_STAT_AVAILABLE_NOW}
        value={doctors.filter((d) => getDoctorStatus(d) === "Available").length}
        icon={<Clock className="h-8 w-8 text-green-600" />}
      />
      <StatCard
        title={DOCTOR_DIRECTORY_STAT_TOTAL_PATIENTS}
        value={getTotalPatientCount()}
        icon={<Calendar className="h-8 w-8 text-purple-600" />}
      />
      <StatCard
        title={DOCTOR_DIRECTORY_STAT_SPECIALTIES}
        value={specialties.length - 1}
        icon={<MapPin className="h-8 w-8 text-orange-600" />}
      />
    </div>
  );
};

export default DoctorStatsGrid;
