import { Reactivation, Clinic, User, sequelize } from '../models/index.js';
import logger from '../config/logger.config.js';
import { Op } from 'sequelize';

/**
 * Create a new reactivation batch record
 * @param {Object} reactivationData - Data for the reactivation batch
 * @returns {Promise<Object>} The created reactivation record
 */
export const createReactivation = async (reactivationData) => {
  try {
    const newReactivation = await Reactivation.create(reactivationData);
    return newReactivation;
  } catch (error) {
    logger.error('Error creating reactivation:', error);
    throw new Error(error.message);
  }
};

/**
 * Update a reactivation batch record
 * @param {number} id - Reactivation ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object|null>} The updated reactivation record
 */
export const updateReactivation = async (id, updateData) => {
  try {
    await Reactivation.update(updateData, { where: { id } });
    const updatedReactivation = await Reactivation.findByPk(id);
    return updatedReactivation;
  } catch (error) {
    logger.error(`Error updating reactivation ${id}:`, error);
    throw new Error(error.message);
  }
};

/**
 * Get a reactivation record by ID
 * @param {number} id - Reactivation ID
 * @returns {Promise<Object|null>} The reactivation record
 */
export const getReactivationById = async (id) => {
  try {
    const reactivation = await Reactivation.findByPk(id);
    return reactivation;
  } catch (error) {
    logger.error(`Error fetching reactivation ${id}:`, error);
    throw new Error(error.message);
  }
};

/**
 * Get all reactivations with optional filters
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of reactivation records
 */
export const getAllReactivations = async (filters = {}) => {
  try {
    const where = {};
    if (filters.clinic_id !== undefined) where.clinic_id = filters.clinic_id;
    if (filters.status !== undefined) where.status = filters.status;
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;

    const reactivations = await Reactivation.findAll({
      where,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Clinic,
          as: 'Clinic',
          attributes: ['id', 'clinic_name'],
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });
    return reactivations;
  } catch (error) {
    logger.error('Error fetching reactivations:', error);
    throw new Error(error.message);
  }
};

/**
 * Get reactivations by clinic ID
 * @param {number} clinicId - Clinic ID
 * @returns {Promise<Array>} List of reactivation records for the clinic
 */
export const getReactivationsByClinic = async (clinicId) => {
  try {
    const reactivations = await Reactivation.findAll({
      where: {
        clinic_id: clinicId,
        is_deleted: false,
        is_active: true,
      },
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Clinic,
          as: 'Clinic',
          attributes: ['id', 'clinic_name'],
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email'],
        },
      ],
    });
    return reactivations;
  } catch (error) {
    logger.error(`Error fetching reactivations for clinic ${clinicId}:`, error);
    throw new Error(error.message);
  }
};

/**
 * Update reactivation status
 * @param {number} id - Reactivation ID
 * @param {string} status - New status
 * @param {Object} additionalData - Additional data to update
 * @returns {Promise<Object|null>} The updated reactivation record
 */
export const updateReactivationStatus = async (id, status, additionalData = {}) => {
  try {
    const updateData = {
      status,
      ...additionalData,
    };

    // Set timestamps based on status
    if (status === 'in_progress') {
      updateData.executed_time = new Date();
    } else if (['completed', 'failed'].includes(status)) {
      updateData.completed_time = new Date();
    }

    const updatedReactivation = await updateReactivation(id, updateData);
    return updatedReactivation;
  } catch (error) {
    logger.error(`Error updating reactivation status ${id}:`, error);
    throw new Error(error.message);
  }
};



/**
 * Get reactivation statistics for a clinic
 * @param {number} clinicId - Clinic ID
 * @param {Object} dateRange - Date range for statistics
 * @returns {Promise<Object>} Reactivation statistics
 */
export const getReactivationStats = async (clinicId, dateRange = {}) => {
  try {
    const where = {
      clinic_id: clinicId,
      is_deleted: false,
      is_active: true,
    };

    if (dateRange.startDate) {
      where.created_at = { [Op.gte]: dateRange.startDate };
    }
    if (dateRange.endDate) {
      where.created_at = { ...where.created_at, [Op.lte]: dateRange.endDate };
    }

    const stats = await Reactivation.findAll({
      where,
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('reactivations.id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('reactivations.patient_count')), 'total_patients'],
      ],
      group: ['status'],
    });

    return stats;
  } catch (error) {
    logger.error(`Error fetching reactivation stats for clinic ${clinicId}:`, error);
    throw new Error(error.message);
      }
  };