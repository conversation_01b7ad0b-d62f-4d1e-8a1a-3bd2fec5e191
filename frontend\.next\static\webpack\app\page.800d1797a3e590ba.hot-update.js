"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/reactivation-program/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/reactivation-program/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Phone,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsGrid */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsGrid.tsx\");\n/* harmony import */ var _components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ReactivationProgram/ReactivationStatsTable */ \"(app-pages-browser)/./src/components/ReactivationProgram/ReactivationStatsTable.tsx\");\n/* harmony import */ var _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Constants/ReactivationProgram */ \"(app-pages-browser)/./src/Constants/ReactivationProgram.ts\");\n/* harmony import */ var _hooks_usePatientManagement__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/usePatientManagement */ \"(app-pages-browser)/./src/hooks/usePatientManagement.ts\");\n/* harmony import */ var _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useReactivationManagement */ \"(app-pages-browser)/./src/hooks/useReactivationManagement.ts\");\n/* harmony import */ var _components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/CommonComponents/PageSection */ \"(app-pages-browser)/./src/components/CommonComponents/PageSection.tsx\");\n/* harmony import */ var _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useAppointment */ \"(app-pages-browser)/./src/hooks/useAppointment.ts\");\n/* harmony import */ var _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useAICallLog */ \"(app-pages-browser)/./src/hooks/useAICallLog.ts\");\n/* harmony import */ var _components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ReactivationProgram/AddReactivationForm */ \"(app-pages-browser)/./src/components/ReactivationProgram/AddReactivationForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ReactivationProgram = ()=>{\n    _s();\n    const [isAddReactivationOpen, setIsAddReactivationOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGNS_DATA);\n    // Patient management state\n    const [selectedClinicId, setSelectedClinicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPatientIds, setSelectedPatientIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [batchCallResults, setBatchCallResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showResults, setShowResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // View state\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"campaigns\");\n    const { patients, loading, getPatientsByClinic, submitBatchCall, clearPatients } = (0,_hooks_usePatientManagement__WEBPACK_IMPORTED_MODULE_7__.usePatientManagement)();\n    const { aiCallLogs, getAllAICallLogs } = (0,_hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_11__.useAICallLog)();\n    const { appointments, getAllAppointments } = (0,_hooks_useAppointment__WEBPACK_IMPORTED_MODULE_10__.useAppointment)();\n    const { summary, reactivations, error, getReactivationSummary, getReactivationsByClinic } = (0,_hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_8__.useReactivationManagement)();\n    const handleAddCampaign = (data)=>{\n        setCampaigns([\n            ...campaigns,\n            data\n        ]);\n    };\n    // Handle clinic selection\n    const handleClinicSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleClinicSelect]\": async (clinicId)=>{\n            setSelectedClinicId(clinicId);\n            setSelectedPatientIds([]);\n            setBatchCallResults(null);\n            setShowResults(false);\n            // Fetch both patients and reactivation summary\n            const [patientsResult, summaryResult] = await Promise.all([\n                getPatientsByClinic(clinicId),\n                getReactivationSummary(clinicId)\n            ]);\n            if (!patientsResult.success) {\n                console.error(\"Failed to fetch patients:\", patientsResult.error);\n            }\n            if (!summaryResult.success) {\n                console.error(\"Failed to fetch reactivation summary:\", summaryResult.error);\n            }\n        }\n    }[\"ReactivationProgram.useCallback[handleClinicSelect]\"], [\n        getPatientsByClinic,\n        getReactivationSummary\n    ]);\n    // Handle patient selection change\n    const handlePatientSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handlePatientSelectionChange]\": (patientIds)=>{\n            setSelectedPatientIds(patientIds);\n        }\n    }[\"ReactivationProgram.useCallback[handlePatientSelectionChange]\"], []);\n    // Handle batch call submission\n    const handleBatchCallSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleBatchCallSubmit]\": async (time)=>{\n            if (!selectedClinicId || selectedPatientIds.length === 0) {\n                return;\n            }\n            const result = await submitBatchCall(selectedClinicId, selectedPatientIds, time);\n            if (result.success && result.data) {\n                setBatchCallResults(result.data);\n                setShowResults(true);\n            }\n        }\n    }[\"ReactivationProgram.useCallback[handleBatchCallSubmit]\"], [\n        selectedClinicId,\n        selectedPatientIds,\n        submitBatchCall\n    ]);\n    // Clear results and reset\n    const handleCloseResults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ReactivationProgram.useCallback[handleCloseResults]\": ()=>{\n            setShowResults(false);\n            setBatchCallResults(null);\n            setSelectedPatientIds([]);\n            clearPatients();\n        }\n    }[\"ReactivationProgram.useCallback[handleCloseResults]\"], [\n        clearPatients\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAppointments();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAppointments\n    ]);\n    // Fetch reactivations when stats tab is active and clinic is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Fetching data for stats tab, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error fetching stats data:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        activeView,\n        selectedClinicId,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            getAllAICallLogs();\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        getAllAICallLogs\n    ]);\n    const today = new Date().toISOString().split(\"T\")[0]; // \"2025-09-15\" format\n    const appointmentCount = Array.isArray(appointments) ? appointments.filter((appointment)=>appointment.appointment_date === today).length : 0;\n    // Fetch data when component mounts if clinic is already selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReactivationProgram.useEffect\": ()=>{\n            if (selectedClinicId) {\n                console.log(\"Initial data fetch for stats, clinic ID:\", selectedClinicId);\n                Promise.all([\n                    getReactivationSummary(selectedClinicId),\n                    getReactivationsByClinic(selectedClinicId)\n                ]).catch({\n                    \"ReactivationProgram.useEffect\": (error)=>{\n                        console.error(\"Error in initial stats fetch:\", error);\n                    }\n                }[\"ReactivationProgram.useEffect\"]);\n            }\n        }\n    }[\"ReactivationProgram.useEffect\"], [\n        selectedClinicId,\n        activeView,\n        getReactivationSummary,\n        getReactivationsByClinic\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CommonComponents_PageSection__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_TITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_SUBTITLE\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"main\",\n                        onClick: ()=>setIsAddReactivationOpen(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, undefined),\n                            _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_PROGRAM_ADD_NEW\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                appointments: appointmentCount,\n                calls: (aiCallLogs === null || aiCallLogs === void 0 ? void 0 : aiCallLogs.length) || 0,\n                successRate: \"\".concat((summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0, \"%\"),\n                newBookings: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"-mb-px flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"campaigns\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"campaigns\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Campaigns\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveView(\"patients\"),\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"patients\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Patient Management\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: async ()=>{\n                                setActiveView(\"stats\");\n                                // Fetch data immediately when stats tab is clicked\n                                if (selectedClinicId) {\n                                    await Promise.all([\n                                        getReactivationSummary(selectedClinicId),\n                                        getReactivationsByClinic(selectedClinicId)\n                                    ]);\n                                }\n                            },\n                            className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeView === \"stats\" ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4 inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Call Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"campaigns\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.REACTIVATION_CAMPAIGNS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_TITLE\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.COMING_SOON_DESCRIPTION\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"patients\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PATIENT_MANAGEMENT_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-xl font-semibold text-gray-700 mb-2\",\n                                    children: \"Patient Management\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: 'Patient management functionality has been moved to the Add Reactivation Program form. Click the \"Add New Program\" button to access patient management features.'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, undefined),\n            activeView === \"stats\" && // <></>\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_TITLE\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedClinicId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600\",\n                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.LOADING_STATISTICS\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 17\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-red-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.ERROR_LOADING_STATISTICS,\n                                                \" \",\n                                                error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 17\n                            }, undefined),\n                            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-blue-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-blue-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_TOTAL_CAMPAIGNS\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_campaigns) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-green-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: [\n                                                                            (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 bg-purple-100 rounded-lg\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-gray-600\",\n                                                                        children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                                        children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.CAMPAIGN_STATUS_BREAKDOWN_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_ACTIVE_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.active_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_COMPLETED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.completed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_FAILED_CAMPAIGNS\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-red-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.failed_campaigns) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 436,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.PERFORMANCE_METRICS_TITLE\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_CONTACTED\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-purple-600\",\n                                                                            children: (summary === null || summary === void 0 ? void 0 : summary.total_patients_contacted) || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_SUCCESS_RATE\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-green-600\",\n                                                                            children: [\n                                                                                (summary === null || summary === void 0 ? void 0 : summary.success_rate) || 0,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_CAMPAIGN_EFFICIENCY\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-semibold text-blue-600\",\n                                                                            children: [\n                                                                                summary && summary.total_campaigns > 0 ? Math.round((summary.total_patients_contacted || 0) / summary.total_campaigns) : 0,\n                                                                                \" \",\n                                                                                _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STAT_PATIENTS_PER_CAMPAIGN\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        reactivations: reactivations || [],\n                                        loading: loading\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Phone_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: _Constants_ReactivationProgram__WEBPACK_IMPORTED_MODULE_6__.STATISTICS_SELECT_CLINIC\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ClinicSelector, {\n                selectedClinicId: selectedClinicId,\n                onClinicSelect: handleClinicSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_ReactivationStatsTable__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                reactivations: reactivations || [],\n                loading: loading\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReactivationProgram_AddReactivationForm__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isAddReactivationOpen,\n                onClose: ()=>setIsAddReactivationOpen(false),\n                onSubmit: handleAddCampaign\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dangerouslySetInnerHTML: {\n                    __html: '<elevenlabs-convai agent-id=\"agent_01jybn5qtwfnd8twmvjffcb0h3\"></elevenlabs-convai>'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Densy-ai\\\\densy-ai\\\\Clinic-Appointment-AI-Agent\\\\frontend\\\\src\\\\app\\\\reactivation-program\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ReactivationProgram, \"0iB/swUpPT6dmeolmNEyk+ktbbE=\", false, function() {\n    return [\n        _hooks_usePatientManagement__WEBPACK_IMPORTED_MODULE_7__.usePatientManagement,\n        _hooks_useAICallLog__WEBPACK_IMPORTED_MODULE_11__.useAICallLog,\n        _hooks_useAppointment__WEBPACK_IMPORTED_MODULE_10__.useAppointment,\n        _hooks_useReactivationManagement__WEBPACK_IMPORTED_MODULE_8__.useReactivationManagement\n    ];\n});\n_c = ReactivationProgram;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactivationProgram);\nvar _c;\n$RefreshReg$(_c, \"ReactivationProgram\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reactivation-program/page.tsx\n"));

/***/ })

});