import { body } from 'express-validator';
import * as constants from '../utils/constants.utils.js';
import { PASSWORD_REGEX } from '../utils/pattern.utils.js';

const ALLOWED_ROLES = ['super_admin', 'clinic_admin', 'front_desk', 'agent'];

/**
 * Validation schema for creating a user
 * <AUTHOR>
 */
export const signUpSchema = [
  // Validate name
  body('name').notEmpty().withMessage(constants.NAME_REQUIRED),

  // Validate email
  body('email')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isEmail()
    .withMessage(constants.INVALID_EMAIL),

  // Validate phone number
  body('user_phonenumber')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isMobilePhone()
    .withMessage(constants.INVALID_PHONE_NUMBER),

  // Validate password
  body('password')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(constants.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(constants.INVALID_PASSWORD_FORMAT),

  // Validate role
  body('role')
    .notEmpty()
    .withMessage(constants.ROLE_REQUIRED)
    .isIn(ALLOWED_ROLES)
    .withMessage(constants.INVALID_ROLE),
];

/**
 * Validation schema for logging in a user
 * <AUTHOR>
 */
export const loginSchema = [
  // Validate email
  body('email')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isEmail()
    .withMessage(constants.INVALID_EMAIL),

  // Validate password
  body('password').notEmpty().withMessage(constants.FIELD_REQUIRED),
];

/**
 * Validation schema for resetting password via token
 * <AUTHOR>
 */
export const resetPasswordSchema = [
  // Validate new password
  body('newPassword')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(constants.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(constants.INVALID_PASSWORD_FORMAT),

  // Validate confirm password
  body('confirmPassword')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error(constants.PASSWORD_CONFIRM_PASSWORD_MISMATCH);
      }
      return true;
    }),
];

/**
 * Validation schema for changing password (authenticated user)
 * <AUTHOR>
 */
export const changePasswordSchema = [
  // Validate current password
  body('currentPassword').notEmpty().withMessage(constants.FIELD_REQUIRED),

  // Validate new password
  body('newPassword')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(constants.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(constants.INVALID_PASSWORD_FORMAT),

  // Validate confirm password
  body('confirmPassword')
    .notEmpty()
    .withMessage(constants.FIELD_REQUIRED)
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error(constants.PASSWORD_CONFIRM_PASSWORD_MISMATCH);
      }
      return true;
    }),
];
