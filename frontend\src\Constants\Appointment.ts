export const APPOINTMENT_FILTER_DATES = [
  { value: "today", label: "Today" },
  { value: "tomorrow", label: "Tomorrow" },
];

// Appointment Form Constants
export const APPOINTMENT_FORM_FIELDS = {
  PATIENT: "Patient",
  DOCTOR: "Doctor",
  CLINIC: "Clinic",
  DATE: "Appointment Date",
  TIME: "Appointment Time",
  STATUS: "Status",
  SOURCE: "Booked Via",
  NOTES: "Notes",
};

export const APPOINTMENT_FORM_PLACEHOLDERS = {
  PATIENT: "Select a patient",
  DOCTOR: "Select a doctor",
  CLINIC: "Select a clinic",
  DATE: "Select appointment date",
  TIME: "Select appointment time",
  NOTES: "Enter appointment notes (optional)",
};

export const APPOINTMENT_STATUS_OPTIONS = [
  { value: "booked", label: "Booked" },
  { value: "cancelled", label: "Cancelled" },
  { value: "rescheduled", label: "Rescheduled" },
  { value: "no-show", label: "No Show" },
];

export const APPOINTMENT_SOURCE_OPTIONS = [
  { value: "ai-agent", label: "AI Agent" },
  { value: "user", label: "User" },
  { value: "manual", label: "Manual" },
];

export const APPOINTMENT_FORM_DEFAULTS = {
  STATUS: "booked",
  SOURCE: "manual",
};

// Form titles and buttons
export const SCHEDULE_APPOINTMENT_TITLE = "Schedule New Appointment";
export const SCHEDULE_APPOINTMENT_SUBMIT = "Schedule Appointment";
export const SCHEDULE_APPOINTMENT_CANCEL = "Cancel";

export const getAppointmentStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case "booked":
      return "bg-green-100 text-green-800 border-green-200";
    case "cancelled":
      return "bg-red-100 text-red-800 border-red-200";
    case "rescheduled":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "no-show":
      return "bg-orange-100 text-orange-800 border-orange-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

export const getAppointmentTypeColor = (type: string) => {
  switch (type?.toLowerCase()) {
    case "ai-agent":
      return "bg-purple-100 text-purple-800 border-purple-200";
    case "user":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "manual":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

// Function to capitalize first letter of each word
export const capitalizeFirstLetter = (text: string) => {
  if (!text) return "";
  return text
    .split(" ")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
};

export const UNKNOWN_PATIENT = "Unknown Patient";
export const UNASSIGNED_DOCTOR = "Unassigned";
export const NA_VALUE = "N/A";

export const PLACEHOLDER_SEARCH_PATIENT = "Search patient...";
export const PLACEHOLDER_ALL_DOCTORS = "All Doctors";
export const LABEL_FILTERS = "Filters:";
export const LABEL_PATIENT = "Patient";
export const LABEL_DOCTOR = "Doctor";
export const LABEL_DATE = "Date";
export const LABEL_TIME = "Time";
export const LABEL_STATUS = "Status";
export const LABEL_BOOKED_VIA = "Booked Via";
export const LABEL_PATIENT_PHONE = "Patient Phone";
export const LABEL_PATIENT_EMAIL = "Patient Email";
export const LABEL_CLINIC = "Clinic";
export const LABEL_NOTES = "Notes";
export const PLACEHOLDER_SEARCH = "Search...";
export const LABEL_VIEW_DETAILS = "View details for";
export const LABEL_APPOINTMENT_DETAILS = "Appointment Details";
export const LABEL_APPOINTMENT_DETAILS_DESC = "Full details for the selected appointment.";
export const LABEL_NO_NOTES = "No notes for this appointment.";

export const TITLE_APPOINTMENT_MANAGEMENT = "Appointment Management";
export const BUTTON_SCHEDULE_NEW = "Schedule New";
export const TITLE_TODAYS_APPOINTMENTS = "Total Appointments";
export const BUTTON_RETRY = "Retry";
export const ERROR_FAILED_TO_LOAD_APPOINTMENTS = "Failed to load appointments.";

// API Messages
export const MESSAGES = {
  APPOINTMENT_FETCH_SUCCESS: "Appointments fetched successfully",
  APPOINTMENT_FETCH_FAILED: "Failed to fetch appointments",
  APPOINTMENT_CREATE_SUCCESS: "Appointment created successfully",
  APPOINTMENT_CREATE_FAILED: "Failed to create appointment",
  APPOINTMENT_UPDATE_SUCCESS: "Appointment updated successfully",
  APPOINTMENT_UPDATE_FAILED: "Failed to update appointment",
  APPOINTMENT_DELETE_SUCCESS: "Appointment deleted successfully",
  APPOINTMENT_DELETE_FAILED: "Failed to delete appointment",
  APPOINTMENT_NOT_FOUND: "Appointment not found",
};
