/**
 * Webhook Validator: Handles validation for webhook operations.
 * <AUTHOR>
 */
import <PERSON><PERSON> from 'joi';

/**
 * Validation schema for ElevenLabs webhook payload
 */
export const elevenLabsWebhookSchema = Joi.object({
  data: Joi.object({
    conversation_id: Joi.string().required().description('ElevenLabs conversation ID'),
    // Add other fields as needed based on ElevenLabs webhook structure
  }).required(),
  // Add other top-level fields as needed
}).unknown(true); // Allow additional fields

/**
 * Validate ElevenLabs webhook payload
 * @param {Object} payload - Webhook payload to validate
 * @returns {Object} Validation result
 */
export const validateElevenLabsWebhook = (payload) => {
  return elevenLabsWebhookSchema.validate(payload, { abortEarly: false });
};
