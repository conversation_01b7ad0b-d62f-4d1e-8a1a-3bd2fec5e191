// SearchBar.tsx
// Renders a search input with an icon, used for filtering/searching lists or tables.

import React from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { PLACEHOLDER_SEARCH } from '@/Constants/CommonComponents';

/**
 * Props for the SearchBar component
 * @property placeholder - Placeholder text for the input
 * @property value - Current value of the search input
 * @property onChange - Handler for input changes
 * @property className - Optional additional class names
 * @property icon - Optional custom icon to display
 */
interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  icon?: React.ReactNode;
}

/**
 * Renders a search bar with an icon and input field.
 * Used for filtering/searching in lists or tables.
 */
const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = PLACEHOLDER_SEARCH,
  value,
  onChange,
  className = '',
  icon,
}) => (
  <div className={`relative ${className}`}>
    {icon !== undefined ? (
      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4">
        {icon}
      </span>
    ) : (
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-[18px] w-[18px]" />
    )}
    <Input
      placeholder={placeholder}
      value={value}
      onChange={onChange}
      className="pl-10 h-10 bg-white rounded-xl shadow border border-gray-200 text-[15px]"
    />
  </div>
);

export default SearchBar; 