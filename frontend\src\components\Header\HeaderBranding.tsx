import React from "react";
import {
    HEADER_BRANDING,
  CSS_CLASSES,
  TEXT_STYLES,
} from "@/Constants/Header";

interface HeaderBrandingProps {
  className?: string;
}

const HeaderBranding: React.FC<HeaderBrandingProps> = ({ className = "" }) => {
  return (
    <div className={`${CSS_CLASSES.BRANDING_CONTAINER} ${className}`}>
      <h3 className={TEXT_STYLES.CLINIC_NAME}>
        {HEADER_BRANDING.CLINIC_NAME}
      </h3>
      <span className={TEXT_STYLES.CLINIC_SUBTITLE}>
        {HEADER_BRANDING.CLINIC_SUBTITLE}
      </span>
    </div>
  );
};

export default HeaderBranding; 