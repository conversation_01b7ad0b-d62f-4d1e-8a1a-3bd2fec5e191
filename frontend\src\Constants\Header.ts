// Header Component Constants

export const HEADER_CONSTANTS = {
  // User Profile Dropdown
  PROFILE_DROPDOWN: {
    LOADING_TEXT: "Loading...",
    MY_PROFILE: "My Profile",
    CHANGE_PASSWORD: "Change Password",
    LOGOUT: "Logout",
    VIEW_PROFILE_TITLE: "View Profile",
    CHANGE_PASSWORD_TITLE: "Change Password",
    LOGOUT_TITLE: "Logout",
  },

  // Header Actions
  HEADER_ACTIONS: {
    SETTINGS_TITLE: "Settings",
    SEARCH_PLACEHOLDER: "Search...",
    NOTIFICATIONS_TITLE: "Notifications",
  },

  // Header Branding
  HEADER_BRANDING: {
    CLINIC_NAME: "Densy AI",
    CLINIC_SUBTITLE: "Clinic Management System",
  },

  // Search Bar
  SEARCH_BAR: {
    PLACEHOLDER: "Search...",
    OPEN_SEARCH_LABEL: "Open search",
  },

  // Notification Button
  NOTIFICATION_BUTTON: {
    NOTIFICATIONS_LABEL: "Notifications",
  },

  // Error Messages
  ERROR_MESSAGES: {
    LOGOUT_FAILED: "Logout failed:",
  },

  // Navigation Routes
  ROUTES: {
    PROFILE: "/profile",
    CHANGE_PASSWORD: "/change-password",
    LOGIN: "/login",
    SETTINGS: "/settings",
  },

  // Accessibility
  ACCESSIBILITY: {
    PROFILE_DROPDOWN_TRIGGER: "User profile dropdown",
    SETTINGS_BUTTON: "Settings",
    SEARCH_BUTTON: "Search",
    NOTIFICATION_BUTTON: "Notifications",
  },

  // CSS Classes
  CSS_CLASSES: {
    // Profile Dropdown
    PROFILE_BUTTON: "flex items-center gap-3 px-4 py-2.5 rounded-xl hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300 hover:shadow-sm",
    DROPDOWN_CONTENT: "w-64 mt-3 z-50 bg-white border border-gray-200 shadow-xl rounded-xl overflow-hidden",
    DROPDOWN_HEADER: "px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100",
    DROPDOWN_ITEM: "cursor-pointer px-4 py-3 hover:bg-gray-50 focus:bg-gray-50 transition-colors duration-150",
    DROPDOWN_ITEM_LOGOUT: "text-red-600 focus:text-red-700 cursor-pointer px-4 py-3 hover:bg-red-50 focus:bg-red-50 transition-colors duration-150",
    DROPDOWN_SEPARATOR: "bg-gray-100 my-1",
    
    // Header Actions
    HEADER_ACTIONS_CONTAINER: "flex items-center gap-3",
    SEARCH_NOTIFICATION_CONTAINER: "relative flex items-center gap-3",
    DIVIDER: "w-px h-6 bg-gray-200 mx-1",
    SETTINGS_BUTTON: "p-2.5 rounded-xl hover:bg-gray-50 transition-all duration-200 border border-gray-200 hover:border-gray-300 hover:shadow-sm",
    
    // Header Branding
    BRANDING_CONTAINER: "flex items-center space-x-4",
    CLINIC_NAME: "text-2xl font-bold text-blue-600",
    CLINIC_SUBTITLE: "text-gray-500 text-sm",
    
    // Search Bar
    SEARCH_CONTAINER: "relative",
    SEARCH_INPUT_CONTAINER: "flex items-center transition-all duration-300 ease-in-out bg-white rounded-[8px]",
    SEARCH_BUTTON: "flex items-center justify-center h-10 w-10 p-0 bg-transparent border-none outline-none",
    SEARCH_INPUT: "transition-all duration-300 ease-in-out bg-transparent border-none focus:ring-0 focus-visible:ring-0 px-0",
    
    // Notification Button
    NOTIFICATION_BUTTON: "absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full",
  },

  // Icons
  ICONS: {
    USER: "h-4 w-4 mr-3 text-gray-600",
    LOCK: "h-4 w-4 mr-3 text-gray-600",
    LOGOUT: "h-4 w-4 mr-3",
    CHEVRON_DOWN: "h-4 w-4 text-gray-400 ml-1",
    SETTINGS: "h-5 w-5 text-gray-600",
    SEARCH: "h-5 w-5 text-gray-500",
    BELL: "h-5 w-5 text-gray-500",
    AVATAR_FALLBACK: "bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold text-sm",
  },

  // Avatar
  AVATAR: {
    SIZE: "h-9 w-9 ring-2 ring-gray-100",
  },

  // Text Styles
  TEXT_STYLES: {
    USER_NAME: "text-sm font-semibold text-gray-900",
    USER_EMAIL: "text-xs text-gray-600 mt-0.5",
    DROPDOWN_ITEM_TEXT: "font-medium",
    CLINIC_NAME: "text-2xl font-bold text-blue-600",
    CLINIC_SUBTITLE: "text-gray-500 text-sm",
  },
};

// Individual exports for easier access
export const {
  PROFILE_DROPDOWN,
  HEADER_ACTIONS,
  HEADER_BRANDING,
  SEARCH_BAR,
  NOTIFICATION_BUTTON,
  ERROR_MESSAGES,
  ROUTES,
  ACCESSIBILITY,
  CSS_CLASSES,
  ICONS,
  AVATAR,
  TEXT_STYLES,
} = HEADER_CONSTANTS; 