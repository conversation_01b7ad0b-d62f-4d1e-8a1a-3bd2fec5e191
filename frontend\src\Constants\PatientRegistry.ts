// Patient form field names
export const FIELD_CLINIC_ID = "clinic_id";
export const FIELD_DOCTOR_IDS = "doctor_ids";
export const FIELD_TAGS = "tags";

// Patient form field labels
export const LABEL_CLINIC = "Clinic";
export const LABEL_DOCTORS = "Doctors";
export const LABEL_TAGS = "Tags";

// Select placeholders
export const SELECT_PLACEHOLDER_PREFIX = "Select ";
export const NO_DOCTORS_FOR_CLINIC = "No doctors available for selected clinic";
export const ADD_PATIENT_TITLE = "Add Patient";
export const EDIT_PATIENT_TITLE = "Edit Patient";
export const ADD_PATIENT_SUBMIT = "Add Patient";
export const EDIT_PATIENT_SUBMIT = "Update Patient";
export const SELECT_CLINIC_PLACEHOLDER = "Select Clinic";
export const SELECT_DOCTOR_PLACEHOLDER = "Select Doctor";
export const TAGS_PLACEHOLDER =
  "Enter tags separated by commas (e.g., urgent, follow-up, chronic)";
export const PLEASE_SELECT_CLINIC_FIRST = "Please select a clinic first";
export const PLEASE_SELECT_AT_LEAST_ONE_DOCTOR =
  "Please select at least one doctor.";
export const FILTER_STATUS_ACTIVE = "active";
export const FILTER_STATUS_INACTIVE = "inactive";
export const PATIENT_DETAILS_TITLE = "Patient Details";
export const DELETE_PATIENT_TITLE = "Delete Patient";
export const DELETE_PATIENT_CONFIRM = "Delete";
export const DELETE_PATIENT_CANCEL = "Cancel";
export const DELETE_PATIENT_DESCRIPTION =
  "Are you sure you want to delete {name}? This action cannot be undone.";
export const DELETE_PATIENT_DESCRIPTION_START =
  "Are you sure you want to delete ";
export const DELETE_PATIENT_DESCRIPTION_END = "? This action cannot be undone.";
export const NA = "N/A";
export const PATIENT_RECORDS_SUFFIX = "Records";
export const FILTER_ALL = "all";
export const BUTTON_RETRY = "Retry";
export const TITLE_PATIENT_REGISTRY = "Patient Registry";
export const SUBTITLE_PATIENT_REGISTRY =
  "Manage patient records and demographics";
export const BUTTON_ADD_NEW_PATIENT = "Add New Patient";
export const PLACEHOLDER_SEARCH_PATIENT_MRN = "Search by name or MRN...";
export const PLACEHOLDER_SEARCH_PATIENT = "Search by name...";
export const PLACEHOLDER_FILTER_BY_STATUS = "Filter by Status";
export const PLACEHOLDER_FILTER_BY_CLINIC = "Filter by Clinic";
export const BUTTON_ALL = "All";
export const BUTTON_ACTIVE = "Active";
export const BUTTON_INACTIVE = "Inactive";
export const LABEL_TOTAL_PATIENTS = "Total Patients";
export const LABEL_ACTIVE_PATIENTS = "Active Patients";
export const LABEL_NEW_THIS_MONTH = "New This Month";
export const LABEL_REACTIVATION_DUE = "Reactivation Due";
export const TABLE_HEADER_PATIENT = "Patient";
export const TABLE_HEADER_CONTACT = "Contact";
export const TABLE_HEADER_EMAIL = "Email";
export const TABLE_HEADER_LAST_VISIT = "Last Visit";
export const TABLE_HEADER_DOB = "DOB";
export const TABLE_HEADER_PREFERRED_SLOT = "Preferred Slot";
export const TABLE_HEADER_STATUS = "Status";
export const TABLE_HEADER_CREATED_AT = "Created At";
export const TABLE_HEADER_CLINIC = "Clinic";
export const TABLE_HEADER_ACTIONS = "Actions";
export const ERROR_FAILED_TO_LOAD_PATIENTS = "Failed to load patients";
export const LOADING_PATIENTS = "Loading patients...";
export const LABEL_ANY_TIME = "Any Time";

// Calling functionality constants
export const CALL_PATIENT_TITLE = "Call Patient";
export const CALL_INITIATED_SUCCESS = "Call initiated successfully";
export const CALL_INITIATED_FAILED = "Failed to initiate call";
export const NO_PHONE_NUMBER = "No phone number available for this patient";
export const CALL_ERROR = "Error initiating call";

export const DIALOG_CALL_PATIENT = "Are you sure to call this patient?";
export const DIALOG_CALL_PATIENT_TITLE = "Call Patient";
export const BUTTON_TYPE = {
  PRIMARY: "primary",
  SECONDARY: "secondary",
  DESTRUCTIVE: "destructive",
};
export const BUTTON_TEXT = {
  CONFIRM: "Confirm",
  CANCEL: "Cancel",
  DELETE: "Delete",
};

export const BUTTON_DISABLE_TIMEOUT = 5 * 60 * 1000;

export const PATIENT_FORM_FIELDS = {
  FIRST_NAME: "First Name",
  LAST_NAME: "Last Name",
  FULL_NAME: "Full Name",
  AGE: "Age",
  GENDER: "Gender",
  PHONE: "Phone",
  EMAIL: "Email",
  CLINIC: "Clinic",
  DOCTOR: "Doctor",
  DOB: "Date of Birth",
  ADDRESS: "Address",
  PRIMARY_DOCTOR: "Primary Doctor",
  LAST_VISIT_SUMMARY: "Last Visit Summary",
};

export const PATIENT_FORM_PLACEHOLDERS = {
  PHONE: "Enter the Number with Country Code",
  ADDRESS: "123 Main St, Anytown, USA 12345",
  LAST_VISIT_SUMMARY: "Enter details about the patient's last visit...",
};

export const PATIENT_FORM_OPTIONS = {
  GENDER: [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ],
  PRIMARY_DOCTORS: [
    { value: "Dr. Sarah Johnson", label: "Dr. Sarah Johnson" },
    { value: "Dr. Michael Brown", label: "Dr. Michael Brown" },
    { value: "Dr. Emily Davis", label: "Dr. Emily Davis" },
    { value: "Dr. James Wilson", label: "Dr. James Wilson" },
  ],
  CLINICS: [
    { value: "Clinic A", label: "Clinic A" },
    { value: "Clinic B", label: "Clinic B" },
    { value: "Clinic C", label: "Clinic C" },
  ],
  DOCTORS: [
    { value: "Dr. Sarah Johnson", label: "Dr. Sarah Johnson" },
    { value: "Dr. Michael Brown", label: "Dr. Michael Brown" },
    { value: "Dr. Emily Davis", label: "Dr. Emily Davis" },
    { value: "Dr. James Wilson", label: "Dr. James Wilson" },
  ],
};

export const MESSAGES = {
  PATIENT_ADDED_SUCCESSFULLY: "Patient added successfully",
  PATIENT_ADD_FAILED: "Failed to add patient",
  PATIENT_UPDATED_SUCCESSFULLY: "Patient updated successfully",
  PATIENT_UPDATE_FAILED: "Failed to update patient",
  PATIENT_DELETED_SUCCESSFULLY: "Patient deleted successfully",
  PATIENT_DELETE_FAILED: "Failed to delete patient",
  PATIENT_UPLOAD_SUCCESSFULLY: "Patient uploaded successfully",
  PATIENT_UPLOAD_FAILED: "Failed to upload patient list",
  PATIENT_FETCHED_SUCCESSFULLY: "Patient fetched successfully",
  PATIENT_FETCH_FAILED: "Failed to fetch patient(s)",
};

export const FILE_UPLOAD_HEADER = {
  headers: { "Content-Type": "multipart/form-data" },
};
