import bcrypt from 'bcryptjs';
import logger from '../config/logger.config.js';
import * as userService from '../services/user.service.js';
import * as emailService from '../services/email.service.js';
import * as constants from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import * as authService from '../services/auth.service.js';
import * as jwtUtil from '../utils/jwt.util.js';
import * as tokenUtil from '../utils/token.util.js';
import { logUserAudit } from '../services/auditLog.service.js';
import {
  AuditActions,
  UserAuditDescriptions,
} from '../utils/auditlog_messages.utils.js';

// Constants
const REFRESH_TOKEN_REQUIRED = constants.REFRESH_TOKEN_REQUIRED;
const INVALID_OR_EXPIRED_REFRESH_TOKEN =
  constants.INVALID_OR_EXPIRED_REFRESH_TOKEN;
const REFRESH_TOKEN_COOKIE_NAME = constants.REFRESH_TOKEN_COOKIE_NAME;
const ACCESS_TOKEN_COOKIE_NAME = constants.ACCESS_TOKEN_COOKIE_NAME;
const COOKIE_SAME_SITE = constants.COOKIE_SAME_SITE;
const REFRESH_TOKEN_EXPIRY_MS = constants.REFRESH_TOKEN_EXPIRY_MS;
const ACCESS_TOKEN_EXPIRY_MS = constants.ACCESS_TOKEN_EXPIRY_MS;

// Helper functions
/**
 * Set authentication tokens in response cookies
 * @param {Object} res - Express response object
 * @param {Object} tokens - Object containing accessToken and refreshToken
 */
const sendAuthTokens = (res, { accessToken, refreshToken }) => {
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV,
    sameSite: COOKIE_SAME_SITE,
  };

  res.cookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
    ...cookieOptions,
    maxAge: REFRESH_TOKEN_EXPIRY_MS,
  });

  res.cookie(ACCESS_TOKEN_COOKIE_NAME, accessToken, {
    ...cookieOptions,
    maxAge: ACCESS_TOKEN_EXPIRY_MS,
  });
};

/**
 * Clear authentication cookies from response
 * @param {Object} res - Express response object
 */
const clearAuthCookies = (res) => {
  res.clearCookie(REFRESH_TOKEN_COOKIE_NAME);
  res.clearCookie(ACCESS_TOKEN_COOKIE_NAME);
};

/**
 * Create JWT tokens for user
 * @param {Object} userData - User data for token creation
 * @returns {Object} Object containing accessToken and refreshToken
 */
const createUserTokens = async (userData) => {
  const accessToken = jwtUtil.createAccessToken({
    id: userData.id,
    email: userData.email,
    role: userData.role_id,
  });

  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
  const refreshTokenDoc = await tokenUtil.createRefreshToken({
    userId: userData.id,
    expiresAt,
  });

  return {
    accessToken,
    refreshToken: refreshTokenDoc.refreshToken,
  };
};

/**
 * Validate user credentials
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Object|null} User object if valid, null otherwise
 */
const validateUserCredentials = async (email, password) => {
  const user = await authService.checkUserExists({ email });

  if (!user) {
    return null;
  }

  const isPasswordValid = await authService.validatePassword(
    password,
    user.password_hash
  );

  return isPasswordValid ? user : null;
};

/**
 * Signup a new user
 * @route POST /api/auth/signup
 */
export const signUp = async (req, res) => {
  try {
    logger.info(loggerMessages.SIGNING_USER);

    const { role, ...userData } = req.body;
    userData.email = userData.email.toLowerCase()

    // Validate role
    const roleRecord = await authService.findRoleByName(role);
    if (!roleRecord) {
      logger.warn(loggerMessages.INVALID_ROLE);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.INVALID_ROLE));
    }

    // Check if user already exists
    const existingUser = await authService.checkUserExists({
      email: userData.email,
      user_phonenumber: userData.user_phonenumber,
    });

    if (existingUser) {
      logger.warn(loggerMessages.USER_ALREADY_EXISTS_LOG_MESSAGE);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_ALREADY_EXISTS_ERROR));
    }

    // Prepare user data
    const hashedPassword = await bcrypt.hash(userData.password, 8);
    const preparedUserData = {
      ...userData,
      role_id: roleRecord.id,
      password_hash: hashedPassword,
      created_at: new Date(),
      updated_at: new Date(),
      is_deleted: false,
      is_active: true,
    };

    delete preparedUserData.password;

    // Create user
    const newUser = await authService.createUser(preparedUserData);

    // Create tokens
    const tokens = await createUserTokens(newUser);

    // Log audit
    await logUserAudit({
      action: AuditActions.CREATE,
      record_id: newUser.id,
      user_id: newUser.id,
      new_value: JSON.stringify(newUser),
      description: UserAuditDescriptions.USER_CREATED_SIGNUP,
    });

    // Send response
    sendAuthTokens(res, tokens);
    res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.SIGNUP_SUCCESSFULLY, {
        newUser,
        ...tokens,
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_SIGNING_UP_USER, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(constants.SIGNUP_FAILED_PREFIX + error.message));
  }
};

/**
 * Login a user
 * @route POST /api/auth/login
 */
export const login = async (req, res) => {
  try {
    logger.info(loggerMessages.LOGGING_USER);

    const { email, password } = req.body;
    let user_email = email.toLowerCase()
    // Validate credentials
    const user = await validateUserCredentials(user_email, password);

    if (!user) {
      logger.warn(loggerMessages.INVALID_CREDENTIALS)

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_DOES_NOT_EXIST));
    }

    // Create tokens
    const tokens = await createUserTokens(user);

    // Log successful login
    await logUserAudit({
      action: AuditActions.LOGIN,
      record_id: user.id,
      user_id: user.id,
      description: UserAuditDescriptions.USER_LOGGED_IN,
    });

    // Send response
    sendAuthTokens(res, tokens);
    res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.LOGIN_SUCCESSFULLY, {
        user,
        ...tokens,
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_LOGGING_IN_USER, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(constants.INTERNAL_SERVER_ERROR));
  }
};

/**
 * Logout a user
 * @route POST /api/auth/logout
 */
export const logout = async (req, res) => {
  try {
    const refreshToken =
      req.cookies[REFRESH_TOKEN_COOKIE_NAME] || req.body.refreshToken;

    if (!refreshToken) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(REFRESH_TOKEN_REQUIRED));
    }

    await tokenUtil.revokeRefreshToken(refreshToken);
    clearAuthCookies(res);

    res.json(successResponse(constants.LOGGED_OUT_SUCCESSFULLY));
  } catch (error) {
    logger.error(loggerMessages.ERROR_LOGGING_OUT_USER, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(constants.INTERNAL_SERVER_ERROR));
  }
};

/**
 * Refresh access token using refresh token
 * @route POST /api/auth/refresh
 */
export const refreshToken = async (req, res) => {
  try {
    const oldRefreshToken =
      req.cookies[REFRESH_TOKEN_COOKIE_NAME] || req.body.refreshToken;

    if (!oldRefreshToken) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(REFRESH_TOKEN_REQUIRED));
    }

    // Validate refresh token
    const tokenDoc = await tokenUtil.findValidRefreshToken(oldRefreshToken);
    if (!tokenDoc) {
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(INVALID_OR_EXPIRED_REFRESH_TOKEN));
    }

    // Revoke old token and create new one
    await tokenUtil.revokeRefreshToken(oldRefreshToken);
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    const newRefreshTokenDoc = await tokenUtil.createRefreshToken({
      userId: tokenDoc.userId,
      expiresAt,
    });

    // Get user data
    const user = await userService.getUserById(tokenDoc.userId);
    if (!user) {
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    // Create new access token
    const accessToken = jwtUtil.createAccessToken({
      id: user.id,
      email: user.email,
      role: user.role_id,
    });

    const newRefreshToken = newRefreshTokenDoc.refreshToken;

    // Set cookies
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV,
      sameSite: COOKIE_SAME_SITE,
    };

    res.cookie(ACCESS_TOKEN_COOKIE_NAME, accessToken, {
      ...cookieOptions,
      maxAge: ACCESS_TOKEN_EXPIRY_MS,
    });

    res.cookie(REFRESH_TOKEN_COOKIE_NAME, newRefreshToken, {
      ...cookieOptions,
      maxAge: REFRESH_TOKEN_EXPIRY_MS,
    });

    res.json(
      successResponse(constants.TOKEN_REFRESHED_SUCCESSFULLY, {
        accessToken,
        refreshToken: newRefreshToken,
      })
    );
  } catch (error) {
    logger.error(loggerMessages.ERROR_REFRESHING_TOKEN, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(constants.INTERNAL_SERVER_ERROR));
  }
};

/**
 * Forgot user password and send reset token via email
 * @route POST /api/auth/forgot-password
 */
export const forgotPassword = async (req, res) => {
  try {
    logger.info(loggerMessages.LOGGING_FORGOT_PASSWORD);

    let { email } = req.body;
    if (email) {
      email = email.toLowerCase();
    }

    // Check if user exists
    const user = await authService.checkUserExists({ email });
    if (!user) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_DOES_NOT_EXIST));
    }

    // Create reset token
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
    const resetTokenDoc = await tokenUtil.createResetToken({
      userId: user.id,
      expiresAt,
    });

    // Send reset email
    await emailService.sendResetPasswordEmail(email, resetTokenDoc.resetToken);

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.PASSWORD_RESET_LINK_SENT));
  } catch (error) {
    logger.error(loggerMessages.ERROR_PASSWORD_FORGOT_EMAIL_SENT, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Reset user password via reset token (forgot password flow)
 * @route POST /api/auth/reset-password/:resetToken
 */
export const resetPassword = async (req, res) => {
  try {
    const { newPassword, confirmPassword } = req.body;
    const resetToken = req.params.resetToken;

    // Validate required fields
    if (!newPassword || !confirmPassword) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.NEW_PASSWORD_REQUIRED));
    }

    // Check if passwords match
    if (newPassword !== confirmPassword) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.PASSWORD_CONFIRM_PASSWORD_MISMATCH));
    }

    // Validate reset token
    const resetTokenDoc = await tokenUtil.findValidResetToken(resetToken);
    if (!resetTokenDoc) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.INVALID_OR_EXPIRED_RESET_TOKEN));
    }

    const userId = resetTokenDoc.userId;

    // Mark token as used and revoke all refresh tokens
    await Promise.all([
      tokenUtil.markResetTokenUsed(resetToken),
      tokenUtil.revokeAllRefreshTokensForUser(userId),
    ]);

    // Get user and update password
    const user = await userService.getUserById(userId);
    if (!user) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    const hashedPassword = await bcrypt.hash(newPassword, 8);
    await authService.updateUserPassword(userId, hashedPassword);

    // Log audit
    await logUserAudit({
      action: AuditActions.PASSWORD_CHANGE,
      record_id: userId,
      user_id: userId,
      description: UserAuditDescriptions.PASSWORD_CHANGED,
    });

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.PASSWORD_CHANGED_SUCCESSFULLY));
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_USER_PASSWORD, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(error.message || constants.INTERNAL_SERVER_ERROR));
  }
};

/**
 * Change user password (authenticated user)
 * @route POST /api/auth/change-password
 */
export const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CURRENT_AND_NEW_PASSWORD_REQUIRED));
    }

    // Check if new password and confirm password match
    if (newPassword !== confirmPassword) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.PASSWORD_CONFIRM_PASSWORD_MISMATCH));
    }

    // Get user and validate current password
    const user = await userService.getUserById(userId);
    if (!user) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    const isPasswordValid = await authService.validatePassword(
      currentPassword,
      user.password_hash
    );
    if (!isPasswordValid) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.CURRENT_PASSWORD_INCORRECT));
    }

    // Update password
    const hashedPassword = await bcrypt.hash(newPassword, 8);
    await authService.updateUserPassword(userId, hashedPassword);

    // Log audit
    await logUserAudit({
      action: AuditActions.PASSWORD_CHANGE,
      record_id: userId,
      user_id: userId,
      description: UserAuditDescriptions.PASSWORD_CHANGED,
    });

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.PASSWORD_CHANGED_SUCCESSFULLY));
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_USER_PASSWORD, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(error.message || constants.INTERNAL_SERVER_ERROR));
  }
};

/**
 * Get user profile by ID
 * @route GET /api/auth/profile
 */
export const getProfile = async (req, res) => {
  try {
    logger.info(loggerMessages.FETCHING_PROFILE_BY_ID);

    const { id } = req.user;
    const user = await userService.getUserById(id);

    if (!user) {
      logger.info(loggerMessages.USER_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(constants.PROFILE_FETCHED_SUCCESSFULLY, user));
  } catch (error) {
    logger.error(loggerMessages.ERROR_FETCHING_PROFILE_BY_ID, error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(errorMessage));
  }
};

/**
 * Update user profile by ID
 * @route PUT /api/auth/profile/:id
 */
export const updateProfile = async (req, res) => {
  try {
    logger.info(loggerMessages.UPDATING_PROFILE);

    const { id } = req.params;
    const userData = req.body;
    userData.email = userData.email.toLowerCase()

    // Check if user exists
    const user = await userService.getUserById(id);
    if (!user) {
      logger.info(loggerMessages.USER_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }
    const email = userData.email
    const checkUserEmail = await authService.checkUserExists({email})
    

    if (checkUserEmail && checkUserEmail.id ==! user.id) {
      logger.info(loggerMessages.USER_EMAIL_EMAIL);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_ALREADY_EXISTS_ERROR));
    }

    // Check email uniqueness if email is being changed
    if (userData.email && userData.email !== user.email) {
      const existingUser = await authService.checkUserExists(userData.email);
      if (existingUser && existingUser.id !== parseInt(id)) {
        logger.warn(loggerMessages.USER_ALREADY_EXISTS_LOG_MESSAGE);
        return res
          .status(status.STATUS_CODE_BAD_REQUEST)
          .json(errorResponse(constants.USER_ALREADY_EXISTS_ERROR));
      }
    }

    // Hash password if provided
    if (userData.password) {
      const hashedPassword = await bcrypt.hash(userData.password, 8);
      userData.password_hash = hashedPassword;
      delete userData.password;
    }

    // Update user
    const oldValue = JSON.stringify(user);
    const updatedUserProfile = await userService.updateUser(id, userData);

    if (!updatedUserProfile) {
      logger.info(loggerMessages.USER_NOT_FOUND);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(constants.USER_NOT_FOUND));
    }

    // Log audit
    await logUserAudit({
      action: AuditActions.UPDATE,
      record_id: id,
      user_id: id,
      old_value: oldValue,
      new_value: JSON.stringify(updatedUserProfile),
      description: UserAuditDescriptions.PROFILE_UPDATED,
    });

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(
          constants.PROFILE_UPDATED_SUCCESSFULLY,
          updatedUserProfile
        )
      );
  } catch (error) {
    logger.error(loggerMessages.ERROR_UPDATING_PROFILE, error);
    return res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(error.message || constants.INTERNAL_SERVER_ERROR));
  }
};
