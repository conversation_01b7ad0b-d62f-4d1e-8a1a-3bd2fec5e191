import * as aiCallLogService from '../services/aiCallLog.service.js';
import * as patientService from '../services/patient.service.js';
import { successResponse, errorResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import * as constants from '../utils/constants.utils.js';
import * as logMessages from '../utils/log_messages.utils.js';
import * as status from '../utils/status_code.utils.js';

/**
 * Create a new AI call log
 * @route POST /v1/ai-call-log/create
 */
export const createAICallLog = async (req, res) => {
  logger.info(logMessages.CREATING_AI_CALL_LOG);
  try {
    const callLogData = req.body;
    if (req.user && req.user.id) {
      callLogData.created_by = req.user.id;
      callLogData.updated_by = req.user.id;
    }
    
    // Handle phone number lookup to find patient
    if (callLogData.phone_number) {
      const patient = await patientService.findPatientByEmailOrPhone({ phone_number: callLogData.phone_number });
      if (patient) {
        callLogData.patient_id = patient.id;
      } else {
        return res.status(status.STATUS_CODE_NOT_FOUND).json(
          errorResponse(constants.PATIENT_NOT_FOUND)
        );
      }
    }
    
    // Remove phone_number from callLogData as it's not a field in the AI call log model
    delete callLogData.phone_number;
    
    // Create the AI call log
    const newCallLog = await aiCallLogService.createAICallLog(callLogData);
    
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.AI_CALL_LOG_CREATED_SUCCESSFULLY, newCallLog)
    );
  } catch (error) {
    logger.error(logMessages.ERROR_CREATING_AI_CALL_LOG + ':', error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
};

/**
 * Get all AI call logs
 * @route GET /v1/ai-call-log/list
 */
export const getAllAICallLogs = async (req, res) => {
  logger.info(logMessages.FETCHING_AI_CALL_LOGS);
  try {
    const filters = req.query;
    const callLogs = await aiCallLogService.getAllAICallLogs(filters);
    return res.status(status.STATUS_CODE_SUCCESS).json(
      successResponse(constants.AI_CALL_LOGS_FETCHED_SUCCESSFULLY, callLogs)
    );
  } catch (error) {
    logger.error(logMessages.ERROR_FETCHING_AI_CALL_LOGS + ':', error);
    const errorMessage = error.message || constants.INTERNAL_SERVER_ERROR;
    return res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json(
      errorResponse(errorMessage)
    );
  }
}; 