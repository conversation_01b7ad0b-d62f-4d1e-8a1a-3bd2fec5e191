// phoneUtils.ts
// Utility functions for phone number handling and country detection

/**
 * Detects the country code from a phone number
 * @param phoneNumber - The phone number with or without country code
 * @returns The country code (e.g., 'us', 'in', 'gb')
 */
export const detectCountryFromPhone = (phoneNumber: string): string => {
  if (!phoneNumber) return 'us';
  
  // Remove any non-digit characters except +
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // Common country code patterns
  const countryPatterns: Record<string, RegExp> = {
    'us': /^\+?1/,
    'in': /^\+?91/,
    'gb': /^\+?44/,
    'ca': /^\+?1/,
    'au': /^\+?61/,
    'de': /^\+?49/,
    'fr': /^\+?33/,
    'es': /^\+?34/,
    'it': /^\+?39/,
    'jp': /^\+?81/,
    'cn': /^\+?86/,
    'br': /^\+?55/,
    'mx': /^\+?52/,
    'ru': /^\+?7/,
    'kr': /^\+?82/,
  };

  for (const [country, pattern] of Object.entries(countryPatterns)) {
    if (pattern.test(cleanNumber)) {
      return country;
    }
  }
  
  return 'us'; // Default to US
};

/**
 * Formats a phone number for display
 * @param phoneNumber - The raw phone number
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // Remove any non-digit characters except +
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // If it already has a +, return as is
  if (cleanNumber.startsWith('+')) {
    return cleanNumber;
  }
  
  // Add + if it doesn't have one
  return `+${cleanNumber}`;
};

/**
 * Normalizes phone number for API submission
 * Preserves the + prefix and ensures consistent format for backend
 * @param phoneNumber - The phone number from PhoneInput component
 * @returns Normalized phone number for API (e.g., "+14892524816")
 */
export const normalizePhoneForAPI = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters except +
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // Ensure it has the + prefix
  if (!cleanNumber.startsWith('+')) {
    return `+${cleanNumber}`;
  }
  
  return cleanNumber;
};

/**
 * Formats phone number for display with proper formatting
 * @param phoneNumber - The raw phone number from API
 * @returns Formatted phone number for display (e.g., "+****************")
 */
export const formatPhoneForDisplay = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  if (digits.length === 0) return '';
  
  // Format by counting from the end:
  // Last 4 digits, then 3, then 3, and the rest as country code
  if (digits.length >= 10) {
    const last4 = digits.slice(-4);
    const next3 = digits.slice(-7, -4);
    const next3Before = digits.slice(-10, -7);
    const countryCode = digits.slice(0, -10);
    
    if (countryCode.length > 0) {
      return `+${countryCode} (${next3Before}) ${next3}-${last4}`;
    } else {
      // If no country code, assume it's a 10-digit number
      return `+1 (${next3Before}) ${next3}-${last4}`;
    }
  }
  
  // For shorter numbers, just add + prefix
  return `+${digits}`;
};

/**
 * Validates phone number format
 * @param phoneNumber - The phone number to validate
 * @returns Validation result with error message if invalid
 */
export const validatePhoneNumber = (phoneNumber: string): { isValid: boolean; error?: string } => {
  if (!phoneNumber) {
    return { isValid: false, error: 'Phone number is required' };
  }
  
  // Remove all non-digit characters
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  
  // Check minimum length (country code + area code + local number)
  if (digitsOnly.length < 10) {
    return { isValid: false, error: 'Phone number must be at least 10 digits' };
  }
  
  // Check maximum length (country code + area code + local number)
  if (digitsOnly.length > 15) {
    return { isValid: false, error: 'Phone number cannot exceed 15 digits' };
  }
  
  return { isValid: true };
};

/**
 * Extracts the local phone number without country code
 * @param phoneNumber - The full phone number with country code
 * @param country - The country code
 * @returns Local phone number without country code
 */
export const extractLocalNumber = (phoneNumber: string, country: string = 'us'): string => {
  if (!phoneNumber) return '';
  
  const cleanNumber = phoneNumber.replace(/[^\d+]/g, '');
  
  // Remove country code based on country
  const countryCodes: Record<string, string> = {
    'us': '1',
    'in': '91',
    'gb': '44',
    'ca': '1',
    'au': '61',
    'de': '49',
    'fr': '33',
    'es': '34',
    'it': '39',
    'jp': '81',
    'cn': '86',
    'br': '55',
    'mx': '52',
    'ru': '7',
    'kr': '82',
  };
  
  const countryCode = countryCodes[country] || '1';
  
  if (cleanNumber.startsWith(countryCode)) {
    return cleanNumber.substring(countryCode.length);
  }
  
  return cleanNumber;
};
