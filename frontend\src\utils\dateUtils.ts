/**
 * Date utility functions for handling various date formats
 * Used for parsing and formatting dates, especially for DOB (Date of Birth) fields
 */

/**
 * Supported date formats for parsing
 */
export const DATE_FORMATS = {
  DD_MM_YYYY: /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
  MM_DD_YYYY: /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
  YYYY_MM_DD: /^(\d{4})-(\d{1,2})-(\d{1,2})$/
} as const;

/**
 * Converts various date formats to YYYY-MM-DD format for HTML date inputs
 * @param dateValue - The date value to parse (can be string, Date object, or any format)
 * @returns Formatted date string in YYYY-MM-DD format, or original value if parsing fails
 */
export const parseDateForInput = (dateValue: string | Date | null | undefined): string => {
  if (!dateValue) return '';
  
  const dateStr = String(dateValue).trim();
  
  // Handle YYYY-MM-DD format (already correct) - check this first
  if (dateStr.match(DATE_FORMATS.YYYY_MM_DD)) {
    return dateStr;
  }
  
  // Handle DD-MM-YYYY format (e.g., "15-03-1990")
  let match = dateStr.match(DATE_FORMATS.DD_MM_YYYY);
  if (match) {
    const [, day, month, year] = match;
    const mm = month.padStart(2, "0");
    const dd = day.padStart(2, "0");
    return `${year}-${mm}-${dd}`;
  }
  
  // Handle MM/DD/YYYY format (e.g., "03/15/1990")
  match = dateStr.match(DATE_FORMATS.MM_DD_YYYY);
  if (match) {
    const [, month, day, year] = match;
    const mm = month.padStart(2, "0");
    const dd = day.padStart(2, "0");
    return `${year}-${mm}-${dd}`;
  }
  
  // Try to parse as Date object and format
  try {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
  } catch {
    // Ignore parsing errors
  }
  
  // Return original value if no parsing worked
  return dateStr;
};

/**
 * Validates if a date string is in a valid format
 * @param dateValue - The date value to validate
 * @returns true if the date is in a recognized format, false otherwise
 */
export const isValidDateFormat = (dateValue: string | Date | null | undefined): boolean => {
  if (!dateValue) return false;
  
  const dateStr = String(dateValue).trim();
  
  // Check against known formats
  if (dateStr.match(DATE_FORMATS.DD_MM_YYYY) || 
      dateStr.match(DATE_FORMATS.MM_DD_YYYY) || 
      dateStr.match(DATE_FORMATS.YYYY_MM_DD)) {
    return true;
  }
  
  // Try parsing as Date object
  try {
    const date = new Date(dateStr);
    return !isNaN(date.getTime());
  } catch {
    return false;
  }
};

/**
 * Formats a date for display purposes
 * @param dateValue - The date value to format
 * @param format - The desired output format ('DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD')
 * @returns Formatted date string
 */
export const formatDateForDisplay = (
  dateValue: string | Date | null | undefined, 
  format: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD' = 'DD/MM/YYYY'
): string => {
  if (!dateValue) return '';
  
  const dateStr = String(dateValue).trim();
  
  // Handle MM/DD/YYYY format directly for display
  let match = dateStr.match(DATE_FORMATS.MM_DD_YYYY);
  if (match) {
    const [, month, day, year] = match;
    const mm = month.padStart(2, "0");
    const dd = day.padStart(2, "0");
    
    switch (format) {
      case 'DD/MM/YYYY':
        return `${dd}/${mm}/${year}`;
      case 'MM/DD/YYYY':
        return `${mm}/${dd}/${year}`;
      case 'YYYY-MM-DD':
        return `${year}-${mm}-${dd}`;
      default:
        return `${dd}/${mm}/${year}`;
    }
  }
  
  // Handle DD-MM-YYYY format directly for display
  match = dateStr.match(DATE_FORMATS.DD_MM_YYYY);
  if (match) {
    const [, day, month, year] = match;
    const mm = month.padStart(2, "0");
    const dd = day.padStart(2, "0");
    
    switch (format) {
      case 'DD/MM/YYYY':
        return `${dd}/${mm}/${year}`;
      case 'MM/DD/YYYY':
        return `${mm}/${dd}/${year}`;
      case 'YYYY-MM-DD':
        return `${year}-${mm}-${dd}`;
      default:
        return `${dd}/${mm}/${year}`;
    }
  }
  
  // Handle YYYY-MM-DD format
  if (dateStr.match(DATE_FORMATS.YYYY_MM_DD)) {
    const [year, month, day] = dateStr.split('-');
    
    switch (format) {
      case 'DD/MM/YYYY':
        return `${day}/${month}/${year}`;
      case 'MM/DD/YYYY':
        return `${month}/${day}/${year}`;
      case 'YYYY-MM-DD':
        return dateStr;
      default:
        return `${day}/${month}/${year}`;
    }
  }
  
  // Try to parse as Date object and format
  try {
    const date = new Date(dateStr);
    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      
      switch (format) {
        case 'DD/MM/YYYY':
          return `${day}/${month}/${year}`;
        case 'MM/DD/YYYY':
          return `${month}/${day}/${year}`;
        case 'YYYY-MM-DD':
          return `${year}-${month}-${day}`;
        default:
          return `${day}/${month}/${year}`;
      }
    }
  } catch {
    // Ignore parsing errors
  }
  
  // Return original value if no parsing worked
  return dateStr;
};

/**
 * Calculates age from date of birth
 * @param dob - Date of birth
 * @returns Age in years
 */
export const calculateAge = (dob: string | Date | null | undefined): number => {
  if (!dob) return 0;
  
  const birthDate = new Date(dob);
  const today = new Date();
  
  if (isNaN(birthDate.getTime())) return 0;
  
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return Math.max(0, age);
}; 