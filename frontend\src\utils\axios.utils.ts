import axios from "axios";
import { errorMessage } from "./commonFunctions";
import { SESSION_EXPIRED, ERROR_MESSAGES } from "../Constants/HooksAPI";
import { AxiosRequestConfig, AxiosResponse } from "axios";
import type { AxiosError } from "axios";

const API_BASE_URL = `${process.env.NEXT_PUBLIC_BACKEND_URL}/${process.env.NEXT_PUBLIC_VERSION}`;

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Always send cookies for cross-origin requests
axiosInstance.defaults.withCredentials = true;

// Request interceptor (no manual token logic needed for cookie-based auth)
axiosInstance.interceptors.request.use(
  (config) => config,
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only redirect to login for 401 errors on non-auth endpoints
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/')) {
      errorMessage(SESSION_EXPIRED);
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

const handleApiError = (error: unknown) => {
  if (error && typeof error === "object" && "response" in error) {
    // Axios error
    const axiosError = error as AxiosError<{ message?: string }>;
    const message =
      axiosError.response?.data?.message ||
      axiosError.message ||
      ERROR_MESSAGES.API_REQUEST_FAILED;
    errorMessage(message);
    throw error;
  }
  if (error instanceof TypeError && error.message === "Failed to fetch") {
    errorMessage(ERROR_MESSAGES.NETWORK_ERROR);
    throw error;
  }
  const message = (error as Error).message || ERROR_MESSAGES.UNEXPECTED_ERROR;
  errorMessage(message);
  throw error;
};

export const apiRequest = {
  get: async <T>(endpoint: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      const response: AxiosResponse<T> = await axiosInstance.get(
        endpoint,
        config
      );
      return response.data;
    } catch (error) {
      return handleApiError(error) as Promise<T>;
    }
  },
  post: async <T = unknown, R = unknown>(
    endpoint: string,
    data: T,
    config?: AxiosRequestConfig
  ): Promise<R> => {
    try {
      const response: AxiosResponse<R> = await axiosInstance.post(
        endpoint,
        data,
        config
      );
      return response.data;
    } catch (error) {
      return handleApiError(error) as Promise<R>;
    }
  },
  put: async <T = unknown, R = unknown>(
    endpoint: string,
    data: T,
    config?: AxiosRequestConfig
  ): Promise<R> => {
    try {
      const response: AxiosResponse<R> = await axiosInstance.put(
        endpoint,
        data,
        config
      );
      return response.data;
    } catch (error) {
      return handleApiError(error) as Promise<R>;
    }
  },
  delete: async <R = unknown>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<R> => {
    try {
      const response: AxiosResponse<R> = await axiosInstance.delete(
        endpoint,
        config
      );
      return response.data;
    } catch (error) {
      return handleApiError(error) as Promise<R>;
    }
  },
};

// ElevenLabs API call function
export const makeElevenLabsCall = async (phoneNumber: string, patient?: { 
  first_name: string; 
  last_name: string; 
  last_visit: string | null; 
  last_visit_summary: string | null; 
  phone_number: string;
}) => {
  try {

    interface ElevenLabsPayload {
      agent_id: string | undefined;
      agent_phone_number_id: string | undefined;
      to_number: string;
      conversation_initiation_client_data?: {
        dynamic_variables: {
          timeZone: string;
          phone_number: string;
          firstName: string;
          last_visit_summary: string;
        };
      };
    }

    const payload: ElevenLabsPayload = {
      agent_id: process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID,
      agent_phone_number_id: process.env.NEXT_PUBLIC_ELEVENLABS_PHONE_NUMBER_ID,
      to_number: phoneNumber
    };

    // Add conversation initiation data if patient information is provided
    if (patient) {
      payload.conversation_initiation_client_data = {
        dynamic_variables: {
          timeZone: "Central US",
          phone_number: patient.phone_number,
          firstName: `${patient.first_name} ${patient.last_name}`,
          last_visit_summary: patient.last_visit_summary || "No previous visit summary available",
        }
      };
    }

    const response = await fetch('https://api.elevenlabs.io/v1/convai/twilio/outbound-call', {
      method: 'POST',
      headers: {
        'xi-api-key': process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

export default axiosInstance;
