import express from 'express';
import userRoutes from './user.route.js';
import authRoutes from './auth.route.js';
import clinicRoutes from './clinic.route.js';
import doctorRoutes from './doctor.route.js';
import patientRoutes from './patient.route.js';
import appointmentRoutes from './appointment.route.js';
import templateRoutes from './template.route.js';
import campaignRoutes from './campaign.route.js';
import escalationRoutes from './escalation.route.js';
import aiCallLogRoutes from './aiCallLog.route.js';
import webhookRoutes from './webhook.route.js';

const router = express.Router();

// API Versions
const version1 = '/v1';

// User Routes
router.use(`${version1}/user`, userRoutes);

// Auth Routes
router.use(`${version1}/auth`, authRoutes);

// Clinic Routes
router.use(`${version1}/clinic`, clinicRoutes);

// Doctor Routes
router.use(`${version1}/doctor`, doctorRoutes);

// Patient Routes
router.use(`${version1}/patient`, patientRoutes);

// Appointment Routes
router.use(`${version1}/appointment`, appointmentRoutes);

// Template Routes
router.use(`${version1}/template`, templateRoutes);

// Campaign Routes
router.use(`${version1}/campaign`, campaignRoutes);

// Escalation Routes
router.use(`${version1}/escalation`, escalationRoutes);

// AI Call Log Routes
router.use(`${version1}/ai-call-log`, aiCallLogRoutes);

// Webhook Routes
router.use(`${version1}/webhook`, webhookRoutes);

export default router;
