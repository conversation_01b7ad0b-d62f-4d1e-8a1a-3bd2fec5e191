import { useState, useEffect } from "react";
import { apiRequest } from "../utils/axios.utils";
import { errorMessage } from "../utils/commonFunctions";

export interface UserProfile {
  id: number;
  clinic_id: number | null;
  name: string;
  email: string;
  user_phonenumber: string;
  role_id: number;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  is_active: boolean;
}

interface ApiResponse {
  status: boolean;
  message: string;
  data: UserProfile;
}

interface UpdateProfilePayload {
  name: string;
  email: string;
  role: string;
  clinic_id: number | null;
  user_phonenumber: string;
  timezone: string;
}

export function useUserProfile() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  const fetchProfile = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await apiRequest.get<ApiResponse>("/auth/profile");
      if (res && res.status) {
        setProfile(res.data);
      } else {
        const errorMsg = res?.message || "Failed to fetch profile";
        setError(errorMsg);
        errorMessage(errorMsg);
      }
    } catch (err: unknown) {
      const errorMsg = err instanceof Error ? err.message : "Failed to fetch profile";
      setError(errorMsg);
      errorMessage(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (payload: UpdateProfilePayload) => {
    if (!profile?.id) {
      throw new Error("Profile ID not found");
    }

    setUpdating(true);
    setError(null);
    
    try {
      const response = await apiRequest.put(
        `/auth/profile/${profile.id}`,
        payload
      ) as { status: boolean; message?: string; data?: UserProfile };

      if (response && response.status) {
        // Refresh the profile data after successful update
        await fetchProfile();
        return { success: true, message: "Profile updated successfully!" };
      } else {
        const errorMsg = response?.message || "Failed to update profile";
        setError(errorMsg);
        return { success: false, message: errorMsg };
      }
    } catch (err: unknown) {
      const errorMsg = err instanceof Error ? err.message : "Failed to update profile";
      setError(errorMsg);
      return { success: false, message: errorMsg };
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  return { 
    profile, 
    loading, 
    error, 
    updating,
    refetch: fetchProfile,
    updateProfile 
  };
}
