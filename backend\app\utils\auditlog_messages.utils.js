// Shared audit log actions
export const AuditActions = {
  CREATE: 'CREATE',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  LOGIN: 'LOGIN',
  LOGIN_FAILED: '<PERSON>OGIN_FAILED',
  PASSWORD_CHANGE: 'PASSWORD_CHANGE',
};

// User audit log descriptions
export const UserAuditDescriptions = {
  USER_CREATED_SIGNUP: 'User created via signup',
  USER_UPDATED: 'User updated via user API',
  USER_DELETED: 'User deleted via user API',
  USER_LOGGED_IN: 'User logged in',
  PASSWORD_CHANGED: 'User changed password',
  PROFILE_UPDATED: 'User profile updated',
  L<PERSON>GIN_FAILED_EMAIL: (email) => `Failed login attempt for email: ${email}`,
  LOGIN_FAILED_USER: (userId) => `Failed login attempt for user id: ${userId}`,
};

// Clinic audit log descriptions
export const ClinicAuditDescriptions = {
  CLINIC_CREATED: 'Clinic created via API',
  CLINIC_UPDATED: 'Clinic updated via API',
  CLINIC_DELETED: 'Clinic deleted (soft delete) via API',
  CLINIC_UPDATED_BY: (userId) => `Clinic updated by user id: ${userId}`,
  CLINIC_DELETED_BY: (userId) => `Clinic deleted by user id: ${userId}`,
};

// Doctor audit log descriptions
export const DoctorAuditDescriptions = {
  DOCTOR_CREATED: 'Doctor created via API',
  DOCTOR_UPDATED: 'Doctor updated via API',
  DOCTOR_DELETED: 'Doctor deleted (soft delete) via API',
  DOCTOR_UPDATED_BY: (userId) => `Doctor updated by user id: ${userId}`,
  DOCTOR_DELETED_BY: (userId) => `Doctor deleted by user id: ${userId}`,
};

// Patient audit log descriptions
export const PatientAuditDescriptions = {
  PATIENT_CREATED: 'Patient created via API',
  PATIENT_UPDATED: 'Patient updated via API',
  PATIENT_DELETED: 'Patient deleted (soft delete) via API',
  PATIENT_UPDATED_BY: (userId) => `Patient updated by user id: ${userId}`,
  PATIENT_DELETED_BY: (userId) => `Patient deleted by user id: ${userId}`,
};

// Template audit log descriptions
export const TemplateAuditDescriptions = {
  TEMPLATE_CREATED: 'Template created via API',
  TEMPLATE_UPDATED: 'Template updated via API',
  TEMPLATE_DELETED: 'Template deleted (soft delete) via API',
  TEMPLATE_UPDATED_BY: (userId) => `Template updated by user id: ${userId}`,
  TEMPLATE_DELETED_BY: (userId) => `Template deleted by user id: ${userId}`,
};

// Campaign audit log descriptions
export const CampaignAuditDescriptions = {
  CAMPAIGN_CREATED: 'Campaign created via API',
  CAMPAIGN_UPDATED: 'Campaign updated via API',
  CAMPAIGN_DELETED: 'Campaign deleted (soft delete) via API',
  CAMPAIGN_UPDATED_BY: (userId) => `Campaign updated by user id: ${userId}`,
  CAMPAIGN_DELETED_BY: (userId) => `Campaign deleted by user id: ${userId}`,
};

// Appointment audit log descriptions
export const AppointmentAuditDescriptions = {
  APPOINTMENT_CREATED: 'Appointment created via API',
  APPOINTMENT_UPDATED: 'Appointment updated via API',
  APPOINTMENT_DELETED: 'Appointment deleted (soft delete) via API',
  APPOINTMENT_UPDATED_BY: (userId) => `Appointment updated by user id: ${userId}`,
  APPOINTMENT_DELETED_BY: (userId) => `Appointment deleted by user id: ${userId}`,
};


