"use strict";

export async function up(queryInterface, Sequelize) {
  await queryInterface.createTable('tokens', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER,
    },
    userId: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: { model: 'users', key: 'id' },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    refreshToken: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    refreshTokenExpiresAt: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    resetToken: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    resetTokenExpiresAt: {
      type: Sequelize.DATE,
      allowNull: true,
    },
    used: {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },
    revoked: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    createdAt: {
      allowNull: false,
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updatedAt: {
      allowNull: false,
      type: Sequelize.DATE,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
    },
  });
}

export async function down(queryInterface) {
  await queryInterface.dropTable('tokens');
}
