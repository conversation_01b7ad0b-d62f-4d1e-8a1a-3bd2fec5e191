"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import AddPatientForm from "@/components/forms/AddOrEditPatientForm";
import PatientHeader from "@/components/PatientRegistry/PatientHeader";
import PatientFilters from "@/components/PatientRegistry/PatientFilters";
import PatientStats from "@/components/PatientRegistry/PatientStats";
import PatientTable from "@/components/PatientRegistry/PatientTable";
import Pagination from "@/components/CommonComponents/Pagination";
import PageSection from "@/components/CommonComponents/PageSection";
import * as PatientConst from "@/Constants/PatientRegistry";
import { usePatients, Patient } from "@/hooks/usePatients";
import { useDoctor } from "@/hooks/useDoctor";
import { useClinic } from "@/hooks/useClinic";

export interface Clinic {
  value: string;
  clinic_name: string;
  id: number;
}

export interface Doctor {
  value: string;
  doctor_name: string;
  id: number;
  clinic_id: number;
}

const PatientRegistry = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState(PatientConst.FILTER_ALL);
  const [filterClinic, setFilterClinic] = useState<string>(
    PatientConst.FILTER_ALL
  );
  const [isAddPatientOpen, setIsAddPatientOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const pageSize = 10;

  // UI Strings from PatientRegistry
  const PATIENT_RECORDS_TITLE =
    PatientConst.TABLE_HEADER_PATIENT +
    " " +
    PatientConst.PATIENT_RECORDS_SUFFIX;

  const {
    patients,
    loading,
    error,
    getAllPatients,
    createPatient,
    updatePatient,
    deletePatient,
  } = usePatients();
  const { getClinics } = useClinic();
  const { getDoctors } = useDoctor();

  useEffect(() => {
    const fetchPatients = async () => {
      await getAllPatients();
    };
    const fetchClinics = async () => {
      const clinicsData = await getClinics();
      if (clinicsData.success && clinicsData.data) {
        // Remove duplicate clinics by id
        const uniqueClinicsMap = new Map();
        (clinicsData.data as Clinic[]).forEach((clinic) => {
          uniqueClinicsMap.set(clinic.id, clinic);
        });
        setClinics(Array.from(uniqueClinicsMap.values()));
      }
    };
    const fetchDoctors = async () => {
      const doctorsData = await getDoctors();
      if (doctorsData.success && doctorsData.data) {
        setDoctors(doctorsData.data as Doctor[]);
      }
    };
    fetchPatients();
    fetchClinics();
    fetchDoctors();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleAddPatient = async (patientData: Record<string, unknown>) => {
    // Transform the form data to match the Patient interface
    const transformedData: Omit<Patient, "id"> = {
      clinic_id: Number(patientData.clinic_id) || 0,
      first_name: String(patientData.first_name || ""),
      last_name: String(patientData.last_name || ""),
      email: String(patientData.email || ""),
      phone_number: String(patientData.phone_number || ""),
      last_visit: null, // Set to null as it's optional and not provided in form
      last_visit_summary: null, // Set to null as it's optional and not provided in form
      tags: patientData.tags
        ? Array.isArray(patientData.tags)
          ? (patientData.tags as string[])
          : [String(patientData.tags)]
        : null,
      preferences: null, // Set to null as it's optional and not provided in form
      dob: patientData.dob ? String(patientData.dob) : null,
      gender: patientData.gender
        ? (String(patientData.gender) as "male" | "female" | "other")
        : null,
      address: patientData.address ? String(patientData.address) : null,
      doctors: patientData.doctors
        ? Array.isArray(patientData.doctors)
          ? patientData.doctors.map((d) => Number(d))
          : [Number(patientData.doctors)]
        : null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      created_by: 1, // Default value, should be replaced with actual user ID
      updated_by: 1, // Default value, should be replaced with actual user ID
      is_deleted: false,
      is_active: true,
    };

    // Clean the data to remove null values that the backend doesn't accept
    const cleanData = Object.fromEntries(
      Object.entries(transformedData).filter(([, value]) => value !== null)
    );

    await createPatient(cleanData as Omit<Patient, "id">);
  };

  const getPatientStatus = (isActive: boolean) => {
    return isActive ? PatientConst.BUTTON_ACTIVE : PatientConst.BUTTON_INACTIVE;
  };

  const filteredPatients = patients.filter((patient) => {
    const matchesSearch = patient.first_name
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const patientStatus = getPatientStatus(patient.is_active);
    const matchesStatus =
      filterStatus === PatientConst.FILTER_ALL ||
      patientStatus.toLowerCase() === filterStatus;
    const matchesClinic =
      filterClinic === PatientConst.FILTER_ALL ||
      String(patient.clinic_id) === filterClinic;
    return matchesSearch && matchesStatus && matchesClinic;
  });

  const total = patients.length;
  const active = patients.filter(
    (p) => getPatientStatus(p.is_active) === PatientConst.BUTTON_ACTIVE
  ).length;
  const newThisMonth = patients.filter((p) => {
    const createdDate = new Date(p.created_at || "");
    const thisMonth = new Date();
    return (
      createdDate.getMonth() === thisMonth.getMonth() &&
      createdDate.getFullYear() === thisMonth.getFullYear()
    );
  }).length;
  const reactivationDue = patients.filter(
    (p) => getPatientStatus(p.is_active) === PatientConst.BUTTON_INACTIVE
  ).length;

  // Pagination logic at the page level
  const totalPages = Math.ceil(filteredPatients.length / pageSize);
  const paginatedPatients = filteredPatients.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  if (loading) {
    return (
      <PageSection>
        <PatientHeader
          onAdd={() => setIsAddPatientOpen(true)}
          buttonVariant="main"
        />
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600">
              {PatientConst.LOADING_PATIENTS}
            </span>
          </div>
        </div>
      </PageSection>
    );
  }

  if (error) {
    return (
      <PageSection>
        <PatientHeader
          onAdd={() => setIsAddPatientOpen(true)}
          buttonVariant="main"
        />
        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p>{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
                variant="outline"
              >
                {PatientConst.BUTTON_RETRY || "Retry"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </PageSection>
    );
  }

  return (
    <PageSection>
      <PatientHeader
        onAdd={() => setIsAddPatientOpen(true)}
        buttonVariant="main"
      />
      <Card>
        <CardContent className="p-6">
          <PatientFilters
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            filterStatus={filterStatus}
            setFilterStatus={setFilterStatus}
            filterClinic={filterClinic}
            setFilterClinic={setFilterClinic}
            clinics={clinics}
          />
        </CardContent>
      </Card>
      <PatientStats
        total={total}
        active={active}
        newThisMonth={newThisMonth}
        reactivationDue={reactivationDue}
      />
      <Card>
        <CardHeader>
          <CardTitle>{PATIENT_RECORDS_TITLE}</CardTitle>
        </CardHeader>
        <CardContent>
          <PatientTable
            patients={paginatedPatients}
            getPatientStatus={getPatientStatus}
            updatePatient={updatePatient}
            deletePatient={deletePatient}
            clinics={clinics}
            doctors={doctors}
          />
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            className="mt-6"
          />
        </CardContent>
      </Card>
      <AddPatientForm
        isOpen={isAddPatientOpen}
        onClose={() => setIsAddPatientOpen(false)}
        onSubmit={handleAddPatient}
        clinics={clinics}
        doctors={doctors}
      />
    </PageSection>
  );
};

export default PatientRegistry;
