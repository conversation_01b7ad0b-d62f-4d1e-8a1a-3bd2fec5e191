import { Op } from 'sequelize';
import { Appointment, <PERSON>, Patient, Doctor } from '../models/index.js';

/**
 * Generate 30-minute time slots for a given date and time range
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time in HH:mm format (e.g., "09:00")
 * @param {string} endTime - End time in HH:mm format (e.g., "17:00")
 * @returns {Array} Array of time slots in ISO format
 */
export const generateTimeSlots = (date, startTime = "10:00", endTime = "16:00") => {
  const slots = [];
  
  // Create dates in local timezone to avoid UTC conversion issues
  const start = new Date(`${date}T${startTime}:00`);
  const end = new Date(`${date}T${endTime}:00`);
  
  let currentSlot = new Date(start);
  
  while (currentSlot < end) {
    // Format the time slot in local timezone format to avoid UTC conversion
    const year = currentSlot.getFullYear();
    const month = String(currentSlot.getMonth() + 1).padStart(2, '0');
    const day = String(currentSlot.getDate()).padStart(2, '0');
    const hours = String(currentSlot.getHours()).padStart(2, '0');
    const minutes = String(currentSlot.getMinutes()).padStart(2, '0');
    const seconds = String(currentSlot.getSeconds()).padStart(2, '0');
    
    // Create ISO string in local timezone
    const localISOString = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    slots.push(localISOString);
    
    currentSlot.setMinutes(currentSlot.getMinutes() + 30);
  }
  
  return slots;
};

/**
 * Get available time slots for a doctor on a specific date
 * @param {number} doctorId - Doctor ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} startTime - Start time (default: "09:00")
 * @param {string} endTime - End time (default: "17:00")
 * @returns {Promise<Array>} Array of available time slots
 */
export const getAvailableTimeSlots = async (doctorId, date, startTime = "10:00", endTime = "16:00") => {
  try {
    // Generate all possible slots for the day
    const allSlots = generateTimeSlots(date, startTime, endTime);
    
    // Get booked appointments for the doctor on this date
    const bookedAppointments = await Appointment.findAll({
      where: {
        doctor_id: doctorId,
        appointment_date: date,
        is_deleted: false,
        is_active: true
      },
      attributes: ['appointment_time']
    });
    
    // Extract booked times and convert to local timezone format for comparison
    const bookedTimes = bookedAppointments.map(apt => {
      const aptDate = new Date(apt.appointment_time);
      const year = aptDate.getFullYear();
      const month = String(aptDate.getMonth() + 1).padStart(2, '0');
      const day = String(aptDate.getDate()).padStart(2, '0');
      const hours = String(aptDate.getHours()).padStart(2, '0');
      const minutes = String(aptDate.getMinutes()).padStart(2, '0');
      const seconds = String(aptDate.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    });
    
    // Filter out booked slots
    const availableSlots = allSlots.filter(slot => 
      !bookedTimes.includes(slot)
    );
    
    return availableSlots;
  } catch (error) {
    throw new Error(`Error getting available time slots: ${error.message}`);
  }
};

/**
 * Check if a time slot is available for booking
 * @param {Object} appointmentData - Appointment data to check
 * @param {number} appointmentData.doctor_id - Doctor ID
 * @param {string} appointmentData.appointment_date - Appointment date
 * @param {string} appointmentData.appointment_time - Appointment time
 * @param {number} excludeId - Appointment ID to exclude (for updates)
 * @returns {Promise<boolean>} True if slot is available, false if conflicted
 */
export const isTimeSlotAvailable = async (appointmentData, excludeId = null) => {
  try {
    const { doctor_id, appointment_date, appointment_time } = appointmentData;
    
    // Convert appointment_time to start and end times (assuming 30-minute slots)
    const appointmentDateTime = new Date(appointment_time);
    const slotStart = new Date(appointmentDateTime);
    const slotEnd = new Date(appointmentDateTime.getTime() + 30 * 60 * 1000); // 30 minutes later
    
    const where = {
      doctor_id,
      appointment_date,
      is_deleted: false,
      is_active: true,
      appointment_time: {
        [Op.between]: [slotStart, slotEnd]
      }
    };
    
    // Exclude current appointment if updating
    if (excludeId) {
      where.id = { [Op.ne]: excludeId };
    }
    
    const conflictingAppointments = await Appointment.findAll({ where });
    
    return conflictingAppointments.length === 0;
  } catch (error) {
    throw new Error(`Error checking time slot availability: ${error.message}`);
  }
};

/**
 * Get conflicting appointments for a time slot
 * @param {Object} appointmentData - Appointment data to check
 * @param {number} excludeId - Appointment ID to exclude (for updates)
 * @returns {Promise<Array>} Array of conflicting appointments
 */
export const getConflictingAppointments = async (appointmentData, excludeId = null) => {
  try {
    const { doctor_id, appointment_date, appointment_time } = appointmentData;
    
    // Convert appointment_time to start and end times (assuming 30-minute slots)
    const appointmentDateTime = new Date(appointment_time);
    const slotStart = new Date(appointmentDateTime);
    const slotEnd = new Date(appointmentDateTime.getTime() + 30 * 60 * 1000); // 30 minutes later
    
    const where = {
      doctor_id,
      appointment_date,
      is_deleted: false,
      is_active: true,
      appointment_time: {
        [Op.between]: [slotStart, slotEnd]
      }
    };
    
    // Exclude current appointment if updating
    if (excludeId) {
      where.id = { [Op.ne]: excludeId };
    }
    
    const conflictingAppointments = await Appointment.findAll({
      where,
      include: [
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });
    
    return conflictingAppointments;
  } catch (error) {
    throw new Error(`Error getting conflicting appointments: ${error.message}`);
  }
};

/**
 * Get all appointments, with optional filters
 * @param {Object} filters - Filtering options
 * @returns {Promise<Array>} List of appointments
 */
export const getAllAppointments = async (filters = {}) => {
  try {
    const where = {};
    if (filters.is_active !== undefined) where.is_active = filters.is_active;
    if (filters.is_deleted !== undefined) where.is_deleted = filters.is_deleted;
    if (filters.clinic_id !== undefined) where.clinic_id = filters.clinic_id;
    if (filters.patient_id !== undefined) where.patient_id = filters.patient_id;
    if (filters.doctor_id !== undefined) where.doctor_id = filters.doctor_id;
    if (filters.status !== undefined) where.status = filters.status;
    if (filters.source !== undefined) where.source = filters.source;
    if (filters.appointment_date !== undefined) where.appointment_date = filters.appointment_date;
    
    const appointments = await Appointment.findAll({ 
      where,
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name']
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
        },
        {
          model: Doctor,
          attributes: ['id', 'doctor_name', 'specialization', 'doctor_email']
        }
      ]
    });
    return appointments;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get an appointment by its ID
 * @param {number} id - Appointment ID
 * @returns {Promise<Object|null>} The appointment or null if not found
 */
export const getAppointmentById = async (id) => {
  try {
    const appointment = await Appointment.findByPk(id, {
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name']
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
        },
        {
          model: Doctor,
          attributes: ['id', 'doctor_name', 'specialization', 'doctor_email']
        }
      ]
    });
    return appointment;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Create a new appointment
 * @param {Object} appointmentData - Data for new appointment
 * @returns {Promise<Object>} The created appointment
 */
export const createAppointment = async (appointmentData) => {
  try {
    // Check if time slot is available
    const isAvailable = await isTimeSlotAvailable(appointmentData);
    
    if (!isAvailable) {
      const conflicts = await getConflictingAppointments(appointmentData);
      throw new Error(`Time slot is not available. Conflicting appointment found: ${conflicts[0].id}`);
    }
    
    const newAppointment = await Appointment.create(appointmentData);
    return newAppointment;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Update an appointment by its ID
 * @param {number} id - Appointment ID
 * @param {Object} appointmentData - Data to update
 * @returns {Promise<Object|null>} The updated appointment or null if not found
 */
export const updateAppointment = async (id, appointmentData) => {
  try {
    // Check if time slot is available (excluding current appointment)
    if (appointmentData.doctor_id || appointmentData.appointment_date || appointmentData.appointment_time) {
      const isAvailable = await isTimeSlotAvailable(appointmentData, id);
      
      if (!isAvailable) {
        const conflicts = await getConflictingAppointments(appointmentData, id);
        throw new Error(`Time slot is not available. Conflicting appointment found: ${conflicts[0].id}`);
      }
    }
    
    await Appointment.update(appointmentData, { where: { id } });
    const updatedAppointment = await Appointment.findByPk(id);
    return updatedAppointment;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Soft delete an appointment by its ID (sets is_deleted to true)
 * @param {number} id - Appointment ID
 * @returns {Promise<Object|null>} The deleted appointment or null if not found
 */
export const deleteAppointment = async (id) => {
  try {
    await Appointment.update({ is_deleted: true }, { where: { id } });
    const deletedAppointment = await Appointment.findByPk(id);
    return deletedAppointment;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get appointments by date range
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {Object} filters - Additional filters
 * @returns {Promise<Array>} List of appointments in date range
 */
export const getAppointmentsByDateRange = async (startDate, endDate, filters = {}) => {
  try {
    const where = {
      appointment_date: {
        [Op.between]: [startDate, endDate]
      },
      ...filters
    };
    
    const appointments = await Appointment.findAll({ 
      where,
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name']
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
        },
        {
          model: Doctor,
          attributes: ['id', 'doctor_name', 'specialization', 'doctor_email']
        }
      ]
    });
    return appointments;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Get appointments by status
 * @param {string} status - Appointment status
 * @param {Object} filters - Additional filters
 * @returns {Promise<Array>} List of appointments with specified status
 */
export const getAppointmentsByStatus = async (status, filters = {}) => {
  try {
    const where = {
      status,
      ...filters
    };
    
    const appointments = await Appointment.findAll({ 
      where,
      include: [
        {
          model: Clinic,
          attributes: ['id', 'clinic_name']
        },
        {
          model: Patient,
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
        },
        {
          model: Doctor,
          attributes: ['id', 'doctor_name', 'specialization', 'doctor_email']
        }
      ]
    });
    return appointments;
  } catch (error) {
    throw new Error(error.message);
  }
}; 