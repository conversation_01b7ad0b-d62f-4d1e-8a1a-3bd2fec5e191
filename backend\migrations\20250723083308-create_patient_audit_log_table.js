
import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('patient_audit_logs', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    record_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
    },
    action: {
      type: Sequelize.STRING,
      allowNull: false,
    },
    user_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    old_value: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    new_value: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    description: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    error_details: {
      type: Sequelize.TEXT,
      allowNull: true,
    },
    timestamp: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('patient_audit_logs');
};
