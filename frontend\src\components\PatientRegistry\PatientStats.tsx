// PatientStats.tsx
// Renders a grid of StatCard components to display patient-related statistics in the Patient Registry.
// Shows total patients, active patients, new this month, and reactivation due.

import React from 'react';
import StatCard from '@/components/CommonComponents/StatCard';
import { Users, Calendar, Phone } from 'lucide-react';
import {
  LABEL_TOTAL_PATIENTS,
  LABEL_ACTIVE_PATIENTS,
  LABEL_NEW_THIS_MONTH,
  LABEL_REACTIVATION_DUE
} from '@/Constants/PatientRegistry';

/**
 * Props for the PatientStats component
 * @property total - Total number of patients
 * @property active - Number of active patients
 * @property newThisMonth - Number of new patients this month
 * @property reactivationDue - Number of patients due for reactivation
 */
interface PatientStatsProps {
  total: number;
  active: number;
  newThisMonth: number;
  reactivationDue: number;
}

/**
 * Renders a grid of StatCard components for patient statistics.
 * Used in the Patient Registry page.
 */
const PatientStats: React.FC<PatientStatsProps> = ({ total, active, newThisMonth, reactivationDue }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full">
    <StatCard
      title={LABEL_TOTAL_PATIENTS}
      value={total}
      icon={<Users className="h-8 w-8 text-blue-600" />}
    />
    <StatCard
      title={LABEL_ACTIVE_PATIENTS}
      value={active}
      icon={<Users className="h-8 w-8 text-green-600" />}
    />
    <StatCard
      title={LABEL_NEW_THIS_MONTH}
      value={newThisMonth}
      icon={<Calendar className="h-8 w-8 text-purple-600" />}
    />
    <StatCard
      title={LABEL_REACTIVATION_DUE}
      value={reactivationDue}
      icon={<Phone className="h-8 w-8 text-orange-600" />}
    />
  </div>
);

export default PatientStats; 