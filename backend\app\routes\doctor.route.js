/**
 * Doctor Routes: Express router for doctor CRUD endpoints.
 * Applies validation and authentication middleware.
 */
import express from 'express';
import { validate } from '../middleware/validate.middleware.js';
import { createDoctorSchema, updateDoctorSchema } from '../validators/doctor.validator.js';
import * as doctor<PERSON>ontroller from '../controllers/doctor.controller.js';
import { verifyToken } from '../middleware/auth.middleware.js';

const router = express.Router();

// Create a new doctor
// POST /v1/doctor/create
router.post(
  '/create',
  verifyToken,
  validate(createDoctorSchema),
  doctorController.createDoctor
);

// Get all doctors
// GET /v1/doctor/list
router.get(
  '/list',
  verifyToken,
  doctorController.getAllDoctors
);

// Get doctor by ID
// GET /v1/doctor/:id
router.get(
  '/:id',
  verifyToken,
  doctorController.getDoctorById
);

// Update doctor by ID
// PUT /v1/doctor/:id
router.put(
  '/:id',
  verifyToken,
  validate(updateDoctorSchema),
  doctorController.updateDoctor
);

// Soft delete doctor by ID
// DELETE /v1/doctor/:id
router.delete(
  '/:id',
  verifyToken,
  doctorController.deleteDoctor
);

export default router; 