/* PhoneInput.module.css */
/* Custom styling for react-phone-input-2 component */

.phoneInputContainer {
  width: 100%;
}

.phoneInputContainer .form-control {
  width: 100% !important;
  height: 42px !important;
  padding: 8px 12px 8px 50px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.phoneInputContainer .form-control:focus {
  border-color: #3b82f6 !important;
  outline: 0 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.phoneInputContainer .flag-dropdown {
  border: 1px solid #d1d5db !important;
  border-right: none !important;
  border-radius: 6px 0 0 6px !important;
  background-color: #f9fafb !important;
}

.phoneInputContainer .flag-dropdown:hover {
  background-color: #f3f4f6 !important;
}

.phoneInputContainer .flag-dropdown.open {
  background-color: #f3f4f6 !important;
}

.phoneInputContainer .selected-flag {
  padding: 0 8px 0 12px !important;
  border-radius: 6px 0 0 6px !important;
}

.phoneInputContainer .selected-flag:hover {
  background-color: #f3f4f6 !important;
}

.phoneInputContainer .country-list {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  background-color: white !important;
  max-height: 200px !important;
}

.phoneInputContainer .country-list .country {
  padding: 8px 12px !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.phoneInputContainer .country-list .country:hover {
  background-color: #f9fafb !important;
}

.phoneInputContainer .country-list .country.highlight {
  background-color: #eff6ff !important;
}

.phoneInputContainer .search-box {
  padding: 8px 12px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background-color: #f9fafb !important;
}

.phoneInputContainer .search-box input {
  width: 100% !important;
  padding: 6px 8px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}

.phoneInputContainer .search-box input:focus {
  border-color: #3b82f6 !important;
  outline: 0 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

/* Error state styling */
.phoneInputContainer.error .form-control {
  border-color: #ef4444 !important;
}

.phoneInputContainer.error .flag-dropdown {
  border-color: #ef4444 !important;
}

/* Disabled state styling */
.phoneInputContainer.disabled .form-control {
  background-color: #f9fafb !important;
  color: #6b7280 !important;
  cursor: not-allowed !important;
}

.phoneInputContainer.disabled .flag-dropdown {
  background-color: #f3f4f6 !important;
  cursor: not-allowed !important;
}
