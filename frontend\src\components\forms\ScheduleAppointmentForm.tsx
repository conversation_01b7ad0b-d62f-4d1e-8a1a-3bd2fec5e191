import React, { useState, useEffect, use<PERSON>emo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FIELD_TYPES,
} from "@/Constants/CommonComponents";
import { useAppointment } from "@/hooks/useAppointment";
import { usePatients } from "@/hooks/usePatients";
import { useDoctor } from "@/hooks/useDoctor";
import { useClinic } from "@/hooks/useClinic";
import { useAuth } from "@/components/contexts/AuthContext";
import { appointmentFormConfig } from "@/components/CommonComponents/formConfigs";
import {
  SCHEDULE_APPOINTMENT_TITLE,
  SCHEDULE_APPOINTMENT_SUBMIT,
  SCHEDULE_APPOINTMENT_CANCEL,
} from "@/Constants/Appointment";
import { Appointment } from "@/hooks/useAppointment";

interface FormField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  defaultValue?: string;
  validation?: (value: string) => string | null;
  gridCols?: number;
  rows?: number;
}

interface FormData {
  [key: string]: string | string[] | Date;
}

interface Doctor {
  id: number;
  doctor_name: string;
  clinic_id: number;
}

interface Clinic {
  id: number;
  clinic_name: string;
}

interface ScheduleAppointmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Record<string, unknown>) => void;
  appointmentToEdit?: Appointment | null; // For editing existing appointments
}

const ScheduleAppointmentForm: React.FC<ScheduleAppointmentFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  appointmentToEdit,
}) => {
  const { createAppointment, updateAppointment } = useAppointment();
  const { patients, getAllPatients } = usePatients();
  const { getDoctors } = useDoctor();
  const { getClinics } = useClinic();
  const { user } = useAuth();

  const [formData, setFormData] = useState<FormData>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);

  const isEditing = !!appointmentToEdit;

  // Initialize form data when editing
  useEffect(() => {
    if (isEditing && appointmentToEdit) {
      const editData: FormData = {
        patient_id: String(appointmentToEdit.patient_id || ''),
        clinic_id: String(appointmentToEdit.clinic_id || ''),
        doctor_id: String(appointmentToEdit.doctor_id || ''),
        appointment_date: appointmentToEdit.appointment_date || '',
        appointment_time: appointmentToEdit.appointment_time ? 
          new Date(appointmentToEdit.appointment_time).toTimeString().slice(0, 5) : '',
        status: appointmentToEdit.status || '',
        source: appointmentToEdit.source || '',
      };
      setFormData(editData);
    } else {
      setFormData({});
    }
    setErrors({});
  }, [isEditing, appointmentToEdit]);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      await getAllPatients();
      const doctorsResponse = await getDoctors();
      const clinicsResponse = await getClinics();
      
      if (doctorsResponse.success) {
        setDoctors(doctorsResponse.data as Doctor[] || []);
      }
      if (clinicsResponse.success) {
        setClinics(clinicsResponse.data as Clinic[] || []);
      }
    };
    
    if (isOpen) {
      fetchData();
    }
  }, [isOpen, getAllPatients, getDoctors, getClinics]);

  // Memoize field options
  const fieldsWithOptions = useMemo(() => {
    return appointmentFormConfig.map((field) => {
      if (field.name === "patient_id") {
        return {
          ...field,
          options: patients.map((patient) => ({
            value: String(patient.id),
            label: `${patient.first_name} ${patient.last_name}`,
          })),
        };
      }
      if (field.name === "clinic_id") {
        return {
          ...field,
          options: clinics.map((clinic) => ({
            value: String(clinic.id),
            label: clinic.clinic_name,
          })),
        };
      }
      if (field.name === "doctor_id") {
        // Filter doctors based on selected clinic
        const selectedClinicId = formData.clinic_id as string;
        const filteredDoctors = selectedClinicId 
          ? doctors.filter((doctor) => String(doctor.clinic_id) === selectedClinicId)
          : doctors;
        
        return {
          ...field,
          options: filteredDoctors.map((doctor) => ({
            value: String(doctor.id),
            label: doctor.doctor_name,
          })),
        };
      }
      return field;
    });
  }, [patients, clinics, doctors, formData.clinic_id]);

  const validateField = (name: string, value: string): string | null => {
    const field = fieldsWithOptions.find((f) => f.name === name);
    if (!field) return null;

    if (field.required && (!value || value.trim() === "")) {
      return `${field.label} is required`;
    }

    // Check if field has validation function
    if ('validation' in field && typeof field.validation === 'function') {
      return field.validation(value);
    }

    return null;
  };

  const handleFieldChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    // Clear doctor selection when clinic changes
    if (name === "clinic_id") {
      setFormData((prev) => ({ ...prev, doctor_id: "" }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Validate all fields
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    fieldsWithOptions.forEach((field) => {
      const value = formData[field.name] as string || "";
      const error = validateField(field.name, value);
      if (error) {
        newErrors[field.name] = error;
        hasErrors = true;
      }
    });

    if (hasErrors) {
      setErrors(newErrors);
      setLoading(false);
      return;
    }

    try {
      // Get current user ID and username
      const currentUserId = user?.id ? parseInt(String(user.id)) : null;
      const currentUserName = user?.name ? String(user.name) : 'Unknown User';

      // Combine date and time into a proper datetime string
      const appointmentDate = formData.appointment_date as string;
      const appointmentTime = formData.appointment_time as string;
      
      // Create a proper datetime string in ISO format
      const combinedDateTime = appointmentDate && appointmentTime 
        ? `${appointmentDate}T${appointmentTime}:00`
        : null;

      if (!combinedDateTime) {
        setErrors({ appointment_date: "Date and time are required" });
        setLoading(false);
        return;
      }

      // Convert form data to appointment format
      const appointmentData = {
        patient_id: parseInt(formData.patient_id as string),
        clinic_id: parseInt(formData.clinic_id as string),
        doctor_id: parseInt(formData.doctor_id as string),
        appointment_date: formData.appointment_date as string,
        appointment_time: combinedDateTime,
        status: formData.status as string,
        source: formData.source as string,
        updated_by: currentUserId,
        is_deleted: false,
        is_active: true,
      };

      let result;
      if (isEditing && appointmentToEdit) {
        // Update existing appointment - preserve original created_by
        result = await updateAppointment(appointmentData, String(appointmentToEdit.id));
      } else {
        // Create new appointment - set created_by and updated_by
        const createData = {
          ...appointmentData,
          created_by: currentUserId,
          created_by_info: JSON.stringify({
            user_id: currentUserId,
            user_name: currentUserName,
            created_at: new Date().toISOString()
          }),
        };
        result = await createAppointment(createData);
      }
      
      if (result.success) {
        onSubmit(appointmentData);
        onClose();
        setFormData({});
        setErrors({});
      }
    } catch (error) {
      console.error("Error saving appointment:", error);
    } finally {
      setLoading(false);
    }
  };

  const renderField = (field: FormField) => {
    const value = formData[field.name] as string || "";
    const error = errors[field.name];

    switch (field.type) {
      case FIELD_TYPES.TEXT:
        return (
          <div key={field.name} className={`${field.gridCols === 2 ? "col-span-2" : ""}`}>
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="text"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}
            />
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      case FIELD_TYPES.EMAIL:
        return (
          <div key={field.name} className={`${field.gridCols === 2 ? "col-span-2" : ""}`}>
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="email"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}
            />
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      case FIELD_TYPES.DATE:
        return (
          <div key={field.name} className={`${field.gridCols === 2 ? "col-span-2" : ""}`}>
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="date"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}
            />
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      case "time":
        return (
          <div key={field.name} className={`${field.gridCols === 2 ? "col-span-2" : ""}`}>
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={field.name}
              type="time"
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}
            />
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      case FIELD_TYPES.SELECT:
        return (
          <div key={field.name} className={`${field.gridCols === 2 ? "col-span-2" : ""}`}>
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Select
              value={value}
              onValueChange={(newValue) => handleFieldChange(field.name, newValue)}
            >
              <SelectTrigger className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}>
                <SelectValue placeholder={field.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      case FIELD_TYPES.TEXTAREA:
        return (
          <div key={field.name} className="col-span-2">
            <Label htmlFor={field.name} className="text-sm font-medium text-gray-700">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={field.name}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              rows={field.rows || 3}
              className={`mt-1 border-gray-200 focus:border-blue-500 focus:ring-blue-500 ${error ? "border-red-500" : ""}`}
            />
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {isEditing ? "Edit Appointment" : SCHEDULE_APPOINTMENT_TITLE}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            {fieldsWithOptions.map(renderField)}
          </div>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              {SCHEDULE_APPOINTMENT_CANCEL}
            </Button>
            <Button
              type="submit"
              variant="main"
              disabled={loading}
            >
              {loading ? (isEditing ? "Updating..." : "Scheduling...") : (isEditing ? "Update Appointment" : SCHEDULE_APPOINTMENT_SUBMIT)}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ScheduleAppointmentForm; 