export const ESCALATION_TITLE = "Escalation Management";
export const ESCALATION_SUBTITLE = "Manage patient escalations and human interventions";
export const ESCALATION_CREATE_SUCCESS = "Escalation created successfully";
export const ESCALATION_CREATE_FAILED = "Failed to create escalation";
export const ESCALATION_UPDATE_SUCCESS = "Escalation updated successfully";
export const ESCALATION_UPDATE_FAILED = "Failed to update escalation";
export const ESCALATION_DELETE_SUCCESS = "Escalation deleted successfully";
export const ESCALATION_DELETE_FAILED = "Failed to delete escalation";
export const ESCALATION_FETCH_FAILED = "Failed to fetch escalation";
export const ESCALATIONS_FETCH_FAILED = "Failed to fetch escalations";

export const ESCALATION_STAT_TOTAL = "Total Escalations";
export const ESCALATION_STAT_OPEN = "Open Cases";
export const ESCALATION_STAT_RESOLVED = "Resolved";
export const ESCALATION_STAT_PENDING = "Pending";

export const ESCALATION_STATUS_OPEN = "open";
export const ESCALATION_STATUS_IN_PROGRESS = "in_progress";
export const ESCALATION_STATUS_RESOLVED = "resolved";
export const ESCALATION_STATUS_CLOSED = "closed";

export const ESCALATION_TAG_BUSY = "busy";
export const ESCALATION_TAG_TECHNICAL = "technical";
export const ESCALATION_TAG_COMPLAINT = "complaint";
export const ESCALATION_TAG_URGENT = "urgent";
export const ESCALATION_TAG_FOLLOW_UP = "follow_up";

export const ESCALATION_VIEW_TRANSCRIPT = "View Transcript";
export const ESCALATION_HIDE_TRANSCRIPT = "Hide Transcript";
export const ESCALATION_ASSIGN_TO = "Assign To";
export const ESCALATION_UPDATE_STATUS = "Update Status";
export const ESCALATION_ADD_NOTE = "Add Note";

export const ESCALATION_FILTER_ALL = "All Escalations";
export const ESCALATION_FILTER_OPEN = "Open";
export const ESCALATION_FILTER_IN_PROGRESS = "In Progress";
export const ESCALATION_FILTER_RESOLVED = "Resolved";

export const ESCALATION_DATA = [
  {
    id: 1,
    status: "open",
    created_at: "2025-08-01T04:40:38.013Z",
    updated_at: "2025-08-01T04:40:38.014Z",
    clinic_id: 1,
    patient_id: 1,
    summary: "AI rejection - Patient needs human assistance",
    tags: "busy",
    assignee_id: 2,
    created_by: 1,
    updated_by: 1,
    patient_name: "Jay Kapadiya",
    patient_phone: "+****************",
    assignee_name: "Dr. Sarah Wilson",
    clinic_name: "Downtown Family Clinic",
    transcript: [
      {
        role: "agent",
        message: "Hey, is this Jay Kapadiya?",
        time_in_call_secs: 0
      },
      {
        role: "user", 
        message: "Yes, this is Jay.",
        time_in_call_secs: 9
      },
      {
        role: "agent",
        message: "I hope you're doing well! Just reaching out to reconnect with some of our past patients. How have you been doing lately?",
        time_in_call_secs: 10
      },
      {
        role: "user",
        message: "Good.",
        time_in_call_secs: 16
      },
      {
        role: "agent",
        message: "I'm glad to hear that! We noticed it's been a while since your last appointment at Downtown Family Clinic. Would you like to schedule a follow-up consultation with Dr. Emily Carter?",
        time_in_call_secs: 18
      },
      {
        role: "user",
        message: "I'm doing it on the right now.",
        time_in_call_secs: 18
      },
      {
        role: "agent",
        message: "That's great! I just wanted to check in because we noticed it's been a while since your last appointment at Downtown Family Clinic. Would you be interested in scheduling a follow-up consultation with Dr. Emily Carter? We're also offering a special ten percent discount on your next appointment as a token of appreciation.",
        time_in_call_secs: 27
      },
      {
        role: "user",
        message: "Busy right now, can't talk now.",
        time_in_call_secs: 45
      },
      {
        role: "agent",
        message: "Totally understand! Is there a better time I can give you a quick callback to discuss everything?",
        time_in_call_secs: 49
      },
      {
        role: "user",
        message: "Maybe later.",
        time_in_call_secs: 61
      },
      {
        role: "agent",
        message: "If you're still there, please let me know a good time for a callback. I'm here to help!",
        time_in_call_secs: 62
      },
      {
        role: "user",
        message: "Good morning.",
        time_in_call_secs: 63
      },
      {
        role: "agent",
        message: "Good morning! Thank you for getting back to me. Would you like to let me know a convenient time for a callback, or should I check available slots for your follow-up appointment with Dr. Emily Carter?",
        time_in_call_secs: 65
      }
    ]
  },
  {
    id: 2,
    status: "in_progress",
    created_at: "2025-08-01T03:30:15.123Z",
    updated_at: "2025-08-01T04:15:22.456Z",
    clinic_id: 1,
    patient_id: 2,
    summary: "Technical issue during appointment booking",
    tags: "technical",
    assignee_id: 3,
    created_by: 1,
    updated_by: 1,
    patient_name: "Emma Davis",
    patient_phone: "+****************",
    assignee_name: "Dr. Michael Chen",
    clinic_name: "Downtown Family Clinic",
    transcript: [
      {
        role: "agent",
        message: "Hello, this is Downtown Family Clinic. How can I help you today?",
        time_in_call_secs: 0
      },
      {
        role: "user",
        message: "I'm trying to book an appointment but the system seems to be having issues.",
        time_in_call_secs: 5
      }
    ]
  },
  {
    id: 3,
    status: "resolved",
    created_at: "2025-07-31T14:20:10.789Z",
    updated_at: "2025-08-01T09:45:33.012Z",
    clinic_id: 1,
    patient_id: 3,
    summary: "Patient complaint about appointment scheduling",
    tags: "complaint",
    assignee_id: 2,
    created_by: 1,
    updated_by: 1,
    patient_name: "Robert Johnson",
    patient_phone: "+****************",
    assignee_name: "Dr. Sarah Wilson",
    clinic_name: "Downtown Family Clinic",
    transcript: [
      {
        role: "agent",
        message: "Thank you for calling Downtown Family Clinic. How may I assist you?",
        time_in_call_secs: 0
      },
      {
        role: "user",
        message: "I'm not happy with how my appointment was rescheduled without notice.",
        time_in_call_secs: 3
      }
    ]
  }
];

export const ESCALATION_STATUS_OPTIONS = [
  { value: "open", label: "Open" },
  { value: "in_progress", label: "In Progress" },
  { value: "resolved", label: "Resolved" },
  { value: "closed", label: "Closed" }
];

export const ESCALATION_TAG_OPTIONS = [
  { value: "busy", label: "Busy" },
  { value: "technical", label: "Technical Issue" },
  { value: "complaint", label: "Complaint" },
  { value: "urgent", label: "Urgent" },
  { value: "follow_up", label: "Follow Up" }
]; 