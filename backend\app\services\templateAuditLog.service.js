import { TemplateAuditLog } from '../models/index.js';

/**
 * Log an entry to the template audit log
 * @param {Object} logData - Audit log fields (action, record_id, user_id, old_value, new_value, description, error_details, timestamp)
 * @returns {Promise<Object>} The created audit log entry
 * <AUTHOR>
 */
export const logTemplateAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return TemplateAuditLog.create(logData);
}; 