import { DataTypes } from 'sequelize';

const ClinicModel = (sequelize) => {
  return sequelize.define('clinics', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    clinic_name: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    clinic_email: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    clinic_phonenumber: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    location: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    timezone: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    reactivation_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    batch_call_time: {
      type: DataTypes.TIME,
      allowNull: true
    },
    working_hours: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    working_days: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    sms_service: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether SMS notifications are enabled for this clinic'
    },
    email_service: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether email notifications are enabled for this clinic'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  }, {
    tableName: 'clinics',
    timestamps: false,
    underscored: true,
  });
};

export default ClinicModel; 