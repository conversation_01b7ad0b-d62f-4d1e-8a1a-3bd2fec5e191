import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Users,
  Phone,
  Clock,
  Calendar,
  Play,
  CheckCircle,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { ReactivationRecord } from "@/services/reactivationService";
import { REACTIVATION_STATS_TABLE } from "@/Constants/ReactivationProgram";

interface ReactivationStatsTableProps {
  reactivations: ReactivationRecord[];
  loading?: boolean;
}

const ReactivationStatsTable: React.FC<ReactivationStatsTableProps> = ({
  reactivations,
  loading = false,
}) => {
  const [expandedCampaigns, setExpandedCampaigns] = useState<Set<number>>(
    new Set()
  );

  const toggleCampaign = (campaignId: number) => {
    const newExpanded = new Set(expandedCampaigns);
    if (newExpanded.has(campaignId)) {
      newExpanded.delete(campaignId);
    } else {
      newExpanded.add(campaignId);
    }
    setExpandedCampaigns(newExpanded);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-700 border-green-200";
      case "in_progress":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "failed":
        return "bg-red-100 text-red-700 border-red-200";
      case "cancelled":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-yellow-100 text-yellow-700 border-yellow-200";
    }
  };

  if (loading) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">
              {REACTIVATION_STATS_TABLE.LOADING_MESSAGE}
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!reactivations || reactivations.length === 0) {
    return (
      <Card className="bg-white shadow-sm">
        <CardContent className="p-4">
          <div className="text-center py-6 text-gray-500">
            <Phone className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">
              {REACTIVATION_STATS_TABLE.NO_CAMPAIGNS_TITLE}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white shadow-sm">
      <CardContent className="p-0">
        <div className="divide-y divide-gray-100">
          {reactivations.map((campaign) => (
            <div
              key={campaign.id}
              className="hover:bg-gray-50 transition-colors"
            >
              {/* Campaign Row - Always Visible */}
              <div className="px-4 py-3">
                <div className="flex items-center justify-between">
                  {/* Left Side - Campaign Info */}
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    <button
                      onClick={() => toggleCampaign(campaign.id)}
                      className="p-1 hover:bg-gray-200 rounded transition-colors"
                    >
                      {expandedCampaigns.has(campaign.id) ? (
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      ) : (
                        <ChevronRight className="w-4 h-4 text-gray-500" />
                      )}
                    </button>

                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-gray-900 truncate">
                          {campaign.batch_name}
                        </h5>
                        <span
                          className={`px-2 py-0.5 text-xs font-medium rounded-full border ${getStatusColor(
                            campaign.status
                          )}`}
                        >
                          {campaign.status.replace("_", " ").toUpperCase()}
                        </span>
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-600">
                        <div className="flex items-center gap-1">
                          <Users className="w-3 h-3 text-blue-600" />
                          <span>
                            {campaign.patient_count}{" "}
                            {REACTIVATION_STATS_TABLE.PATIENTS_LABEL}
                          </span>
                        </div>

                        {campaign.reactivation_days && (
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3 text-purple-600" />
                            <span>
                              {campaign.reactivation_days}{" "}
                              {REACTIVATION_STATS_TABLE.DAYS_LABEL}
                            </span>
                          </div>
                        )}

                        {campaign.elevenlabs_batch_id && (
                          <div className="flex items-center gap-1">
                            <Phone className="w-3 h-3 text-green-600" />
                            <span className="font-mono">
                              {campaign.elevenlabs_batch_id.slice(0, 8)}...
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Right Side - Timestamps */}
                  <div className="flex items-center gap-3 text-xs text-gray-500 ml-4">
                    {campaign.scheduled_time && (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        <span>{REACTIVATION_STATS_TABLE.SCHEDULED_LABEL}</span>
                      </div>
                    )}
                    {campaign.executed_time && (
                      <div className="flex items-center gap-1">
                        <Play className="w-3 h-3" />
                        <span>{REACTIVATION_STATS_TABLE.STARTED_LABEL}</span>
                      </div>
                    )}
                    <span>
                      {new Date(campaign.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Expandable Details Section */}
              {expandedCampaigns.has(campaign.id) && (
                <div className="px-4 pb-3 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 w-full pt-2">
                    {/* Patient Information */}
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm">
                        <Users className="w-4 h-4 text-blue-600" />
                        {REACTIVATION_STATS_TABLE.PATIENT_INFORMATION}
                      </h6>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {REACTIVATION_STATS_TABLE.TOTAL_PATIENTS}
                          </span>
                          <span className="font-medium">
                            {campaign.patient_count}
                          </span>
                        </div>
                        {campaign.reactivation_days && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">
                              {REACTIVATION_STATS_TABLE.INACTIVE_PERIOD}
                            </span>
                            <span className="font-medium">
                              {campaign.reactivation_days} days
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Timeline */}
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm">
                        <Clock className="w-4 h-4 text-purple-600" />
                        {REACTIVATION_STATS_TABLE.TIMELINE}
                      </h6>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {REACTIVATION_STATS_TABLE.CREATED}
                          </span>
                          <span>
                            {new Date(campaign.created_at).toLocaleString()}
                          </span>
                        </div>
                        {campaign.scheduled_time && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">
                              {REACTIVATION_STATS_TABLE.SCHEDULED}
                            </span>
                            <span>
                              {new Date(
                                campaign.scheduled_time
                              ).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {campaign.executed_time && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">
                              {REACTIVATION_STATS_TABLE.STARTED}
                            </span>
                            <span>
                              {new Date(
                                campaign.executed_time
                              ).toLocaleString()}
                            </span>
                          </div>
                        )}
                        {campaign.completed_time && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">
                              {REACTIVATION_STATS_TABLE.COMPLETED}
                            </span>
                            <span>
                              {new Date(
                                campaign.completed_time
                              ).toLocaleString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Clinic & Creator Info */}
                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm">
                        <svg
                          className="w-4 h-4 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                          />
                        </svg>
                        {REACTIVATION_STATS_TABLE.CLINIC_INFO}
                      </h6>
                      <div className="text-xs">
                        {campaign.Clinic ? (
                          <p className="text-gray-700">
                            {campaign.Clinic.clinic_name}
                          </p>
                        ) : (
                          <p className="text-gray-500">
                            {REACTIVATION_STATS_TABLE.NO_CLINIC_INFO}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-3 shadow-sm">
                      <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm">
                        <svg
                          className="w-4 h-4 text-blue-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                        {REACTIVATION_STATS_TABLE.CREATED_BY}
                      </h6>
                      <div className="text-xs">
                        {campaign.Creator ? (
                          <>
                            <p className="text-gray-700 font-medium">
                              {campaign.Creator.name}
                            </p>
                            <p className="text-gray-500">
                              {campaign.Creator.email}
                            </p>
                          </>
                        ) : (
                          <p className="text-gray-500">
                            {REACTIVATION_STATS_TABLE.NO_CREATOR_INFO}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Call Results & Errors - Full Width */}
                    {(campaign.call_results || campaign.error_message) && (
                      <div className="col-span-1 md:col-span-4 space-y-2">
                        {campaign.call_results && (
                          <div className="bg-white rounded-lg p-3 shadow-sm">
                            <h6 className="font-medium text-gray-900 mb-2 flex items-center gap-2 text-sm">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                              {REACTIVATION_STATS_TABLE.CALL_RESULTS}
                            </h6>
                            <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                              {JSON.stringify(campaign.call_results, null, 2)}
                            </pre>
                          </div>
                        )}

                        {campaign.error_message && (
                          <div className="bg-white rounded-lg p-3 shadow-sm">
                            <h6 className="font-medium text-red-900 mb-2 flex items-center gap-2 text-sm">
                              <svg
                                className="w-4 h-4 text-red-600"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                              </svg>
                              {REACTIVATION_STATS_TABLE.ERROR_DETAILS}
                            </h6>
                            <p className="text-xs text-red-600">
                              {campaign.error_message}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ReactivationStatsTable;
