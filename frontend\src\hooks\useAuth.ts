import { useState } from "react";
import { apiRequest } from "../utils/axios.utils";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { ERROR_MESSAGES } from "../Constants/HooksAPI";

export function useAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [user, setUser] = useState<Record<string, unknown> | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [refreshTokenValue, setRefreshToken] = useState<string | null>(null);

  const getToken = () => accessToken;
  const checkSession = () => !!accessToken;

  const logout = async () => {
    setLoading(true);
    setError("");
    try {
      await apiRequest.post("/auth/logout", {});
    } catch {}
    setUser(null);
    setAccessToken(null);
    setRefreshToken(null);
    if (typeof window !== "undefined") {
      window.location.href = "/login";
    }
    setLoading(false);
  };

  const login = async (
    email: string,
    password: string,
    onSuccess?: () => void
  ): Promise<{
    success: boolean;
    user?: Record<string, unknown>;
    error?: string;
    accessToken?: string;
  }> => {
    setLoading(true);
    setError("");
    try {
      const response = (await apiRequest.post(
        "/auth/login",
        { email, password },
        { withCredentials: true }
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status && response.data) {
        const data = response.data as Record<string, unknown>;
        if (data?.user) {
          setUser(data.user as Record<string, unknown>);
          if (data.accessToken) {
            setAccessToken(data.accessToken as string);
          }
          if (data.refreshToken) {
            setRefreshToken(data.refreshToken as string);
          }
          if (onSuccess) onSuccess();
          compactSuccessMessage(response.message || "Login successful");
          return {
            success: true,
            user: data.user as Record<string, unknown>,
            accessToken: data.accessToken as string,
          };
        } else {
          compactErrorMessage(response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED);
          return { success: false, error: response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED };
        }
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED);
        return { success: false, error: response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const register = async (
    name: string,
    email: string,
    password: string,
    user_phonenumber: string,
    role: string = "user",
    clinic_id: string | null = null,
    onSuccess?: () => void
  ): Promise<{
    success: boolean;
    user?: Record<string, unknown>;
    error?: string;
    message?: string;
    data?: Record<string, unknown>;
  }> => {
    setLoading(true);
    setError("");
    if (!name || !email || !password || !user_phonenumber) {
      const errorMsg = ERROR_MESSAGES.INTERNAL_SERVER_ERROR;
      compactErrorMessage(errorMsg);
      setLoading(false);
      return { success: false, error: errorMsg };
    }
    try {
      const response = (await apiRequest.post(
        "/auth/signup",
        { name, email, password, role, user_phonenumber, clinic_id },
        { withCredentials: true }
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status && response.data) {
        const data = response.data as Record<string, unknown>;
        if (data?.newUser && data?.accessToken) {
          setUser(data.newUser as Record<string, unknown>);
          if (data.accessToken) {
            setAccessToken(data.accessToken as string);
          }
          if (data.refreshToken) {
            setRefreshToken(data.refreshToken as string);
          }
          if (onSuccess) onSuccess();
          compactSuccessMessage(response.message || "Registration successful");
          return {
            success: true,
            user: data.newUser as Record<string, unknown>,
            message: response.message,
            data,
          };
        } else {
          compactErrorMessage(response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
          return { success: false, error: response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
        }
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
        return { success: false, error: response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const refreshToken = async (): Promise<{
    success: boolean;
    accessToken?: string;
    error?: string;
  }> => {
    setLoading(true);
    setError("");
    try {
      const response = (await apiRequest.post(
        "/auth/refresh",
        {},
        { withCredentials: true }
      )) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status && response.data) {
        const data = response.data as Record<string, unknown>;
        if (data?.accessToken) {
          setAccessToken(data.accessToken as string);
          if (data.refreshToken) {
            setRefreshToken(data.refreshToken as string);
          }
          return { success: true, accessToken: data.accessToken as string };
        } else {
          compactErrorMessage(response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED);
          return { success: false, error: response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED };
        }
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED);
        return { success: false, error: response?.message || ERROR_MESSAGES.AUTHENTICATION_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const forgotPassword = async (
    email: string
  ): Promise<{
    success: boolean;
    message?: string;
    error?: string;
  }> => {
    setLoading(true);
    setError("");
    try {
      const response = (await apiRequest.post("/auth/forgot-password", {
        email,
      })) as { status: boolean; message?: string; data?: unknown };
      
      if (response && response.status) {
        compactSuccessMessage(response.message || "Password reset email sent successfully");
        return { success: true, message: response.message };
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
        return { success: false, error: response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Reset password with token
   * @param params: { resetToken: string, newPassword: string, confirmPassword: string }
   */
  const resetPasswordWithToken = async (params: {
    resetToken: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{
    success: boolean;
    error?: string;
  }> => {
    try {
      const response = (await apiRequest.post(
        `/auth/reset-password/${params.resetToken}`,
        {
          newPassword: params.newPassword,
          confirmPassword: params.confirmPassword,
        },
        { withCredentials: true }
      )) as { status: boolean; message?: string; data?: unknown };

      if (response && response.status) {
        compactSuccessMessage(response.message || "Password reset successfully");
        return { success: true };
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
        return { success: false, error: response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  /**
   * Change password for authenticated user
   * @param params: { currentPassword: string, newPassword: string, confirmPassword: string }
   */
  const changePassword = async (params: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{
    success: boolean;
    error?: string;
  }> => {
    try {
      const response = (await apiRequest.post(
        "/auth/change-password",
        {
          currentPassword: params.currentPassword,
          newPassword: params.newPassword,
          confirmPassword: params.confirmPassword,
        },
        { withCredentials: true }
      )) as { status: boolean; message?: string; data?: unknown };

      if (response && response.status) {
        compactSuccessMessage(response.message || "Password changed successfully");
        return { success: true };
      } else {
        compactErrorMessage(response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
        return { success: false, error: response?.message || ERROR_MESSAGES.INTERNAL_SERVER_ERROR };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, ERROR_MESSAGES.INTERNAL_SERVER_ERROR);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    }
  };

  return {
    loading,
    error,
    user,
    accessToken,
    refreshTokenValue,
    login,
    refreshToken,
    register,
    getToken,
    logout,
    checkSession,
    forgotPassword,
    resetPassword: resetPasswordWithToken,
    changePassword,
    setAccessToken,
    setRefreshToken,
    setUser,
  };
}
