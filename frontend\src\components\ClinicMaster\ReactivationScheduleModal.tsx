import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Clock, Users, CalendarDays } from "lucide-react";
import ConfirmAlertDialog from "@/components/CommonComponents/ConfirmAlertDialog";
import {
  REACTIVATION_SCHEDULE_CONFIRM_TITLE,
  REACTIVATION_SCHEDULE_CONFIRM_DESC,
  REACTIVATION_SCHEDULE_BATCH_CONFIRM_TITLE,
  REACTIVATION_SCHEDULE_BATCH_CONFIRM_DESC,
  REACTIVATION_SCHEDULE_SUCCESS_TITLE,
  REACTIVATION_SCHEDULE_BATCH_SUCCESS_TITLE,
  REACTIVATION_SCHEDULE_VALIDATION_TITLE,
  REACTIVATION_SCHEDULE_VALIDATION_DESC,
  REACTIVATION_SCHEDULE_DATE_REQUIRED_DESC,
  REACTIVATION_SCHEDULE_TIME_REQUIRED_DESC,
  REACTIVATION_SCHEDULE_FAILED_DESC,
  REACTIVATION_SCHEDULE_BATCH_FAILED_DESC,
  REACTIVATION_SCHEDULE_MODAL_TITLE,
  REACTIVATION_SCHEDULE_MODAL_SUBTITLE,
  REACTIVATION_SCHEDULE_TAB_SCHEDULE_AFTER,
  REACTIVATION_SCHEDULE_TAB_BATCH_CALL,
  REACTIVATION_SCHEDULE_AFTER_CONFIG_TITLE,
  REACTIVATION_SCHEDULE_AFTER_CONFIG_DESC,
  REACTIVATION_SCHEDULE_BATCH_CONFIG_TITLE,
  REACTIVATION_SCHEDULE_BATCH_CONFIG_DESC,
  REACTIVATION_SCHEDULE_LABEL_DAYS_AFTER,
  REACTIVATION_SCHEDULE_LABEL_TIME,
  REACTIVATION_SCHEDULE_LABEL_CALL_DATE,
  REACTIVATION_SCHEDULE_LABEL_CALL_TIME,
  REACTIVATION_SCHEDULE_SUMMARY_TITLE,
  REACTIVATION_SCHEDULE_AFTER_SUMMARY_TEXT,
  REACTIVATION_SCHEDULE_BATCH_SUMMARY_TEXT,
  REACTIVATION_SCHEDULE_AFTER_SUMMARY_DAYS,
  REACTIVATION_SCHEDULE_BATCH_SUMMARY_AT,
  REACTIVATION_SCHEDULE_BUTTON_CANCEL,
  REACTIVATION_SCHEDULE_BUTTON_SAVE_SCHEDULE,
  REACTIVATION_SCHEDULE_BUTTON_SAVE_BATCH,
  REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_CALLS,
  REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_BATCH,
  REACTIVATION_SCHEDULE_BUTTON_OK,
  REACTIVATION_SCHEDULE_PLACEHOLDER_SELECT_DAYS,
  REACTIVATION_SCHEDULE_NO_DATE_SELECTED,
  REACTIVATION_SCHEDULE_DAY_OPTIONS,
  REACTIVATION_SCHEDULE_AM_PM_OPTIONS,
} from "@/Constants/ClinicMaster";
import { successMessage, errorMessage, formatDateAsDDMMYYYY } from "@/utils/commonFunctions";
import { useClinic } from "@/hooks/useClinic";

interface ReactivationScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  clinicName: string;
  clinicId: number; // Add clinicId prop
}

const ReactivationScheduleModal: React.FC<ReactivationScheduleModalProps> = ({
  isOpen,
  onClose,
  clinicName,
  clinicId, // Add this prop
}) => {
  const [scheduleAfterDays, setScheduleAfterDays] = useState("7");
  const [scheduleAfterTime, setScheduleAfterTime] = useState("09:00");
  const [scheduleAfterAmPm, setScheduleAfterAmPm] = useState("AM");
  
  const [batchCallDate, setBatchCallDate] = useState("");
  const [batchCallTime, setBatchCallTime] = useState("09:00");
  // Remove batchCallAmPm state since we're using 24-hour format

  // Alert dialog states
  const [showScheduleConfirm, setShowScheduleConfirm] = useState(false);
  const [showBatchConfirm, setShowBatchConfirm] = useState(false);
  const [showValidationAlert, setShowValidationAlert] = useState(false);
  const [validationMessage, setValidationMessage] = useState("");

  const { updateReactivationDays, triggerBatchCall } = useClinic();
  
  const handleScheduleAfterSave = () => {
    // Validate required fields
    if (!scheduleAfterDays || !scheduleAfterTime) {
      setValidationMessage(REACTIVATION_SCHEDULE_VALIDATION_DESC);
      setShowValidationAlert(true);
      return;
    }
    
    // Show confirmation dialog
    setShowScheduleConfirm(true);
  };

  const handleBatchCallSave = () => {
    // Validate required fields
    if (!batchCallDate) {
      setValidationMessage(REACTIVATION_SCHEDULE_DATE_REQUIRED_DESC);
      setShowValidationAlert(true);
      return;
    }
    
    if (!batchCallTime) {
      setValidationMessage(REACTIVATION_SCHEDULE_TIME_REQUIRED_DESC);
      setShowValidationAlert(true);
      return;
    }
    
    // Show confirmation dialog
    setShowBatchConfirm(true);
  };

  const confirmScheduleAfter = async () => {
    try {
      // Convert days to number and time to HH:mm format
      const days = parseInt(scheduleAfterDays);
      const time = scheduleAfterTime;
      const amPm = scheduleAfterAmPm;
      
      // Convert 12-hour format to 24-hour format
      const timeParts = time.split(':').map(Number);
      let hours = timeParts[0];
      const minutes = timeParts[1];
      
      if (amPm === 'PM' && hours !== 12) hours += 12;
      if (amPm === 'AM' && hours === 12) hours = 0;
      
      const batchCallTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      
      const payload = {
        reactivation_days: days,
        batch_call_time: batchCallTime,
      };

      const result = await updateReactivationDays(clinicId, payload);
      
      if (result.success) {
        successMessage(REACTIVATION_SCHEDULE_SUCCESS_TITLE);
        onClose();
      } else {
        errorMessage(result.error || REACTIVATION_SCHEDULE_FAILED_DESC);
      }
    } catch {
      errorMessage(REACTIVATION_SCHEDULE_FAILED_DESC);
    }
  };

  const confirmBatchCall = async () => {
    try {
      // Convert date and time to Unix timestamp
      const selectedDate = new Date(batchCallDate);
      const timeValue: string = batchCallTime;
      
      // Parse time directly (already in 24-hour format)
      const [hours, minutes]: [number, number] = timeValue.split(':').map(Number) as [number, number];
      
      // Set the time on the selected date
      selectedDate.setHours(hours, minutes, 0, 0);
      
      // Convert to Unix timestamp (seconds)
      const unixTimestamp: number = Math.floor(selectedDate.getTime() / 1000);
      
      // Trigger the batch call API
      const result = await triggerBatchCall(clinicId, unixTimestamp);
      
      if (result.success) {
        successMessage(REACTIVATION_SCHEDULE_BATCH_SUCCESS_TITLE);
        onClose();
      } else {
        errorMessage(result.error || REACTIVATION_SCHEDULE_BATCH_FAILED_DESC);
      }
    } catch {
      errorMessage(REACTIVATION_SCHEDULE_BATCH_FAILED_DESC);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <div className="p-2 rounded-lg" style={{ background: 'linear-gradient(to right, #1e40af, #22c55e)' }}>
              <Calendar className="h-6 w-6 text-white" />
            </div>
            {REACTIVATION_SCHEDULE_MODAL_TITLE}
          </DialogTitle>
          <p className="text-gray-600 mt-2">
            {REACTIVATION_SCHEDULE_MODAL_SUBTITLE} <span className="font-semibold">{clinicName}</span>
          </p>
        </DialogHeader>

        <Tabs defaultValue="schedule-after" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gradient-to-r from-gray-100 to-gray-200 p-1 rounded-lg">
            <TabsTrigger value="schedule-after" className="flex items-center gap-2 data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200">
              <Clock className="h-4 w-4" />
              {REACTIVATION_SCHEDULE_TAB_SCHEDULE_AFTER}
            </TabsTrigger>
            <TabsTrigger value="batch-call" className="flex items-center gap-2 data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200">
              <Users className="h-4 w-4" />
              {REACTIVATION_SCHEDULE_TAB_BATCH_CALL}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="schedule-after" className="mt-6">
            <Card className="border-2 border-blue-100 bg-gradient-to-br from-blue-50 to-blue-100/50 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg -mt-2 -mx-2 mb-4">
                <CardTitle className="flex items-center gap-2">
                  <CalendarDays className="h-5 w-5" />
                  {REACTIVATION_SCHEDULE_AFTER_CONFIG_TITLE}
                </CardTitle>
                <p className="text-sm text-blue-100">
                  {REACTIVATION_SCHEDULE_AFTER_CONFIG_DESC}
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="schedule-days" className="text-sm font-medium text-gray-700">
                      {REACTIVATION_SCHEDULE_LABEL_DAYS_AFTER}
                    </Label>
                    <Select value={scheduleAfterDays} onValueChange={setScheduleAfterDays}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder={REACTIVATION_SCHEDULE_PLACEHOLDER_SELECT_DAYS} />
                      </SelectTrigger>
                      <SelectContent>
                        {REACTIVATION_SCHEDULE_DAY_OPTIONS.map((option: { value: string; label: string }) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="schedule-time" className="text-sm font-medium text-gray-700">
                      {REACTIVATION_SCHEDULE_LABEL_TIME}
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        type="time"
                        value={scheduleAfterTime}
                        onChange={(e) => setScheduleAfterTime(e.target.value)}
                        className="flex-1"
                      />
                      <Select value={scheduleAfterAmPm} onValueChange={setScheduleAfterAmPm}>
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {REACTIVATION_SCHEDULE_AM_PM_OPTIONS.map((option: { value: string; label: string }) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-100 to-blue-200 p-4 rounded-lg border border-blue-300">
                  <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {REACTIVATION_SCHEDULE_SUMMARY_TITLE}
                  </h4>
                  <p className="text-sm text-blue-700">
                    {REACTIVATION_SCHEDULE_AFTER_SUMMARY_TEXT} <span className="font-semibold">{scheduleAfterDays} {REACTIVATION_SCHEDULE_AFTER_SUMMARY_DAYS}</span> <span className="font-semibold">{scheduleAfterTime} {scheduleAfterAmPm}</span>
                  </p>
                </div>

                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={onClose} className="border-gray-300 hover:bg-gray-50">
                    {REACTIVATION_SCHEDULE_BUTTON_CANCEL}
                  </Button>
                  <Button 
                    onClick={handleScheduleAfterSave}
                    className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {REACTIVATION_SCHEDULE_BUTTON_SAVE_SCHEDULE}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="batch-call" className="mt-6">
            <Card className="border-2 border-green-100 bg-gradient-to-br from-green-50 to-green-100/50 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-t-lg -mt-2 -mx-2 mb-4">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {REACTIVATION_SCHEDULE_BATCH_CONFIG_TITLE}
                </CardTitle>
                <p className="text-sm text-green-100">
                  {REACTIVATION_SCHEDULE_BATCH_CONFIG_DESC}
                </p>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="batch-date" className="text-sm font-medium text-gray-700">
                      {REACTIVATION_SCHEDULE_LABEL_CALL_DATE}
                    </Label>
                    <Input
                      type="date"
                      value={batchCallDate}
                      onChange={(e) => setBatchCallDate(e.target.value)}
                      min={new Date().toLocaleDateString('en-CA')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="batch-time" className="text-sm font-medium text-gray-700">
                      {REACTIVATION_SCHEDULE_LABEL_CALL_TIME}
                    </Label>
                    <Input
                      type="time"
                      value={batchCallTime}
                      onChange={(e) => setBatchCallTime(e.target.value)}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-100 to-green-200 p-4 rounded-lg border border-green-300">
                  <h4 className="font-semibold text-green-800 mb-2 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    {REACTIVATION_SCHEDULE_SUMMARY_TITLE}
                  </h4>
                  <p className="text-sm text-green-700">
                    {REACTIVATION_SCHEDULE_BATCH_SUMMARY_TEXT} <span className="font-semibold">{formatDateAsDDMMYYYY(batchCallDate, REACTIVATION_SCHEDULE_NO_DATE_SELECTED)}</span> {REACTIVATION_SCHEDULE_BATCH_SUMMARY_AT} <span className="font-semibold">{batchCallTime}</span>
                  </p>
                </div>

                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={onClose} className="border-gray-300 hover:bg-gray-50">
                    {REACTIVATION_SCHEDULE_BUTTON_CANCEL}
                  </Button>
                  <Button 
                    onClick={handleBatchCallSave}
                    className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    {REACTIVATION_SCHEDULE_BUTTON_SAVE_BATCH}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>

      {/* Confirmation Alert Dialogs */}
      <ConfirmAlertDialog
        open={showScheduleConfirm}
        onOpenChange={setShowScheduleConfirm}
        title={REACTIVATION_SCHEDULE_CONFIRM_TITLE}
        description={REACTIVATION_SCHEDULE_CONFIRM_DESC}
        confirmText={REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_CALLS}
        cancelText={REACTIVATION_SCHEDULE_BUTTON_CANCEL}
        variant="info"
        onConfirm={confirmScheduleAfter}
      />

      <ConfirmAlertDialog
        open={showBatchConfirm}
        onOpenChange={setShowBatchConfirm}
        title={REACTIVATION_SCHEDULE_BATCH_CONFIRM_TITLE}
        description={REACTIVATION_SCHEDULE_BATCH_CONFIRM_DESC}
        confirmText={REACTIVATION_SCHEDULE_BUTTON_YES_SCHEDULE_BATCH}
        cancelText={REACTIVATION_SCHEDULE_BUTTON_CANCEL}
        variant="info"
        onConfirm={confirmBatchCall}
      />

      <ConfirmAlertDialog
        open={showValidationAlert}
        onOpenChange={setShowValidationAlert}
        title={REACTIVATION_SCHEDULE_VALIDATION_TITLE}
        description={validationMessage}
        confirmText={REACTIVATION_SCHEDULE_BUTTON_OK}
        cancelText=""
        variant="warning"
        onConfirm={() => setShowValidationAlert(false)}
      />
    </Dialog>
  );
};

export default ReactivationScheduleModal; 