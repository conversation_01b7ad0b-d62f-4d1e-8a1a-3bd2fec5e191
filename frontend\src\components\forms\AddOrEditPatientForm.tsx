// AddPatientForm.tsx
// Renders a modal form for adding a new patient using CommonForm and patient field config.

import React from "react";
import * as PatientConst from "@/Constants/PatientRegistry";
import { formConfigs } from "@/components/CommonComponents/formConfigs";
import { Clinic, Doctor } from "@/app/patient-registry/page";
import PatientForm from "./PatientForm";
import { parseDateForInput } from "@/utils/dateUtils";

// Import the FormField interface to ensure type compatibility
interface FormField {
  name: string;
  label: string;
  type: string;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ value: string; label: string }>;
  defaultValue?: string;
  validation?: (value: string) => string | null;
  gridCols?: number;
  isMulti?: boolean;
  rows?: number;
  country?: string;
  enableAreaCodes?: boolean;
}

const patientFields = (doctors: Doctor[], clinics: Clinic[]): FormField[] => {
  return (formConfigs.patient as FormField[]).map((field) => {
    if (field.name === "doctor_id") {
      return {
        ...field,
        options: doctors.map((doctor) => ({
          value: String(doctor.id),
          label: doctor.doctor_name,
        })),
      };
    }
    if (field.name === "clinic_id") {
      return {
        ...field,
        options: clinics.map((clinic) => ({
          value: String(clinic.id),
          label: clinic.clinic_name,
        })),
      };
    }
    return field;
  });
};

/**
 * Props for the AddPatientForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (patient data)
 */
interface AddPatientFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (patient: Record<string, unknown>) => void;
  initialValues?: Record<string, string>;
  doctors: Doctor[];
  clinics: Clinic[];
}

/**
 * Renders a modal form for adding a new patient.
 * Uses CommonForm with patient field configuration.
 */
const AddPatientForm: React.FC<AddPatientFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  doctors,
  clinics,
}) => {
  return (
    <PatientForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      fields={formConfigs.patient as FormField[]}
      doctors={doctors}
      clinics={clinics}
      title={PatientConst.ADD_PATIENT_TITLE}
      submitButtonText={PatientConst.ADD_PATIENT_SUBMIT}
    />
  );
};

export const EditPatientForm: React.FC<AddPatientFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialValues,
  doctors,
  clinics,
}) => {
  if (!initialValues) return null;
  const formattedInitialValues = Object.entries(initialValues).reduce(
    (acc, [key, value]) => {
      if (key === "dob") {
        // Use utility function to parse date for input
        acc[key] = parseDateForInput(value);
      } else {
        acc[key] = value;
      }
      return acc;
    },
    {} as Record<string, string | Date>
  );

  return (
    <PatientForm
      isOpen={isOpen}
      initialData={formattedInitialValues}
      onClose={onClose}
      onSubmit={onSubmit}
      submitButtonText={PatientConst.EDIT_PATIENT_SUBMIT}
      fields={patientFields(doctors, clinics)}
      title={PatientConst.EDIT_PATIENT_TITLE}
      clinics={clinics}
      doctors={doctors}
    />
  );
};

export default AddPatientForm;
