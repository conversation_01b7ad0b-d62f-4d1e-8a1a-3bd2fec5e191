import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Phone, Mail, Calendar, User } from 'lucide-react';
import { PatientWithUserDetails } from '@/hooks/usePatientManagement';
import { format } from 'date-fns';
import { PATIENT_LIST } from '@/Constants/ReactivationProgram';

interface PatientListProps {
  patients: PatientWithUserDetails[];
  loading: boolean;
  onPatientSelectionChange: (selectedIds: number[]) => void;
}

const PatientList: React.FC<PatientListProps> = ({ 
  patients, 
  loading, 
  onPatientSelectionChange 
}) => {
  const [selectedPatients, setSelectedPatients] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPatients, setFilteredPatients] = useState<PatientWithUserDetails[]>([]);
  
  // Use ref to store the callback to prevent infinite loops
  const callbackRef = useRef(onPatientSelectionChange);
  callbackRef.current = onPatientSelectionChange;

  useEffect(() => {
    const filtered = patients.filter(patient => {
      const searchLower = searchTerm.toLowerCase();
      return (
        patient.first_name.toLowerCase().includes(searchLower) ||
        patient.last_name.toLowerCase().includes(searchLower) ||
        patient.email?.toLowerCase().includes(searchLower) ||
        patient.phone_number?.includes(searchTerm)
      );
    });
    setFilteredPatients(filtered);
  }, [patients, searchTerm]);

  useEffect(() => {
    // Only call the callback if there are actual changes
    if (selectedPatients.size > 0 || callbackRef.current) {
      callbackRef.current(Array.from(selectedPatients));
    }
  }, [selectedPatients]);

  const handlePatientToggle = useCallback((patientId: number) => {
    setSelectedPatients(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(patientId)) {
        newSelected.delete(patientId);
      } else {
        newSelected.add(patientId);
      }
      return newSelected;
    });
  }, []);

  const handleSelectAll = useCallback(() => {
    setSelectedPatients(prev => {
      if (prev.size === filteredPatients.length) {
        return new Set();
      } else {
        return new Set(filteredPatients.map(p => p.id));
      }
    });
  }, [filteredPatients]);

  const formatDate = useCallback((dateString: string | null) => {
    if (!dateString) return PATIENT_LIST.NEVER_VISITED;
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return PATIENT_LIST.INVALID_DATE;
    }
  }, []);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{PATIENT_LIST.LOADING_TITLE}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (patients.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{PATIENT_LIST.TITLE}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            {PATIENT_LIST.NO_PATIENTS_MESSAGE}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{PATIENT_LIST.TITLE} ({patients.length})</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
            >
              {selectedPatients.size === filteredPatients.length ? PATIENT_LIST.DESELECT_ALL : PATIENT_LIST.SELECT_ALL}
            </Button>
            <Badge variant="secondary">
              {selectedPatients.size} {PATIENT_LIST.SELECTED_COUNT}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={PATIENT_LIST.SEARCH_PLACEHOLDER}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredPatients.map((patient) => (
            <div
              key={patient.id}
              className={`flex items-center space-x-3 p-3 rounded-lg border ${
                selectedPatients.has(patient.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Checkbox
                checked={selectedPatients.has(patient.id)}
                onCheckedChange={() => handlePatientToggle(patient.id)}
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">
                      {patient.first_name} {patient.last_name}
                    </h4>
                    {patient.tags && patient.tags.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {patient.tags[0]}
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    {PATIENT_LIST.ID_PREFIX} {patient.id}
                  </div>
                </div>
                
                <div className="mt-1 flex items-center space-x-4 text-sm text-gray-600">
                  {patient.email && (
                    <div className="flex items-center space-x-1">
                      <Mail className="h-3 w-3" />
                      <span>{patient.email}</span>
                    </div>
                  )}
                  {patient.phone_number && (
                    <div className="flex items-center space-x-1">
                      <Phone className="h-3 w-3" />
                      <span>{patient.phone_number}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{PATIENT_LIST.LAST_VISIT_PREFIX} {formatDate(patient.last_visit)}</span>
                  </div>
                </div>

                {patient.Creator && (
                  <div className="mt-1 flex items-center space-x-1 text-xs text-gray-500">
                    <User className="h-3 w-3" />
                    <span>{PATIENT_LIST.CREATED_BY_PREFIX} {patient.Creator.name}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default PatientList;
