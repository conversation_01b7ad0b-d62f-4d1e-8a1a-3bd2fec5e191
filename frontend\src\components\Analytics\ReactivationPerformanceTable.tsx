import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ANALYTICS_REACTIVATION_STATS,
  ANALYTICS_CHART_REACTIVATION_PERFORMANCE,
  ANALYTICS_CHART_LABEL_CAMPAIGN,
  ANALYTICS_CHART_LABEL_TOTAL_CALLS,
  ANALYTICS_CHART_LABEL_SUCCESSFUL,
  ANALYTICS_CHART_LABEL_SUCCESS_RATE,
  ANALYTICS_CHART_LABEL_PERFORMANCE,
} from "@/Constants/Analytics";

// Props for the ReactivationPerformanceTable component
interface ReactivationPerformanceTableProps {
  data?: typeof ANALYTICS_REACTIVATION_STATS; // Optional custom data, defaults to ANALYTICS_REACTIVATION_STATS
}

/**
 * Renders a table of reactivation campaign performance stats.
 * Each row shows campaign, calls, successful, rate, and a progress bar.
 */
const ReactivationPerformanceTable: React.FC<
  ReactivationPerformanceTableProps
> = ({ data = ANALYTICS_REACTIVATION_STATS }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{ANALYTICS_CHART_REACTIVATION_PERFORMANCE}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left p-4 font-medium text-gray-600">
                  {ANALYTICS_CHART_LABEL_CAMPAIGN}
                </th>
                <th className="text-left p-4 font-medium text-gray-600">
                  {ANALYTICS_CHART_LABEL_TOTAL_CALLS}
                </th>
                <th className="text-left p-4 font-medium text-gray-600">
                  {ANALYTICS_CHART_LABEL_SUCCESSFUL}
                </th>
                <th className="text-left p-4 font-medium text-gray-600">
                  {ANALYTICS_CHART_LABEL_SUCCESS_RATE}
                </th>
                <th className="text-left p-4 font-medium text-gray-600">
                  {ANALYTICS_CHART_LABEL_PERFORMANCE}
                </th>
              </tr>
            </thead>
            <tbody>
              {/* Render each campaign's stats as a table row */}
              {data.map((stat, index) => (
                <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                  <td className="p-4 font-medium text-gray-900">
                    {stat.campaign}
                  </td>
                  <td className="p-4 text-gray-600">{stat.calls}</td>
                  <td className="p-4 text-green-600 font-medium">
                    {stat.success}
                  </td>
                  <td className="p-4">
                    <Badge variant="outline" className="border border-gray-200">{stat.rate}</Badge>
                  </td>
                  <td className="p-4">
                    {/* Progress bar for campaign performance */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: stat.rate }}
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReactivationPerformanceTable;
