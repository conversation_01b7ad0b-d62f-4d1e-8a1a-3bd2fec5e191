import Sequelize from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable({ tableName: 'appointments'}, {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    clinic_id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: { tableName: 'clinics' },
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    patient_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: { tableName: 'patients' },
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    doctor_id: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: { tableName: 'doctor' },
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    appointment_date: {
      type: Sequelize.DATEONLY,
      allowNull: false,
    },
    appointment_time: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
    },
    status: {
      type: Sequelize.TEXT,
      allowNull: true,
      validate: {
        isIn: [['booked', 'cancelled', 'rescheduled', 'no-show']],
      },
    },
    source: {
      type: Sequelize.TEXT,
      allowNull: true,
      defaultValue: 'user',
      validate: {
        isIn: [['ai-agent', 'user', 'manual']],
      },
    },
    created_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    updated_at: {
      type: 'TIMESTAMPTZ',
      allowNull: false,
      defaultValue: Sequelize.literal('NOW()'),
    },
    created_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    updated_by: {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    },
    is_deleted: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    is_active: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
  });
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable({ tableName: 'appointments' });
}; 