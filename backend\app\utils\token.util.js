
import { Token } from '../models/index.js';
import logger from '../config/logger.config.js';
import * as loggerMessages from '../utils/log_messages.utils.js';
import { Op } from 'sequelize';
import crypto from 'crypto';
import * as constants from '../utils/constants.utils.js';


function generateSecureToken(length = constants.TOKEN_BYTE_LENGTH || 64) {
  return crypto.randomBytes(length).toString('hex');
}


// --- REFRESH TOKEN LOGIC ---
export async function createRefreshToken({ userId, expiresAt }) {
  const refreshToken = generateSecureToken(constants.REFRESH_TOKEN_BYTE_LENGTH || 48);
  const token = await Token.create({
    userId,
    refreshToken,
    refreshTokenExpiresAt: expiresAt,
    revoked: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return token;
}

export async function findValidRefreshToken(token) {
  return await Token.findOne({
    where: {
      refreshToken: token,
      revoked: false,
      refreshTokenExpiresAt: { [Op.gt]: new Date() },
    },
  });
}

export async function rotateRefreshToken(oldToken) {
  const tokenDoc = await findValidRefreshToken(oldToken);
  if (!tokenDoc) return null;
  await revokeRefreshToken(oldToken);
  const expiresAt = new Date(Date.now() + (constants.REFRESH_TOKEN_EXPIRY_MS || 7 * 24 * 60 * 60 * 1000));
  const newTokenDoc = await createRefreshToken({ userId: tokenDoc.userId, expiresAt });
  return newTokenDoc;
}

export async function revokeRefreshToken(token) {
  await Token.update(
    { revoked: true, updatedAt: new Date() },
    { where: { refreshToken: token } }
  );
}

export async function revokeAllRefreshTokensForUser(userId) {
  await Token.update(
    { revoked: true, updatedAt: new Date() },
    { where: { userId, refreshToken: { [Op.not]: null } } }
  );
}

export async function deleteRefreshToken(token) {
  await Token.destroy({ where: { refreshToken: token } });
}

// --- RESET TOKEN LOGIC ---
export async function createResetToken({ userId, expiresAt }) {
  const resetToken = generateSecureToken(constants.RESET_TOKEN_BYTE_LENGTH || 48);
  const token = await Token.create({
    userId,
    resetToken,
    resetTokenExpiresAt: expiresAt,
    used: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return token;
}

export async function findValidResetToken(token) {
  return await Token.findOne({
    where: {
      resetToken: token,
      used: false,
      resetTokenExpiresAt: { [Op.gt]: new Date() },
    },
  });
}

export async function markResetTokenUsed(token) {
  await Token.update(
    { used: true, updatedAt: new Date() },
    { where: { resetToken: token } }
  );
} 