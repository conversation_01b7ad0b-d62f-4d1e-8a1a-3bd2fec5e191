// AddClinicForm.tsx
// Renders a modal form for adding a new clinic using CommonForm and clinic field config.

import React from "react";
import CommonForm from "@/components/CommonComponents/CommonForm";
import { formConfigs } from "@/components/CommonComponents/formConfigs";

/**
 * Props for the AddClinicForm component
 * @property isOpen - Whether the form dialog is open
 * @property onClose - Handler to close the dialog
 * @property onSubmit - Handler for form submission (clinic data)
 */
interface AddClinicFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (clinic: Record<string, unknown>) => void;
  initialData?: Record<string, string | boolean>;
  submitButtonText?: string;
  title?: string;
}

/**
 * Renders a modal form for adding a new clinic.
 * Uses CommonForm with clinic field configuration.
 */
export const AddClinicForm: React.FC<AddClinicFormProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData = {},
  submitButtonText,
  title,
}) => {
  return (
    <CommonForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={onSubmit}
      formType="clinic"
      fields={formConfigs.clinic}
      initialData={initialData}
      submitButtonText={submitButtonText}
      title={title}
    />
  );
};

