import { useState, useCallback } from "react";
import { compactSuccessMessage, compactErrorMessage, extractErrorMessage } from "@/utils/commonFunctions";
import { apiRequest } from "@/utils/axios.utils";
import * as AICallLogs from "@/Constants/AICallLogs";

export interface AICallLog {
  id: number;
  direction: "inbound" | "outbound";
  call_time_date: string;
  call_duration: number;
  call_status: "successful" | "failed" | "in_progress";
  conversation_summary: string;
  transcript?: string[]; // Array of transcript messages
  call_summary_title: string;
  clinic_id: number;
  patient_id?: number;
  phone_number?: string;
  created_at: string;
  updated_at: string;
  created_by?: number;
  updated_by?: number;
  is_deleted: boolean;
  is_active: boolean;
  clinic?: {
    id: number;
    clinic_name: string;
    location: string;
  };
  patient?: {
    id: number;
    first_name: string;
    last_name: string;
    phone_number: string;
  };
  Creator?: {
    id?: number;
    name?: string;
    email?: string;
  } | null;
}

export interface CreateAICallLogData {
  direction: "inbound" | "outbound";
  call_time_date: string;
  call_duration: number;
  call_status: "successful" | "failed" | "in_progress";
  conversation_summary: string;
  call_summary_title: string;
  clinic_id: number;
  phone_number?: string;
  patient_id?: number;
}

export function useAICallLog() {
  const [aiCallLogs, setAICallLogs] = useState<AICallLog[]>([]);
  const [aiCallLog, setAICallLog] = useState<AICallLog | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_BASE = "/ai-call-log";
  const GET_AI_CALL_LOGS_URL = `${API_BASE}/list`;

  const getAllAICallLogs = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(GET_AI_CALL_LOGS_URL)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setAICallLogs(response.data as AICallLog[]);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED || "Failed to fetch AI call logs");
        return { success: false, error: response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED || "Failed to fetch AI call logs" };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED || "Failed to fetch AI call logs");
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, [GET_AI_CALL_LOGS_URL]);

  const getAICallLogById = useCallback(async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.get(`${API_BASE}/${id}`)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setAICallLog(response.data as AICallLog);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED);
        return { success: false, error: response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AICallLogs.MESSAGES?.AI_CALL_LOG_FETCH_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  const createAICallLog = useCallback(async (data: CreateAICallLogData) => {
    setLoading(true);
    setError(null);
    try {
      const response = (await apiRequest.post(`${API_BASE}/create`, data)) as { status: boolean; message?: string; data?: unknown };
      if (response && response.status) {
        setAICallLogs((prev) => [...prev, response.data as AICallLog]);
        compactSuccessMessage(response.message || AICallLogs.MESSAGES?.AI_CALL_LOG_CREATED_SUCCESSFULLY);
        return { success: true, data: response.data };
      } else {
        compactErrorMessage(response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_CREATE_FAILED);
        return { success: false, error: response?.message || AICallLogs.MESSAGES?.AI_CALL_LOG_CREATE_FAILED };
      }
    } catch (error: unknown) {
      const errorMsg = extractErrorMessage(error, AICallLogs.MESSAGES?.AI_CALL_LOG_CREATE_FAILED);
      compactErrorMessage(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    aiCallLogs,
    aiCallLog,
    loading,
    error,
    setLoading,
    setError,
    getAllAICallLogs,
    getAICallLogById,
    createAICallLog,
  };
} 