
## Database Migration & Seeding

### Migrate
- Development (default):
  ```sh
  npm run migrate
  ```
- Specific environment:
  ```sh
  npm run migrate --env=test
  npm run migrate --env=production
  ```

### Seed
- Development (default):
  ```sh
  npm run seed
  ```
- Specific environment:
  ```sh
  npm run seed --env=test
  npm run seed --env=production
  ```

## Backend Setup & Start

Install dependencies:
```sh
npm i
```

Start the server:
```sh
npm run server
```
// ...existing content...
