import { UserAuditLog } from '../models/index.js';
import { ClinicAuditLog } from '../models/index.js';
import { DoctorAuditLog } from '../models/index.js';

/**
 * Log an entry to the user audit log
 * @param {Object} logData - Audit log fields (action, record_id, user_id, old_value, new_value, description, error_details, timestamp)
 * @returns {Promise<Object>} The created audit log entry
 */
export const logUserAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return UserAuditLog.create(logData);
};

export const logClinicAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return ClinicAuditLog.create(logData);
};

export const logDoctorAudit = async (logData) => {
  if (!logData.timestamp) logData.timestamp = new Date();
  return DoctorAuditLog.create(logData);
};
